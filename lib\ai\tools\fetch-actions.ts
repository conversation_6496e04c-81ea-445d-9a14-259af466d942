import { tool } from 'ai';
import { z } from 'zod';
import { OpenAIToolSet } from "composio-core";
import { getConnectionsByUserId } from '@/lib/db/queries';
import { auth } from '@/app/(auth)/auth';

/**
 * Generate contextual parameter examples based on action and parameter name
 */
function generateParameterExamples(actionName: string, paramName: string, _paramType: string): string | null {
  const action = actionName.toLowerCase();
  const param = paramName.toLowerCase();

  // GitHub parameter examples
  if (action.includes('github')) {
    if (param === 'owner') {
      return "'authenticated' (for your repos), 'octocat' (for user repos), 'microsoft' (for org repos)";
    }
    if (param === 'repo') {
      return "'my-project', 'Hello-World', 'react'";
    }
    if (param === 'branch') {
      return "'main', 'master', 'develop', 'feature/new-feature'";
    }
    if (param === 'per_page') {
      return "30 (default), 50, 100 (max)";
    }
    if (param === 'page') {
      return "1 (first page), 2, 3...";
    }
  }

  // Gmail parameter examples
  if (action.includes('gmail')) {
    if (param === 'q') {
      return "'from:<EMAIL>', 'subject:meeting', 'is:unread'";
    }
    if (param === 'maxresults') {
      return "10, 25, 50 (reasonable limits)";
    }
    if (param === 'labelids') {
      return "'INBOX', 'SENT', 'DRAFT'";
    }
  }

  // Calendar parameter examples
  if (action.includes('calendar')) {
    if (param === 'calendarid') {
      return "'primary' (main calendar), specific calendar ID";
    }
    if (param === 'timemin' || param === 'timemax') {
      return "'2024-01-01T00:00:00Z' (ISO format)";
    }
  }

  // Notion parameter examples
  if (action.includes('notion')) {
    if (param === 'database_id') {
      return "Database UUID from Notion URL";
    }
    if (param === 'page_id') {
      return "Page UUID from Notion URL";
    }
  }

  // Jira parameter examples
  if (action.includes('jira')) {
    if (param === 'project') {
      return "'PROJ', 'DEV', 'SUPPORT' (project key)";
    }
    if (param === 'issuetype') {
      return "'Task', 'Bug', 'Story', 'Epic'";
    }
  }

  return null;
}

/**
 * Generate parameter inference tips for specific actions
 */
function generateParameterTips(actionName: string, appName: string): string | null {
  const action = actionName.toLowerCase();
  const app = appName.toLowerCase();

  if (app === 'github') {
    if (action.includes('list_repositories')) {
      return `• Use 'authenticated' as owner to list your own repositories
• For public repos, use the actual GitHub username as owner
• Add per_page parameter to control how many repos are returned`;
    }
    if (action.includes('list_branches')) {
      return `• Both owner and repo are required - extract from user context
• Use 'authenticated' if user refers to their own repository
• Repository name should match exactly (case-sensitive)`;
    }
    if (action.includes('create_issue')) {
      return `• Title and body are typically required
• Extract repository info from user request
• Use 'authenticated' as owner for user's repos`;
    }
  }

  if (app === 'gmail') {
    if (action.includes('list')) {
      return `• Use 'q' parameter for search queries (Gmail search syntax)
• Set reasonable maxResults (10-50) to avoid overwhelming responses
• Common queries: 'is:unread', 'from:email', 'subject:keyword'`;
    }
    if (action.includes('send')) {
      return `• 'to' field requires valid email addresses
• 'subject' and message body are essential for meaningful emails
• Use proper email formatting`;
    }
  }

  if (app === 'googlecalendar') {
    return `• Use 'primary' as calendarId for main calendar
• Time parameters need ISO format (use getCurrentTime tool)
• Set reasonable time ranges to avoid too many results`;
  }

  if (app === 'notion') {
    return `• Extract database/page IDs from Notion URLs
• IDs are long UUID strings (32 characters)
• Check user's connected Notion workspaces for context`;
  }

  if (app === 'jira') {
    return `• Project keys are usually 2-4 uppercase letters
• Issue types: Task, Bug, Story, Epic are common
• Summary field is typically required for issue creation`;
  }

  return null;
}

export const fetchActions = tool({
  description: 'Fetch relevant actions from connected services using semantic search. The search matches your query against API action descriptions in Composio\'s database. For best results, use API-centric terminology (operations, resource types, parameters) and avoid specific identifiers or personal information. Think in terms of what would appear in API documentation for the service.',
  parameters: z.object({
    description: z.string().describe('Describe the API action you need using terminology that would appear in API documentation. Include API operations, standard resource types, and common parameters, but avoid specific identifiers or exact values.'),
  }),
  execute: async ({ description }) => {
    try {
      // Get the current user session
      const session = await auth();

      if (!session || !session.user) {
        return {
          error: 'Not authenticated',
          message: 'User must be authenticated to use this tool',
        };
      }

      // Get user's active connections to determine which apps to search
      const userId = session.user.id as string;
      const userConnections = await getConnectionsByUserId(userId);
      const activeConnections = userConnections.filter(conn => conn.status === 'ACTIVE');

      // Extract the provider names (app names) from active connections
      const connectedApps = [...new Set(activeConnections.map(conn => conn.provider.toLowerCase()))];

      console.log(`User has active connections to: ${connectedApps.join(', ') || 'none'}`);

      // If user has no active connections, return empty results
      if (connectedApps.length === 0) {
        return {
          success: true,
          message: 'No connected apps available. Please connect apps in your profile settings to use this feature.',
          actions: [],
          connectedApps: []
        };
      }

      // Optimized: Skip memory retrieval in fetchActions to reduce latency
      // Tool experiences are now handled at the conversation level for better performance
      let preFetchGuidance = '';

      // Only add basic guidance without memory lookup
      if (connectedApps.includes('googlecalendar') && description.toLowerCase().includes('calendar')) {
        preFetchGuidance = `\n\n🔍 Quick Tip: For calendar operations, use getCurrentTime first to get the current date.\n`;
      } else if (connectedApps.includes('github') && description.toLowerCase().includes('repository')) {
        preFetchGuidance = `\n\n🔍 Quick Tip: Use 'authenticated' for your own repositories.\n`;
      }

      // Initialize the Composio toolset
      const toolset = new OpenAIToolSet();

      // Find relevant actions by use case
      console.log("Searching for actions with use case:", {
        useCase: description,
        apps: connectedApps,
        connected: connectedApps.join(', ')
      });

      // Step 1: First find relevant action enums by use case using the new API endpoint
      // Using fetch directly since the SDK method seems to be outdated
      const searchResponse = await fetch('https://find-actions.composio.dev/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          // Add authorization if needed
          'Authorization': `Bearer ${process.env.COMPOSIO_API_KEY}`
        },
        body: JSON.stringify({
          use_case: description,
          apps: connectedApps,
          min_actions_per_task: 10,
          max_actions_per_task: 20,
          limit: 10
        })
      });

      if (!searchResponse.ok) {
        throw new Error(`Failed to search for actions: ${searchResponse.status} ${searchResponse.statusText}`);
      }

      const searchResults = await searchResponse.json() as Array<{
        app: string;
        actions: string[];
        description: string;
        order: number;
      }>;
      console.log('Search results:', searchResults);

      // Extract action enums from the search results
      const relevantActionEnums = searchResults.flatMap(result => result.actions || []);

      console.log(`Found ${relevantActionEnums.length} relevant action enums`);

      // Check if we found any relevant action enums
      if (!relevantActionEnums || relevantActionEnums.length === 0) {
        return {
          success: true,
          message: `No relevant actions found for "${description}" in your connected apps (${connectedApps.join(', ')})`,
          actions: [],
          connectedApps
        };
      }

      // Step 2: Then get the full tool definitions for these actions
      const tools = await toolset.getTools({
        actions: relevantActionEnums
      });

      console.log(`Fetched ${tools.length} tool definitions`);

      if (!tools || tools.length === 0) {
        return {
          success: true,
          message: `No relevant actions found for "${description}" in your connected apps (${connectedApps.join(', ')})`,
          actions: [],
          connectedApps
        };
      }

      // Log the structure of the first tool to understand its format
      console.log("Tool structure example:", JSON.stringify(tools[0], null, 2).substring(0, 500));

      // Format the results for display - simpler approach without grouping
      let resultMessage = `Found ${tools.length} relevant actions for "${description}":\n\n`;

      // Enhanced parameter presentation for better AI understanding
      tools.forEach((tool: any) => {
        try {
          // Try to extract meaningful information from the tool
          const toolName = tool.name || tool.function?.name || tool.id || "Unknown action";
          const appName = tool.appName || tool.app || "github"; // Default to github if not specified

          // Make the action name very explicit for the AI to use
          resultMessage += `\n📋 **${toolName}** (${appName.toUpperCase()})\n`;

          // Add description if available
          if (tool.description || tool.function?.description) {
            resultMessage += `${tool.description || tool.function?.description}\n\n`;
          }

          // Enhanced parameter presentation
          if (tool.parameters || tool.function?.parameters) {
            const params = tool.parameters || tool.function?.parameters;
            if (params.properties) {
              const requiredParams = params.required || [];
              const optionalParams = Object.keys(params.properties).filter(key => !requiredParams.includes(key));

              // Required parameters section
              if (requiredParams.length > 0) {
                resultMessage += `🔴 **Required Parameters:**\n`;
                requiredParams.forEach((key: string) => {
                  const param = params.properties[key];
                  const type = param.type || 'string';
                  const description = param.description || 'No description available';
                  const examples = generateParameterExamples(toolName, key, type);

                  resultMessage += `  • **${key}** (${type}): ${description}\n`;
                  if (examples) {
                    resultMessage += `    💡 Examples: ${examples}\n`;
                  }
                });
                resultMessage += '\n';
              }

              // Optional parameters section
              if (optionalParams.length > 0) {
                resultMessage += `🟡 **Optional Parameters:**\n`;
                optionalParams.forEach((key: string) => {
                  const param = params.properties[key];
                  const type = param.type || 'string';
                  const description = param.description || 'No description available';
                  const defaultValue = param.default;

                  resultMessage += `  • **${key}** (${type}): ${description}`;
                  if (defaultValue !== undefined) {
                    resultMessage += ` (default: ${defaultValue})`;
                  }
                  resultMessage += '\n';
                });
                resultMessage += '\n';
              }

              // Add parameter inference tips
              const tips = generateParameterTips(toolName, appName);
              if (tips) {
                resultMessage += `💡 **Parameter Tips:**\n${tips}\n`;
              }
            }
          }

          resultMessage += `---\n`;
        } catch (err) {
          console.log("Error processing tool:", err);
        }
      });

      resultMessage += '\n';

      // Add pre-fetch guidance if available
      if (preFetchGuidance) {
        resultMessage += preFetchGuidance;
      }

      resultMessage += 'I can execute any of these actions for you using the executeAction tool. To execute an action, use the EXACT action name as shown above (e.g., GITHUB_LIST_REPOSITORIES) with the executeAction tool.';

      // Return the tools with metadata
      return {
        success: true,
        message: resultMessage,
        actions: tools,
        connectedApps,
        toolsFetched: tools.length,
        searchDescription: description,
        // Don't return the enhanced query to the UI
        // enhancedQuery: enhancedQuery !== description ? enhancedQuery : undefined
      };
    } catch (error) {
      console.error("Error in fetch-actions tool:", error);

      // Provide a more helpful error message for specific errors
      let errorMessage = 'Failed to fetch actions. Please try again later.';

      if (error instanceof Error) {
        if (error.message.includes('find-actions.composio.dev')) {
          errorMessage = 'Failed to connect to the Composio search service. This might be a temporary issue with the service.';
        } else if (error.message.includes('Failed to search for actions')) {
          errorMessage = `Search API error: ${error.message}. Please check your query and try again.`;
        }
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: errorMessage
      };
    }
  },
});
