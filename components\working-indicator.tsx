'use client';

import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

export const WorkingIndicator = () => {
  return (
    <motion.div
      data-testid="working-indicator"
      className="w-full mx-auto max-w-3xl px-4 group/message"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex">
        <div
          className={cn(
            'flex items-center gap-1.5 text-muted-foreground'
          )}
        >
          <span className="text-sm font-normal">Working</span>
          <div className="flex space-x-0.5 ml-0.5">
            <motion.div
              className="w-1.5 h-1.5 rounded-full bg-gray-400"
              animate={{ opacity: [0.4, 1, 0.4] }}
              transition={{ duration: 0.8, repeat: Infinity, delay: 0 }}
            />
            <motion.div
              className="w-1.5 h-1.5 rounded-full bg-gray-400"
              animate={{ opacity: [0.4, 1, 0.4] }}
              transition={{ duration: 0.8, repeat: Infinity, delay: 0.2 }}
            />
            <motion.div
              className="w-1.5 h-1.5 rounded-full bg-gray-400"
              animate={{ opacity: [0.4, 1, 0.4] }}
              transition={{ duration: 0.8, repeat: Infinity, delay: 0.4 }}
            />
          </div>
        </div>
      </div>
    </motion.div>
  );
};
