import { NextResponse } from 'next/server';
import { OpenAIToolSet } from "composio-core";
import { auth } from '@/app/(auth)/auth';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { connection } from '@/lib/db/schema';
import { getCustomIntegration } from '@/lib/composio/custom-integration';

// biome-ignore lint: Forbidden non-null assertion.
const client = postgres(process.env.POSTGRES_URL!);
const db = drizzle(client);

export async function POST(request: Request) {
  try {
    const session = await auth();

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { provider } = await request.json();

    if (!provider) {
      return NextResponse.json(
        { error: 'Provider is required' },
        { status: 400 }
      );
    }

    // Initialize the Composio toolset
    const toolset = new OpenAIToolSet();

    // Get the entity for the user - use the user's ID from the session
    const userId = session.user.id as string;
    const entity = await toolset.getEntity(userId);

    // Get the origin for the redirect URL
    const origin = new URL(request.url).origin;
    const callbackUrl = `${origin}/oauth-callback`;

    // Check if we have a manually created integration for this provider
    let connectionRequest;
    try {
      // Get the integration ID from environment variables
      const integrationId = await getCustomIntegration(provider);

      // According to Composio docs, we can use either:
      // 1. entity.initiateConnection with integrationId
      // 2. toolset.connectedAccounts.initiate with integrationId

      try {
        // First try the entity approach (recommended in docs)
        connectionRequest = await entity.initiateConnection({
          integrationId: integrationId,
          redirectUri: callbackUrl
        });
      } catch (entityError) {
        console.warn(`Error using entity.initiateConnection: ${entityError instanceof Error ? entityError.message : String(entityError)}`);

        // If that fails, try the direct client approach as fallback
        console.log(`Trying alternative approach with direct client API`);
        connectionRequest = await toolset.client.connectedAccounts.initiate({
          integrationId: integrationId,
          entityId: userId,
          redirectUri: callbackUrl
        });
      }

      console.log(`Successfully initiated connection for ${provider} using integration ID: ${integrationId}`);
    } catch (error) {
      console.warn(`Integration error for ${provider}:`, error);

      // Type guard for Error objects
      const errorMessage = error instanceof Error ? error.message : String(error);

      // If the error is "coming soon", return that message to the user
      if (errorMessage.includes("coming soon")) {
        return NextResponse.json({
          success: false,
          error: errorMessage,
          comingSoon: true
        }, { status: 404 });
      }

      // For other errors, return a generic error message
      return NextResponse.json({
        success: false,
        error: `Failed to connect to ${provider}. Please try again later.`
      }, { status: 500 });
    }

    // Return the redirect URL
    return NextResponse.json({
      success: true,
      redirectUrl: connectionRequest.redirectUrl,
    });
  } catch (error) {
    console.error(`Error initiating ${request.body} connection:`, error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}
