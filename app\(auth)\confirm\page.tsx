'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { toast } from '@/components/toast';
import { supabase } from '@/lib/supabase/client';
import { syncUserWithDatabase, markUserAsConfirmed } from '@/lib/supabase/user-sync';
import { confirmUserDirectly } from '@/lib/db/confirm-user';

// Loading component for Suspense
function ConfirmLoading() {
  return (
    <div className="flex h-dvh w-screen items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl gap-8 flex flex-col">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h3 className="text-xl font-semibold dark:text-zinc-50">Verifying your email...</h3>
          <div className="mt-4">
            <p className="text-sm text-gray-500 dark:text-zinc-400">
              Please wait while we verify your email address...
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

// Component that uses useSearchParams
function ConfirmContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [verificationStatus, setVerificationStatus] = useState<
    'verifying' | 'success' | 'error'
  >('verifying');

  useEffect(() => {
    const handleEmailConfirmation = async () => {
      try {
        // Check if we have a session already (user is already verified)
        const { data: { session } } = await supabase.auth.getSession();
        console.log('Current session:', session ? 'exists' : 'none');

        if (session) {
          // User is already verified and logged in
          console.log('User is already logged in:', session.user.email);
          setVerificationStatus('success');
          toast({
            type: 'success',
            description: 'Email verified successfully! You are now logged in.',
          });

          // Make sure the user is marked as confirmed
          if (session.user.email) {
            console.log('Ensuring user is marked as confirmed:', session.user.email);
            const confirmResult = await markUserAsConfirmed(session.user.email);
            console.log('Confirmation result (session):', confirmResult);

            if (!confirmResult.success) {
              console.error('Failed to mark user as confirmed (session), trying direct approach');
              // If the regular approach fails, try the direct approach
              const directResult = await confirmUserDirectly(session.user.email);
              console.log('Direct confirmation result (session):', directResult);
            }
          }

          // Redirect to home page after successful verification
          setTimeout(() => {
            router.push('/');
          }, 2000);
          return;
        }

        // If no session, check for access_token and refresh_token in the URL
        // This happens when Supabase redirects back after email verification
        const accessToken = searchParams.get('access_token');
        const refreshToken = searchParams.get('refresh_token');

        if (accessToken && refreshToken) {
          // Set the session with the tokens
          const { error } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken
          });

          if (error) {
            console.error('Session error:', error);
            setVerificationStatus('error');
            toast({
              type: 'error',
              description: 'Failed to verify your email. Please try again.',
            });
          } else {
            setVerificationStatus('success');
            toast({
              type: 'success',
              description: 'Email verified successfully! You are now logged in.',
            });

            // Get the user's email and sync with our database
            const { data: { user } } = await supabase.auth.getUser();
            console.log('User from supabase.auth.getUser():', user ? { id: user.id, email: user.email } : 'no user');
            if (user?.email) {
              console.log('Syncing user with database:', user.email);
              await syncUserWithDatabase(user.email);

              console.log('Marking user as confirmed:', user.email);
              const confirmResult = await markUserAsConfirmed(user.email);
              console.log('Confirmation result:', confirmResult);

              if (!confirmResult.success) {
                console.error('Failed to mark user as confirmed, trying direct approach');
                // If the regular approach fails, try the direct approach
                const directResult = await confirmUserDirectly(user.email);
                console.log('Direct confirmation result:', directResult);
              }
            }

            // Redirect to home page after successful verification
            setTimeout(() => {
              router.push('/');
            }, 2000);
          }
        } else {
          // No tokens in URL, check if there's a token for OTP verification
          const token = searchParams.get('token');

          if (token) {
            // Verify the email using the token
            const { error } = await supabase.auth.verifyOtp({
              token_hash: token,
              type: 'signup',
            });

            if (error) {
              console.error('Verification error:', error);
              setVerificationStatus('error');
              toast({
                type: 'error',
                description: 'Failed to verify your email. Please try again.',
              });
            } else {
              setVerificationStatus('success');
              toast({
                type: 'success',
                description: 'Email verified successfully! You are now logged in.',
              });

              // Get the user's email and sync with our database
              const { data: { user } } = await supabase.auth.getUser();
              console.log('User from supabase.auth.getUser() (OTP flow):', user ? { id: user.id, email: user.email } : 'no user');
              if (user?.email) {
                console.log('Syncing user with database (OTP flow):', user.email);
                await syncUserWithDatabase(user.email);

                console.log('Marking user as confirmed (OTP flow):', user.email);
                const confirmResult = await markUserAsConfirmed(user.email);
                console.log('Confirmation result (OTP flow):', confirmResult);

                if (!confirmResult.success) {
                  console.error('Failed to mark user as confirmed (OTP flow), trying direct approach');
                  // If the regular approach fails, try the direct approach
                  const directResult = await confirmUserDirectly(user.email);
                  console.log('Direct confirmation result (OTP flow):', directResult);
                }
              }

              // Redirect to home page after successful verification
              setTimeout(() => {
                router.push('/');
              }, 2000);
            }
          } else {
            setVerificationStatus('error');
            toast({
              type: 'error',
              description: 'Invalid verification link.',
            });
          }
        }
      } catch (error) {
        console.error('Verification error:', error);
        setVerificationStatus('error');
        toast({
          type: 'error',
          description: 'An error occurred during verification.',
        });
      }
    };

    handleEmailConfirmation();
  }, [searchParams, router]);

  return (
    <div className="flex h-dvh w-screen items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl gap-8 flex flex-col">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h3 className="text-xl font-semibold dark:text-zinc-50">
            {verificationStatus === 'verifying'
              ? 'Verifying your email...'
              : verificationStatus === 'success'
              ? 'Email Verified!'
              : 'Verification Failed'}
          </h3>

          <div className="mt-4">
            {verificationStatus === 'verifying' && (
              <p className="text-sm text-gray-500 dark:text-zinc-400">
                Please wait while we verify your email address...
              </p>
            )}

            {verificationStatus === 'success' && (
              <p className="text-sm text-gray-500 dark:text-zinc-400">
                Your email has been verified successfully. You are now logged in.
                <br />
                Redirecting you to the home page...
              </p>
            )}

            {verificationStatus === 'error' && (
              <div className="flex flex-col gap-4">
                <p className="text-sm text-gray-500 dark:text-zinc-400">
                  We couldn't verify your email. The link may have expired or is invalid.
                </p>
                <div className="flex flex-col gap-2">
                  <Link
                    href="/login"
                    className="w-full rounded-md bg-zinc-900 px-4 py-2 text-sm font-medium text-white hover:bg-zinc-800 dark:bg-zinc-100 dark:text-zinc-900 dark:hover:bg-zinc-200"
                  >
                    Back to Login
                  </Link>
                  <Link
                    href="/register"
                    className="w-full rounded-md bg-transparent px-4 py-2 text-sm font-medium text-zinc-900 hover:bg-zinc-100 dark:text-zinc-100 dark:hover:bg-zinc-800"
                  >
                    Sign Up Again
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Main page component with Suspense
export default function ConfirmPage() {
  return (
    <Suspense fallback={<ConfirmLoading />}>
      <ConfirmContent />
    </Suspense>
  );
}
