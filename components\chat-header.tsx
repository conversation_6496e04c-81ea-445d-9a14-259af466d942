'use client';

import { useRouter } from 'next/navigation';
import { useWindowSize } from 'usehooks-ts';
import Image from 'next/image';
import type { User } from 'next-auth';
import { signOut } from 'next-auth/react';
import { useTheme } from 'next-themes';
import { ChevronDown } from 'lucide-react';

import { ModelSelector } from '@/components/model-selector';
import { SidebarToggle } from '@/components/sidebar-toggle';
import { Button } from '@/components/ui/button';
import { NewChatIcon } from './icons';
import { useSidebar } from './ui/sidebar';
import { memo } from 'react';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { type VisibilityType, VisibilitySelector } from './visibility-selector';
import { ProfileSettingsModal } from './profile-settings-modal';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';


function PureChatHeader({
  chatId,
  selectedModelId,
  selectedVisibilityType,
  isReadonly,
  user,
}: {
  chatId: string;
  selectedModelId: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
  user: User | undefined;
}) {
  const router = useRouter();
  const { open } = useSidebar();
  const { setTheme, theme } = useTheme();

  const { width: windowWidth } = useWindowSize();

  return (
    <header className="flex sticky top-0 bg-background/80 backdrop-blur-sm dark:bg-transparent dark:backdrop-blur-sm py-1 items-center px-1 md:px-2 gap-2 justify-between z-10 kortex-content">
      <div className="flex items-center justify-center md:hidden">
        <SidebarToggle className="relative" />
      </div>

      {!open && (
        <div className="flex items-center gap-2">
          {/* New Chat Button for Mobile */}
          {windowWidth < 768 && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  className="px-2 h-fit border-0"
                  onClick={() => {
                    router.push('/');
                    router.refresh();
                  }}
                >
                  <NewChatIcon />
                  <span className="sr-only">New Chat</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>New Chat</TooltipContent>
            </Tooltip>
          )}

          {/* New Chat Button for Desktop */}
          {windowWidth >= 768 && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  className="px-2 h-fit border-0"
                  onClick={() => {
                    router.push('/');
                    router.refresh();
                  }}
                >
                  <NewChatIcon size={32}/>
                  <span className="sr-only">New Chat</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>New Chat</TooltipContent>
            </Tooltip>
          )}

          {/* Logo */}
          <div className="relative h-8 w-52 flex items-center">
            <Image
              src="/Logo.png"
              alt="Logo"
              fill
              className="object-contain object-left"
              priority
            />
          </div>
        </div>
      )}

      {(open && windowWidth < 768) && (
        <div className="flex-1 flex justify-start">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                className="px-2 h-fit border-0"
                onClick={() => {
                  router.push('/');
                  router.refresh();
                }}
              >
                <NewChatIcon />
                <span className="sr-only">New Chat</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>New Chat</TooltipContent>
          </Tooltip>
        </div>
      )}

      {/* Model selector hidden as requested */}

      {!isReadonly && (
        <div className="flex-1 flex justify-center">
          <VisibilitySelector
            chatId={chatId}
            selectedVisibilityType={selectedVisibilityType}
            className="order-1 md:order-2" /* Updated order from 3 to 2 since model selector is hidden */
          />
        </div>
      )}

      {user && windowWidth >= 768 && (
        <DropdownMenu>
          <DropdownMenuTrigger
            asChild
            className="data-[state=open]:bg-accent/20 data-[state=open]:border-primary/50 data-[state=open]:text-foreground"
          >
            <Button
              variant="outline"
              className="hidden md:flex py-1.5 px-2 h-fit md:h-[34px] order-3 bg-transparent hover:bg-accent/40 hover:border-primary/50 border-border/70 dark:border-border/40 dark:hover:bg-accent/30 hover:text-foreground"
            >
              <div className="flex items-center">
                <Image
                  src={`https://avatar.vercel.sh/${user.email}`}
                  alt={user.email ?? 'User Avatar'}
                  width={20}
                  height={20}
                  className="rounded-full mr-2"
                />
                <span className="truncate text-foreground">{user?.email}</span>
                <ChevronDown className="ml-2 text-foreground/70" size={14} />
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="p-1 border-border/70 dark:border-border/40 shadow-md">
            <DropdownMenuItem
              className="cursor-pointer rounded-md px-3 py-2 my-1 hover:bg-accent/40 dark:hover:bg-accent/30"
              onSelect={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
            >
              {`Toggle ${theme === 'light' ? 'dark' : 'light'} mode`}
            </DropdownMenuItem>
            <ProfileSettingsModal
              trigger={
                <DropdownMenuItem
                  className="cursor-pointer rounded-md px-3 py-2 my-1 hover:bg-accent/40 dark:hover:bg-accent/30"
                  onSelect={(e) => e.preventDefault()}
                >
                  Profile Settings
                </DropdownMenuItem>
              }
            />
            <DropdownMenuSeparator className="my-1 bg-border/50" />
            <DropdownMenuItem
              className="cursor-pointer rounded-md px-3 py-2 my-1 hover:bg-accent/40 dark:hover:bg-accent/30"
              onSelect={() => signOut()}
            >
              Sign out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </header>
  );
}

export const ChatHeader = memo(PureChatHeader, (prevProps, nextProps) => {
  return prevProps.selectedModelId === nextProps.selectedModelId && prevProps.user?.id === nextProps.user?.id;
});
