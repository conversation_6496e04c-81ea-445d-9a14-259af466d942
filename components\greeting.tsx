import { motion } from 'framer-motion';
import { useTheme } from 'next-themes';
import Image from 'next/image';

export const Greeting = () => {
  const { theme } = useTheme();
  return (
    <div
      key="overview"
      className="max-w-3xl mx-auto md:mt-20 px-8 size-full flex flex-col justify-center"
    >
      {/* Logo section removed */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 10 }}
        transition={{ delay: 0.3 }}
        className="text-3xl font-semibold text-zinc-700 dark:text-zinc-200"
      >
        How can I help you today?
      </motion.div>
    </div>
  );
};
