'use client';

import { useState } from 'react';
import { EditIcon } from 'lucide-react';
import { ToolUsageCard } from './ui/tool-usage-card';

interface UpdateMemoryResultsProps {
  updateData: {
    success: boolean;
    message?: string;
    previousContent?: string;
    newContent?: string;
    reason?: string;
    memoryId?: string;
    error?: string;
    suggestion?: string;
    isExecuting?: boolean;
  };
}

export function UpdateMemoryResults({ updateData }: UpdateMemoryResultsProps) {
  const [showDetails, setShowDetails] = useState(false);

  if (updateData.isExecuting) {
    return (
      <ToolUsageCard
        icon={EditIcon}
        toolName="Update Memory"
        status="loading"
        isExecuting={true}
      />
    );
  }

  if (!updateData.success) {
    return (
      <ToolUsageCard
        icon={EditIcon}
        toolName="Update Memory"
        status="error"
        query={updateData.error || 'Failed to update memory'}
        showContent={showDetails}
        onToggleContent={() => setShowDetails(!showDetails)}
        isExecuting={false}
      >
        {showDetails && updateData.suggestion && (
          <div className="text-sm text-muted-foreground mt-2">
            <strong>Suggestion:</strong> {updateData.suggestion}
          </div>
        )}
      </ToolUsageCard>
    );
  }

  // Create truncated preview for the updated content
  const truncatedContent = updateData.newContent 
    ? updateData.newContent.split(' ').slice(0, 4).join(' ') + (updateData.newContent.split(' ').length > 4 ? '...' : '')
    : '';

  return (
    <ToolUsageCard
      icon={EditIcon}
      toolName="Update Memory"
      status="success"
      query={showDetails ? updateData.newContent : truncatedContent}
      showContent={showDetails}
      onToggleContent={() => setShowDetails(!showDetails)}
      isExecuting={false}
    >
      {showDetails && (
        <div className="space-y-3">
          {updateData.previousContent && (
            <div className="border rounded-md p-3 bg-muted/30">
              <div className="text-xs font-medium mb-1 text-muted-foreground">Previous Content:</div>
              <div className="text-sm text-muted-foreground line-through">
                {updateData.previousContent}
              </div>
            </div>
          )}
          
          {updateData.reason && (
            <div className="text-sm">
              <span className="font-medium text-primary">Reason:</span> {updateData.reason}
            </div>
          )}

          {updateData.memoryId && (
            <div className="text-xs text-muted-foreground font-mono">
              ID: {updateData.memoryId}
            </div>
          )}
        </div>
      )}
    </ToolUsageCard>
  );
}
