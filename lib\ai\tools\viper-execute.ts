import { executeAction } from './execute-action';
import { fetchActions } from './fetch-actions';
import { z } from 'zod';
import { tool } from '@ai-sdk/tools';

/**
 * VIPER Execute Tool
 * 
 * This tool wraps the standard execute-action tool with automatic verification and recovery.
 * It implements the VIPER (Verification-Integrated Proactive Execution and Recovery) framework
 * to improve task completion reliability.
 */
export const viperExecute = tool({
  description: 'Execute an action with automatic verification and recovery. Use this tool when you need to execute an action and ensure it completes successfully.',
  parameters: z.object({
    intent: z.string().describe('The user\'s original intent/goal (e.g., "get commits from branch X in repo Y")'),
    action: z.string().describe('The action to execute (e.g., "GITHUB_LIST_COMMITS")'),
    params: z.record(z.any()).optional().describe('The parameters for the action'),
    maxRetries: z.number().optional().describe('Maximum number of retries (default: 2)'),
    verificationCriteria: z.array(z.string()).optional().describe('Specific criteria to verify in the result'),
  }),
  async handler({ intent, action, params, maxRetries = 2, verificationCriteria = [] }) {
    console.log(`VIPER executing action: ${action} for intent: ${intent}`);
    
    // First attempt with original parameters
    const result = await executeAction.handler({ action, params });
    
    // If successful, verify the result meets the intent
    if (result.success) {
      // Simple verification logic
      const verification = {
        success: true,
        missingElements: [] as string[],
      };
      
      // Check if result is empty or error
      if (!result.result || (typeof result.result === 'object' && Object.keys(result.result).length === 0)) {
        verification.success = false;
        verification.missingElements.push('complete_data');
      }
      
      // Check against specific criteria if provided
      if (verificationCriteria.length > 0) {
        for (const criterion of verificationCriteria) {
          // Simple check if the result contains the criterion
          const resultStr = JSON.stringify(result.result).toLowerCase();
          const hasCriterion = resultStr.includes(criterion.toLowerCase());
          if (!hasCriterion) {
            verification.missingElements.push(criterion);
            verification.success = false;
          }
        }
      }
      
      // If verification passed, return the successful result
      if (verification.success) {
        return {
          success: true,
          verified: true,
          result: result.result,
          action,
          params,
          message: `Successfully executed ${action} and verified result matches intent.`
        };
      }
      
      console.log(`VIPER verification failed for ${action}. Missing elements: ${verification.missingElements.join(', ')}`);
    } else {
      console.log(`VIPER initial execution failed for ${action}: ${result.error}`);
    }
    
    // If we reach here, either the execution failed or verification failed
    // Start recovery process
    for (let retry = 0; retry < maxRetries; retry++) {
      console.log(`VIPER recovery attempt ${retry + 1} for ${action}`);
      
      // Strategy 1: Try with refined parameters (if applicable)
      if (result.error?.includes('parameter') || result.error?.includes('invalid')) {
        // Simple parameter refinement based on error message
        const refinedParams = { ...params };
        
        // Extract parameter names from error message
        const paramMatch = result.error.match(/parameter ['"]?([a-zA-Z0-9_]+)['"]?/);
        if (paramMatch && paramMatch[1]) {
          const paramName = paramMatch[1];
          
          // If the parameter is missing, try to infer it from the intent
          if (!refinedParams[paramName]) {
            // Very simple inference - extract words that might be parameters
            const words = intent.split(/\s+/);
            for (const word of words) {
              if (word.length > 2 && !refinedParams[word.toLowerCase()]) {
                refinedParams[paramName] = word;
                break;
              }
            }
          }
        }
        
        console.log(`VIPER trying refined parameters:`, refinedParams);
        
        const retryResult = await executeAction.handler({ 
          action, 
          params: refinedParams 
        });
        
        if (retryResult.success) {
          return {
            success: true,
            verified: true,
            result: retryResult.result,
            action,
            params: refinedParams,
            recovery: 'parameter_refinement',
            message: `Successfully executed ${action} after parameter refinement.`
          };
        }
      }
      
      // Strategy 2: Try alternative actions
      console.log(`VIPER searching for alternative actions for intent: ${intent}`);
      const alternativeActions = await fetchActions.handler({ 
        description: `Alternative to ${action} for ${intent}`
      });
      
      if (alternativeActions.success && alternativeActions.actions?.length > 0) {
        // Try each alternative action
        for (const altActionObj of alternativeActions.actions.slice(0, 3)) { // Try top 3 alternatives
          const altAction = altActionObj.name || altActionObj.id;
          if (altAction && altAction !== action) {
            console.log(`VIPER trying alternative action: ${altAction}`);
            
            const altResult = await executeAction.handler({ 
              action: altAction, 
              params 
            });
            
            if (altResult.success) {
              return {
                success: true,
                verified: true,
                result: altResult.result,
                action: altAction,
                params,
                recovery: 'alternative_action',
                message: `Successfully executed alternative action ${altAction}.`
              };
            }
          }
        }
      }
      
      // Strategy 3: Hierarchical navigation
      // This is a simplified implementation - in a real system, you would
      // implement more sophisticated hierarchical navigation
      if (action.includes('GITHUB') && intent.toLowerCase().includes('branch')) {
        // Example: For GitHub branch-related actions, try getting the repo first
        if (action.includes('COMMIT') || action.includes('BRANCH')) {
          console.log(`VIPER trying hierarchical navigation for GitHub`);
          
          // First get repositories
          const repoAction = 'GITHUB_LIST_REPOSITORIES_FOR_THE_AUTHENTICATED_USER';
          const repoResult = await executeAction.handler({ 
            action: repoAction,
            params: {}
          });
          
          if (repoResult.success && repoResult.result) {
            // Extract repo name from intent
            const repoWords = intent.toLowerCase().split(/\s+/);
            let repoName = '';
            
            for (let i = 0; i < repoWords.length; i++) {
              if (repoWords[i] === 'repo' || repoWords[i] === 'repository') {
                if (i + 1 < repoWords.length) {
                  repoName = repoWords[i + 1];
                  break;
                }
              }
            }
            
            // Find matching repo
            const repos = Array.isArray(repoResult.result) ? repoResult.result : [];
            const matchingRepo = repos.find((repo: any) => 
              repo.name?.toLowerCase().includes(repoName) || 
              repo.full_name?.toLowerCase().includes(repoName)
            );
            
            if (matchingRepo) {
              // Now try to get branches
              const branchAction = 'GITHUB_LIST_BRANCHES';
              const branchResult = await executeAction.handler({
                action: branchAction,
                params: {
                  owner: matchingRepo.owner?.login || 'authenticated',
                  repo: matchingRepo.name
                }
              });
              
              if (branchResult.success && branchResult.result) {
                return {
                  success: true,
                  verified: true,
                  result: {
                    repository: matchingRepo,
                    branches: branchResult.result
                  },
                  action: 'hierarchical_navigation',
                  recovery: 'hierarchical_navigation',
                  message: `Successfully navigated repository hierarchy.`
                };
              }
            }
          }
        }
      }
    }
    
    // If all recovery strategies fail, return the original error
    return {
      success: false,
      verified: false,
      error: result.error,
      message: result.message || 'Failed to execute action and recovery strategies were unsuccessful.',
      recovery_attempted: true
    };
  }
});
