import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { generateEmbedding } from '@/lib/memory/embeddings';
import { createMemory, getRelevantMemories } from '@/lib/memory/queries';

export async function GET(request: Request) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the user ID
    const userId = session.user.id;

    // Test embedding generation
    const testText = 'The user prefers dark mode in all applications';
    const embedding = await generateEmbedding(testText);

    // Create test memories
    const memories = [
      {
        content: 'The user prefers dark mode in all applications',
        category: 'preferences',
      },
      {
        content: 'The user\'s name is <PERSON><PERSON>',
        category: 'personal',
      },
      {
        content: 'The user is working on an AI chatbot project',
        category: 'work',
      },
    ];

    const createdMemories = [];
    for (const memory of memories) {
      const result = await createMemory({
        userId,
        content: memory.content,
        category: memory.category,
      });
      createdMemories.push(result[0]);
    }

    // Test memory retrieval
    const query = 'What are the user\'s preferences?';
    const relevantMemories = await getRelevantMemories({
      userId,
      query,
      limit: 2,
    });

    return NextResponse.json({
      success: true,
      embedding: {
        dimensions: embedding.length,
        sample: embedding.slice(0, 5),
      },
      createdMemories: createdMemories.map(m => ({
        id: m.id,
        content: m.content,
        category: m.category,
      })),
      query,
      relevantMemories,
    });
  } catch (error) {
    console.error('Error testing memory system:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
