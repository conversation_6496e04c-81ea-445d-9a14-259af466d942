import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getAllToolExperiences, createToolExperience } from '@/lib/memory/toolExperiences';

/**
 * GET /api/admin/tool-experiences
 * Retrieves all tool experiences for admin management
 */
export async function GET() {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // For now, allow any authenticated user to access admin features
    // In production, you might want to add role-based access control
    
    const experiences = await getAllToolExperiences();
    
    return NextResponse.json(experiences);
  } catch (error) {
    console.error('Error fetching tool experiences:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tool experiences' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/tool-experiences
 * Creates a new tool experience
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    
    // Validate required fields
    const {
      experienceType,
      appName,
      actionName,
      contextTags,
      experienceContent,
      successRate,
      priority
    } = body;

    if (!experienceType || !appName || !experienceContent) {
      return NextResponse.json(
        { error: 'Missing required fields: experienceType, appName, experienceContent' },
        { status: 400 }
      );
    }

    // Validate experience type
    const validTypes = ['pattern', 'insight', 'advice', 'workflow', 'parameter_combo'];
    if (!validTypes.includes(experienceType)) {
      return NextResponse.json(
        { error: 'Invalid experience type' },
        { status: 400 }
      );
    }

    // Validate priority
    const validPriorities = ['critical', 'high', 'medium', 'low'];
    if (priority && !validPriorities.includes(priority)) {
      return NextResponse.json(
        { error: 'Invalid priority' },
        { status: 400 }
      );
    }

    // Validate success rate
    if (successRate !== undefined && (successRate < 0 || successRate > 1)) {
      return NextResponse.json(
        { error: 'Success rate must be between 0 and 1' },
        { status: 400 }
      );
    }

    // Create the tool experience
    const result = await createToolExperience({
      experienceType,
      appName: appName.toLowerCase(),
      actionName: actionName || undefined,
      contextTags: Array.isArray(contextTags) ? contextTags : [],
      experienceContent,
      successRate: successRate || 1.0,
      priority: priority || 'medium'
    });

    return NextResponse.json(result[0], { status: 201 });
  } catch (error) {
    console.error('Error creating tool experience:', error);
    return NextResponse.json(
      { error: 'Failed to create tool experience' },
      { status: 500 }
    );
  }
}
