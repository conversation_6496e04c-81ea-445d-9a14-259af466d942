import { compare } from 'bcrypt-ts';
import NextAuth, { type User, type Session } from 'next-auth';
import Credentials from 'next-auth/providers/credentials';

import { getUser } from '@/lib/db/queries';
import { withRetry } from '@/lib/db/client';

import { authConfig } from './auth.config';

interface ExtendedSession extends Session {
  user: User;
}

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  ...authConfig,
  providers: [
    Credentials({
      credentials: {},
      async authorize({ email, password }: any) {
        console.log('Authorizing user:', email);
        const users = await getUser(email);
        console.log('Found users:', users);

        if (users.length === 0) {
          console.log('No user found with this email');
          return null;
        }

        // biome-ignore lint: Forbidden non-null assertion.
        const passwordsMatch = await compare(password, users[0].password!);
        console.log('Password match:', passwordsMatch);

        if (!passwordsMatch) return null;

        // Check if the user's email is confirmed
        console.log('User confirmed status:', users[0].confirmed);

        // Check if the user's email is confirmed
        if (!users[0].confirmed) {
          console.log('User email is not verified');
          throw new Error('Email not verified');
        }

        console.log('User authorized successfully');
        return users[0] as any;
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.email = user.email;
      }

      return token;
    },
    async session({
      session,
      token,
    }: {
      session: ExtendedSession;
      token: any;
    }) {
      if (session.user) {
        session.user.id = token.id as string;
        // Ensure email is set from token
        if (token.email) {
          session.user.email = token.email as string;
        }

        // Check if the user still exists and is confirmed, but only if CHECK_USER_STATUS is enabled
        const shouldCheckUserStatus = process.env.CHECK_USER_STATUS === 'true';

        if (shouldCheckUserStatus) {
          try {
            if (session.user.email) {
              console.log('Validating session for user:', session.user.email);
              const users = await withRetry(
                async () => await getUser(session.user.email as string),
                {
                  maxRetries: 2,
                  onRetry: (error) => {
                    console.warn('Retrying user validation due to error:', error.message);
                  }
                }
              );

              if (users.length === 0 || !users[0].confirmed) {
                console.log('User no longer exists or is not confirmed:', session.user.email);
                // Return null to invalidate the session
                return null as any;
              }
            }
          } catch (error) {
            console.error('Error checking user status during session validation:', error);
            // Continue with the session in case of database errors
            console.log('Continuing with session despite database error');
          }
        } else {
          console.log('Skipping user status check (CHECK_USER_STATUS=false)');
        }
      }

      return session;
    },
  },
});
