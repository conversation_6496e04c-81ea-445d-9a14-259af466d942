-- Update ToolExperiences table to remove unnecessary columns
-- Run this in Supabase SQL Editor

-- Step 1: Check current table structure
SELECT
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns
WHERE table_name = 'ToolExperiences'
ORDER BY ordinal_position;

-- Step 2: Remove the phase_category constraint first
ALTER TABLE "ToolExperiences" DROP CONSTRAINT IF EXISTS "ToolExperiences_phase_category_check";

-- Step 3: Drop unnecessary columns (keeping experience_type, app_name, experience_content, success_rate, priority)
ALTER TABLE "ToolExperiences" DROP COLUMN IF EXISTS "action_name";
ALTER TABLE "ToolExperiences" DROP COLUMN IF EXISTS "context_tags";
ALTER TABLE "ToolExperiences" DROP COLUMN IF EXISTS "usage_count";
ALTER TABLE "ToolExperiences" DROP COLUMN IF EXISTS "lastUsed";
ALTER TABLE "ToolExperiences" DROP COLUMN IF EXISTS "phase_category";

-- Step 4: Verify the updated structure
SELECT
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns
WHERE table_name = 'ToolExperiences'
ORDER BY ordinal_position;

-- Step 5: Insert your sample data
INSERT INTO "ToolExperiences" (
  "experience_type",
  "app_name",
  "experience_content",
  "success_rate",
  "priority",
  "isActive"
) VALUES (
  'insight',
  'github',
  'u should search about list repositories for authenticated users when i say MY Repos',
  0.88,
  'high',
  true
);

-- Step 6: Verify the data was inserted
SELECT
  id,
  experience_type,
  app_name,
  LEFT(experience_content, 50) as content_preview,
  success_rate,
  priority,
  "isActive",
  "createdAt"
FROM "ToolExperiences"
WHERE app_name = 'github'
ORDER BY "createdAt" DESC;

-- Final table structure should be:
-- id (uuid, primary key)
-- experience_type (text, not null)
-- app_name (text, not null)
-- experience_content (text, not null)
-- success_rate (real, default 1)
-- priority (text, default 'medium')
-- "createdAt" (timestamp, default now())
-- "isActive" (boolean, default true)
-- embedding (vector)
