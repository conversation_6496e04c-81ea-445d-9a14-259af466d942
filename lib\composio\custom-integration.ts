import { OpenAIToolSet } from "composio-core";

/**
 * Gets the integration ID for the specified provider from environment variables
 *
 * @param provider The provider name (e.g., 'github', 'gmail', 'googlecalendar')
 * @returns The integration ID from environment variables
 * @throws Error if the integration ID is not found in environment variables
 */
export async function getCustomIntegration(provider: string): Promise<string> {
  // Check for integration ID in environment variables
  const envVarName = `COMPOSIO_${provider.toUpperCase()}_INTEGRATION_ID`;
  const integrationId = process.env[envVarName];

  if (!integrationId) {
    throw new Error(`Integration for ${provider} is coming soon. Please check back later.`);
  }

  console.log(`Using integration ID for ${provider} from environment variables: ${integrationId}`);
  return integrationId;
}

// No longer needed as we're only using pre-created integrations from environment variables
