'use client';

import { useState } from 'react';
import { SearchIcon, ExternalLinkIcon } from 'lucide-react';
import { ToolUsageCard } from './ui/tool-usage-card';

interface SearchResult {
  title: string;
  url: string;
  content: string;
  score: number;
}

interface SearchResultsProps {
  searchData?: {
    results: SearchResult[];
    query: string;
    message: string;
    error?: string;
    isExecuting?: boolean;
  };
}

export function SearchResults({ searchData }: SearchResultsProps) {
  const [showResults, setShowResults] = useState(false);

  if (!searchData) {
    // Loading state
    return (
      <ToolUsageCard
        icon={SearchIcon}
        toolName="Search"
        status="loading"
        isExecuting={true}
      >
        <div className="space-y-2">
          <div className="h-5 w-3/4 animate-pulse rounded-md bg-muted"></div>
          <div className="h-4 w-full animate-pulse rounded-md bg-muted"></div>
          <div className="h-4 w-1/2 animate-pulse rounded-md bg-muted"></div>
        </div>
      </ToolUsageCard>
    );
  }

  // Handle error cases
  if (searchData.error || !searchData.results || searchData.results.length === 0) {
    return (
      <ToolUsageCard
        icon={SearchIcon}
        toolName="Search"
        status="error"
        query={searchData.query}
        errorMessage={searchData.error || "No search results found"}
        isExecuting={searchData.isExecuting}
      />
    );
  }

  return (
    <ToolUsageCard
      icon={SearchIcon}
      toolName="Search"
      status="success"
      query={searchData.query}
      showContent={showResults}
      onToggleContent={() => setShowResults(!showResults)}
      isExecuting={searchData.isExecuting}
    >
      <div className="space-y-2">
        {searchData.results.map((result, index) => (
          <a
            key={index}
            href={result.url}
            target="_blank"
            rel="noopener noreferrer"
            className="block border border-border/50 rounded-md overflow-hidden hover:shadow-sm transition-all"
          >
            <div className="flex items-center justify-between gap-2 px-3 py-2 bg-gradient-to-b from-muted/10 to-muted/20 border-b border-border/40">
              <h3 className="font-medium text-sm text-primary truncate">{result.title}</h3>
              <ExternalLinkIcon size={14} className="text-primary/70 flex-shrink-0" />
            </div>
            <div className="p-3">
              <p className="text-xs text-muted-foreground line-clamp-2">{result.content}</p>
              <div className="text-xs text-muted-foreground truncate mt-2 opacity-70">{result.url}</div>
            </div>
          </a>
        ))}
      </div>
    </ToolUsageCard>
  );
}
