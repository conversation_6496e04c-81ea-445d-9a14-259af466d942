// Simple test script to verify the date/time utility works correctly
require('dotenv').config({ path: '.env.local' });

async function testDateTimeUtility() {
  try {
    // Simulate the getCurrentTimeForLocation function
    const location = 'New York, USA';
    const apiKey = process.env.ABSTRACT_API_KEY;
    
    if (!apiKey) {
      throw new Error('ABSTRACT_API_KEY is not defined in environment variables');
    }

    console.log(`Testing Abstract API with key: ${apiKey.substring(0, 5)}...`);
    console.log(`Getting current time for location: ${location}`);
    
    const url = `https://timezone.abstractapi.com/v1/current_time?api_key=${apiKey}&location=${encodeURIComponent(location)}`;
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Abstract API returned status ${response.status}`);
    }
    
    const data = await response.json();
    console.log('API Response:');
    console.log(JSON.stringify(data, null, 2));
    
    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error('Error testing date/time utility:', error);
  }
}

testDateTimeUtility();
