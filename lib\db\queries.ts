import 'server-only';
import crypto from 'crypto';

import { genSaltSync, hashSync } from 'bcrypt-ts';
import {
  and,
  asc,
  desc,
  eq,
  gt,
  gte,
  inArray,
  lt,
  type SQL,
} from 'drizzle-orm';

import {
  user,
  chat,
  type User,
  document,
  type Suggestion,
  suggestion,
  message,
  vote,
  type DBMessage,
  type Chat,
  passwordReset,
  type PasswordReset,
  connection,
  type Connection,
} from './schema';
import type { ArtifactKind } from '@/components/artifact';
import { db, withRetry } from './client';

export async function getUser(email: string): Promise<Array<User>> {
  try {
    return await withRetry(async () => {
      return await db.select().from(user).where(eq(user.email, email));
    });
  } catch (error) {
    console.error('Failed to get user from database');
    throw error;
  }
}

export async function createUser(email: string, password: string, firstName?: string, lastName?: string) {
  const salt = genSaltSync(10);
  const hash = hashSync(password, salt);

  try {
    return await db.insert(user).values({
      email,
      password: hash,
      firstName: firstName || null,
      lastName: lastName || null
    });
  } catch (error) {
    console.error('Failed to create user in database');
    throw error;
  }
}

export async function saveChat({
  id,
  userId,
  title,
}: {
  id: string;
  userId: string;
  title: string;
}) {
  try {
    return await db.insert(chat).values({
      id,
      createdAt: new Date(),
      userId,
      title,
    });
  } catch (error) {
    console.error('Failed to save chat in database');
    throw error;
  }
}

export async function deleteChatById({ id }: { id: string }) {
  try {
    await db.delete(vote).where(eq(vote.chatId, id));
    await db.delete(message).where(eq(message.chatId, id));

    return await db.delete(chat).where(eq(chat.id, id));
  } catch (error) {
    console.error('Failed to delete chat by id from database');
    throw error;
  }
}

export async function getChatsByUserId({
  id,
  limit,
  startingAfter,
  endingBefore,
}: {
  id: string;
  limit: number;
  startingAfter: string | null;
  endingBefore: string | null;
}) {
  try {
    const extendedLimit = limit + 1;

    const query = (whereCondition?: SQL<any>) =>
      db
        .select()
        .from(chat)
        .where(
          whereCondition
            ? and(whereCondition, eq(chat.userId, id))
            : eq(chat.userId, id),
        )
        .orderBy(desc(chat.createdAt))
        .limit(extendedLimit);

    let filteredChats: Array<Chat> = [];

    if (startingAfter) {
      const [selectedChat] = await db
        .select()
        .from(chat)
        .where(eq(chat.id, startingAfter))
        .limit(1);

      if (!selectedChat) {
        throw new Error(`Chat with id ${startingAfter} not found`);
      }

      filteredChats = await query(gt(chat.createdAt, selectedChat.createdAt));
    } else if (endingBefore) {
      const [selectedChat] = await db
        .select()
        .from(chat)
        .where(eq(chat.id, endingBefore))
        .limit(1);

      if (!selectedChat) {
        throw new Error(`Chat with id ${endingBefore} not found`);
      }

      filteredChats = await query(lt(chat.createdAt, selectedChat.createdAt));
    } else {
      filteredChats = await query();
    }

    const hasMore = filteredChats.length > limit;

    return {
      chats: hasMore ? filteredChats.slice(0, limit) : filteredChats,
      hasMore,
    };
  } catch (error) {
    console.error('Failed to get chats by user from database');
    throw error;
  }
}

export async function getChatById({ id }: { id: string }) {
  try {
    return await withRetry(async () => {
      const [selectedChat] = await db.select().from(chat).where(eq(chat.id, id));
      return selectedChat || null; // Explicitly return null if chat not found
    });
  } catch (error) {
    console.error(`Failed to get chat by id from database: ${id}`, error);
    // Return null instead of throwing to allow graceful handling
    return null;
  }
}

export async function saveMessages({
  messages,
}: {
  messages: Array<DBMessage>;
}) {
  try {
    return await db.insert(message).values(messages);
  } catch (error) {
    console.error('Failed to save messages in database', error);
    throw error;
  }
}

export async function getMessagesByChatId({ id }: { id: string }) {
  try {
    return await db
      .select()
      .from(message)
      .where(eq(message.chatId, id))
      .orderBy(asc(message.createdAt));
  } catch (error) {
    console.error('Failed to get messages by chat id from database', error);
    throw error;
  }
}

export async function voteMessage({
  chatId,
  messageId,
  type,
}: {
  chatId: string;
  messageId: string;
  type: 'up' | 'down';
}) {
  try {
    const [existingVote] = await db
      .select()
      .from(vote)
      .where(and(eq(vote.messageId, messageId)));

    if (existingVote) {
      return await db
        .update(vote)
        .set({ isUpvoted: type === 'up' })
        .where(and(eq(vote.messageId, messageId), eq(vote.chatId, chatId)));
    }
    return await db.insert(vote).values({
      chatId,
      messageId,
      isUpvoted: type === 'up',
    });
  } catch (error) {
    console.error('Failed to upvote message in database', error);
    throw error;
  }
}

export async function getVotesByChatId({ id }: { id: string }) {
  try {
    return await db.select().from(vote).where(eq(vote.chatId, id));
  } catch (error) {
    console.error('Failed to get votes by chat id from database', error);
    throw error;
  }
}

export async function saveDocument({
  id,
  title,
  kind,
  content,
  userId,
}: {
  id: string;
  title: string;
  kind: ArtifactKind;
  content: string;
  userId: string;
}) {
  try {
    return await db
      .insert(document)
      .values({
        id,
        title,
        kind,
        content,
        userId,
        createdAt: new Date(),
      })
      .returning();
  } catch (error) {
    console.error('Failed to save document in database');
    throw error;
  }
}

export async function getDocumentsById({ id }: { id: string }) {
  try {
    const documents = await db
      .select()
      .from(document)
      .where(eq(document.id, id))
      .orderBy(asc(document.createdAt));

    return documents;
  } catch (error) {
    console.error('Failed to get document by id from database');
    throw error;
  }
}

export async function getDocumentById({ id }: { id: string }) {
  try {
    const [selectedDocument] = await db
      .select()
      .from(document)
      .where(eq(document.id, id))
      .orderBy(desc(document.createdAt));

    return selectedDocument;
  } catch (error) {
    console.error('Failed to get document by id from database');
    throw error;
  }
}

export async function deleteDocumentsByIdAfterTimestamp({
  id,
  timestamp,
}: {
  id: string;
  timestamp: Date;
}) {
  try {
    await db
      .delete(suggestion)
      .where(
        and(
          eq(suggestion.documentId, id),
          gt(suggestion.documentCreatedAt, timestamp),
        ),
      );

    return await db
      .delete(document)
      .where(and(eq(document.id, id), gt(document.createdAt, timestamp)))
      .returning();
  } catch (error) {
    console.error(
      'Failed to delete documents by id after timestamp from database',
    );
    throw error;
  }
}

export async function saveSuggestions({
  suggestions,
}: {
  suggestions: Array<Suggestion>;
}) {
  try {
    return await db.insert(suggestion).values(suggestions);
  } catch (error) {
    console.error('Failed to save suggestions in database');
    throw error;
  }
}

export async function getSuggestionsByDocumentId({
  documentId,
}: {
  documentId: string;
}) {
  try {
    return await db
      .select()
      .from(suggestion)
      .where(and(eq(suggestion.documentId, documentId)));
  } catch (error) {
    console.error(
      'Failed to get suggestions by document version from database',
    );
    throw error;
  }
}

export async function getMessageById({ id }: { id: string }) {
  try {
    return await db.select().from(message).where(eq(message.id, id));
  } catch (error) {
    console.error('Failed to get message by id from database');
    throw error;
  }
}

export async function deleteMessagesByChatIdAfterTimestamp({
  chatId,
  timestamp,
}: {
  chatId: string;
  timestamp: Date;
}) {
  try {
    const messagesToDelete = await db
      .select({ id: message.id })
      .from(message)
      .where(
        and(eq(message.chatId, chatId), gte(message.createdAt, timestamp)),
      );

    const messageIds = messagesToDelete.map((message) => message.id);

    if (messageIds.length > 0) {
      await db
        .delete(vote)
        .where(
          and(eq(vote.chatId, chatId), inArray(vote.messageId, messageIds)),
        );

      return await db
        .delete(message)
        .where(
          and(eq(message.chatId, chatId), inArray(message.id, messageIds)),
        );
    }
  } catch (error) {
    console.error(
      'Failed to delete messages by id after timestamp from database',
    );
    throw error;
  }
}

export async function updateChatVisiblityById({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: 'private' | 'public';
}) {
  try {
    return await db.update(chat).set({ visibility }).where(eq(chat.id, chatId));
  } catch (error) {
    console.error('Failed to update chat visibility in database');
    throw error;
  }
}

export async function updateUserConfirmedStatus({
  email,
  confirmed,
}: {
  email: string;
  confirmed: boolean;
}) {
  try {
    console.log(`Updating user ${email} confirmed status to ${confirmed}`);

    // First, get the user to make sure they exist
    const users = await db.select().from(user).where(eq(user.email, email));
    console.log('Found users:', users);

    if (users.length === 0) {
      console.error('No user found with email:', email);
      return { count: 0 };
    }

    // Use a raw SQL query to ensure the update works
    const result = await db.execute(
      `UPDATE "User" SET "confirmed" = $1 WHERE "email" = $2`,
      [confirmed, email]
    );

    console.log('Update query result:', result);
    return result;
  } catch (error) {
    console.error('Failed to update user confirmed status in database', error);
    throw error;
  }
}

export async function createPasswordResetToken(email: string) {
  try {
    // Check if user exists
    const users = await getUser(email);
    if (users.length === 0) {
      throw new Error('User not found');
    }

    // Generate a random token
    const token = crypto.randomUUID();

    // Set expiration time (1 hour from now)
    const expires = new Date();
    expires.setHours(expires.getHours() + 1);

    // Save the token in the database
    const result = await db.insert(passwordReset).values({
      email,
      token,
      expires,
      createdAt: new Date(),
      used: false,
    }).returning();

    return result[0];
  } catch (error) {
    console.error('Failed to create password reset token', error);
    throw error;
  }
}

export async function getPasswordResetToken(token: string): Promise<PasswordReset | null> {
  try {
    const tokens = await db
      .select()
      .from(passwordReset)
      .where(eq(passwordReset.token, token));

    if (tokens.length === 0) {
      return null;
    }

    return tokens[0];
  } catch (error) {
    console.error('Failed to get password reset token', error);
    throw error;
  }
}

export async function markPasswordResetTokenAsUsed(token: string) {
  try {
    return await db
      .update(passwordReset)
      .set({ used: true })
      .where(eq(passwordReset.token, token));
  } catch (error) {
    console.error('Failed to mark password reset token as used', error);
    throw error;
  }
}

export async function updateUserPassword(email: string, newPassword: string) {
  try {
    const salt = genSaltSync(10);
    const hash = hashSync(newPassword, salt);

    return await db
      .update(user)
      .set({ password: hash })
      .where(eq(user.email, email));
  } catch (error) {
    console.error('Failed to update user password', error);
    throw error;
  }
}

// Connection management functions

export async function getConnectionsByUserId(userId: string): Promise<Array<Connection>> {
  try {
    return await db
      .select()
      .from(connection)
      .where(eq(connection.userId, userId));
  } catch (error) {
    console.error('Failed to get connections for user from database');
    throw error;
  }
}

export async function saveConnection({
  userId,
  provider,
  connectionId,
  status,
  metadata
}: {
  userId: string;
  provider: string;
  connectionId: string;
  status: 'ACTIVE' | 'EXPIRED' | 'REVOKED';
  metadata?: any;
}): Promise<Connection> {
  try {
    const [newConnection] = await db
      .insert(connection)
      .values({
        userId,
        provider,
        connectionId,
        status,
        createdAt: new Date(),
        updatedAt: new Date(),
        metadata: metadata || null
      })
      .returning();

    return newConnection;
  } catch (error) {
    console.error('Failed to save connection to database');
    throw error;
  }
}

export async function updateConnectionStatus({
  id,
  status
}: {
  id: string;
  status: 'ACTIVE' | 'EXPIRED' | 'REVOKED';
}): Promise<Connection> {
  try {
    const [updatedConnection] = await db
      .update(connection)
      .set({
        status,
        updatedAt: new Date()
      })
      .where(eq(connection.id, id))
      .returning();

    return updatedConnection;
  } catch (error) {
    console.error('Failed to update connection status in database');
    throw error;
  }
}

export async function deleteConnection(id: string): Promise<void> {
  try {
    await db
      .delete(connection)
      .where(eq(connection.id, id));
  } catch (error) {
    console.error('Failed to delete connection from database');
    throw error;
  }
}
