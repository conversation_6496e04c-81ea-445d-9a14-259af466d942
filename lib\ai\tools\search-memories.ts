import { tool } from 'ai';
import { z } from 'zod';
import { Session } from 'next-auth';
import { getRelevantMemories } from '@/lib/memory/queries';

/**
 * AI agent tool for searching and analyzing existing memories
 * Enables intelligent duplicate detection and memory analysis
 */
export const createSearchMemoriesTool = ({ session }: { session: Session | null }) => {
  return tool({
    name: 'searchMemories',
    description: 'Search existing memories to check for duplicates, find related information, or analyze memory patterns before creating new memories. Use this to maintain clean, organized memory without duplicates.',
    parameters: z.object({
      query: z.string().describe('What to search for in existing memories'),
      intent: z.enum(['duplicate_check', 'related_info', 'update_check', 'general_search']).describe('Purpose of the search'),
      limit: z.number().optional().default(5).describe('Maximum number of memories to return'),
      similarityThreshold: z.number().optional().default(0.3).describe('Minimum similarity threshold (0.0 to 1.0)'),
    }),
    execute: async ({ query, intent, limit, similarityThreshold }) => {
      if (!session?.user?.id) {
        return { error: 'User not authenticated', success: false };
      }

      try {
        console.log(`🔍 AI searching memories: "${query}" (intent: ${intent})`);

        const memories = await getRelevantMemories({
          userId: session.user.id,
          query,
          limit,
          similarityThreshold
        });

        if (!memories || memories.length === 0) {
          return {
            success: true,
            message: 'No relevant memories found',
            memories: [],
            intent,
            query,
            recommendation: intent === 'duplicate_check' ? 'Safe to create new memory' : 'No existing information found',
            isExecuting: false
          };
        }

        // Analyze results based on intent
        let analysis = '';
        let recommendation = '';

        switch (intent) {
          case 'duplicate_check':
            const highSimilarity = memories.filter(m => m.similarity > 0.7);
            if (highSimilarity.length > 0) {
              analysis = `Found ${highSimilarity.length} highly similar memories`;
              recommendation = 'Consider updating existing memory instead of creating new one';
            } else {
              analysis = `Found ${memories.length} somewhat related memories`;
              recommendation = 'Safe to create new memory, but consider if it relates to existing ones';
            }
            break;

          case 'update_check':
            const updateCandidates = memories.filter(m => m.similarity > 0.5);
            analysis = `Found ${updateCandidates.length} memories that might need updating`;
            recommendation = updateCandidates.length > 0 ? 'Consider updating the most relevant memory' : 'No memories need updating';
            break;

          case 'related_info':
            analysis = `Found ${memories.length} related memories`;
            recommendation = 'Review related information before proceeding';
            break;

          default:
            analysis = `Found ${memories.length} relevant memories`;
            recommendation = 'Review results for context';
        }

        console.log(`📊 Memory search analysis: ${analysis}`);

        return {
          success: true,
          message: `Found ${memories.length} relevant memories`,
          memories: memories.map(m => ({
            id: m.id,
            content: m.content,
            category: m.category,
            similarity: m.similarity,
            createdAt: m.createdAt
          })),
          intent,
          query,
          analysis,
          recommendation,
          highestSimilarity: memories.length > 0 ? memories[0].similarity : 0,
          isExecuting: false
        };

      } catch (error) {
        console.error('Failed to search memories:', error);
        return {
          error: 'Failed to search memories',
          success: false,
          message: error instanceof Error ? error.message : 'Unknown error',
          isExecuting: false
        };
      }
    },
  });
};
