/**
 * Simple Node.js script to generate tool experiences with embeddings
 * Outputs SQL that can be run directly in Supabase SQL Editor
 *
 * Usage: node scripts/generate-tool-experiences-sql.js
 */

const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config({ path: '.env.local' });

// Initialize Google AI
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY);

async function generateEmbedding(text) {
  try {
    const model = genAI.getGenerativeModel({ model: 'text-embedding-004' });
    const result = await model.embedContent(text);
    return result.embedding.values;
  } catch (error) {
    console.error('Error generating embedding:', error);
    throw error;
  }
}

// Strategic tool experiences data
const toolExperiences = [
  {
    experienceType: 'workflow',
    appName: 'gmail',
    actionName: 'general',
    contextTags: ['email_management', 'search'],
    experienceContent: 'For email tasks: 1) Use GMAIL_FETCH_EMAILS with specific query parameters 2) Avoid fetching all emails - always use query filters 3) Set maxResults to reasonable limit (10-50)',
    successRate: 0.9,
    priority: 'critical'
  },
  {
    experienceType: 'parameter_combo',
    appName: 'gmail',
    actionName: 'GMAIL_FETCH_EMAILS',
    contextTags: ['search', 'filtering'],
    experienceContent: 'Optimize with: {query: "subject:keyword OR from:email", maxResults: 20, includeSpamTrash: false} - much faster than fetching all emails then filtering',
    successRate: 0.85,
    priority: 'high'
  },
  {
    experienceType: 'workflow',
    appName: 'github',
    actionName: 'general',
    contextTags: ['repo_management', 'project_overview'],
    experienceContent: 'For repository exploration: 1) Use GITHUB_LIST_REPOSITORIES with type=owner and sort=updated 2) Then GITHUB_GET_REPOSITORY_CONTENT with path="" for overview 3) Check README.md for project details',
    successRate: 0.95,
    priority: 'critical'
  },
  {
    experienceType: 'pattern',
    appName: 'github',
    actionName: 'GITHUB_LIST_REPOSITORIES',
    contextTags: ['repo_management'],
    experienceContent: 'Most effective parameters: {type: "owner", sort: "updated", per_page: 10} - shows user\'s most recently active repositories first',
    successRate: 0.92,
    priority: 'high'
  },
  {
    experienceType: 'parameter_combo',
    appName: 'github',
    actionName: 'GITHUB_GET_REPOSITORY_CONTENT',
    contextTags: ['file_exploration'],
    experienceContent: 'For file exploration, start with path="" to get repository structure, then drill down to specific files. Use ref parameter for specific branches.',
    successRate: 0.88,
    priority: 'high'
  },
  {
    experienceType: 'workflow',
    appName: 'notion',
    actionName: 'general',
    contextTags: ['documentation', 'search'],
    experienceContent: 'For Notion tasks: 1) Use NOTION_SEARCH to find pages first 2) Then NOTION_FETCH_NOTION_CHILD_BLOCK with page ID 3) Always check page permissions before accessing',
    successRate: 0.87,
    priority: 'high'
  },
  {
    experienceType: 'insight',
    appName: 'multi',
    actionName: 'general',
    contextTags: ['user_experience', 'optimization'],
    experienceContent: 'Users prefer: 1) Quick results over comprehensive data 2) Filtered/relevant content over raw dumps 3) Clear progress indicators for long operations',
    successRate: 0.93,
    priority: 'medium'
  }
];

async function generateToolExperiencesSQL() {
  console.log('🚀 Generating tool experiences with embeddings...');

  let sqlOutput = `-- Tool Experiences with Vector Embeddings
-- Generated on ${new Date().toISOString()}
-- Run this SQL in Supabase SQL Editor

-- Clear existing data (optional)
-- DELETE FROM "ToolExperiences";

-- Insert tool experiences with embeddings
`;

  for (let i = 0; i < toolExperiences.length; i++) {
    const exp = toolExperiences[i];
    console.log(`Processing ${i + 1}/${toolExperiences.length}: ${exp.experienceType} for ${exp.appName}`);

    try {
      // Create embedding text
      const embeddingText = `${exp.experienceType} ${exp.appName} ${exp.actionName} ${exp.contextTags.join(' ')} ${exp.experienceContent}`;

      // Generate embedding
      const embedding = await generateEmbedding(embeddingText);
      console.log(`Generated embedding with ${embedding.length} dimensions`);

      // Format context tags as PostgreSQL array
      const contextTagsArray = `{${exp.contextTags.map(tag => `"${tag}"`).join(',')}}`;

      // Format embedding as PostgreSQL vector
      const embeddingVector = `[${embedding.join(',')}]`;

      // Generate SQL INSERT statement
      sqlOutput += `
INSERT INTO "ToolExperiences" (
  "experience_type",
  "app_name",
  "action_name",
  "context_tags",
  "experience_content",
  "embedding",
  "success_rate",
  "priority",
  "usage_count",
  "isActive"
) VALUES (
  '${exp.experienceType}',
  '${exp.appName}',
  '${exp.actionName}',
  '${contextTagsArray}',
  '${exp.experienceContent.replace(/'/g, "''")}',
  '${embeddingVector}',
  ${exp.successRate},
  '${exp.priority}',
  0,
  true
);
`;

      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 200));
    } catch (error) {
      console.error(`❌ Failed to process experience ${i + 1}:`, error);
    }
  }

  sqlOutput += `
-- Verify the data
SELECT
  experience_type,
  app_name,
  action_name,
  context_tags,
  LEFT(experience_content, 50) as content_preview,
  success_rate,
  priority
FROM "ToolExperiences"
ORDER BY priority DESC, success_rate DESC;

-- Check embedding dimensions (vector type doesn't use array_length)
SELECT
  app_name,
  experience_type,
  CASE
    WHEN embedding IS NOT NULL THEN 'Has embedding'
    ELSE 'No embedding'
  END as embedding_status
FROM "ToolExperiences"
LIMIT 5;

-- Test vector similarity (should work if embeddings are properly inserted)
SELECT
  app_name,
  experience_type,
  LEFT(experience_content, 30) as content_preview
FROM "ToolExperiences"
WHERE embedding IS NOT NULL
LIMIT 3;
`;

  return sqlOutput;
}

// Main execution
async function main() {
  try {
    const sql = await generateToolExperiencesSQL();

    // Write to file
    const fs = require('fs');
    const filename = `tool-experiences-${Date.now()}.sql`;
    fs.writeFileSync(filename, sql);

    console.log(`\n🎉 SQL generated successfully!`);
    console.log(`📁 File saved as: ${filename}`);
    console.log(`\n📋 Copy the SQL content and run it in Supabase SQL Editor:`);
    console.log('=' .repeat(60));
    console.log(sql);
    console.log('=' .repeat(60));

  } catch (error) {
    console.error('💥 Error:', error);
  }
}

main();
