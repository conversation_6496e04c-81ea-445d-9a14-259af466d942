import 'server-only';
import { sql } from 'drizzle-orm';
import { db, withRetry } from '@/lib/db/client';
import { generateEmbedding } from '@/lib/memory/embeddings';

/**
 * Simplified tool experience management - following user memory pattern exactly
 */
export interface ToolExperienceQuery {
  userRequest?: string;
  targetApp?: string;
  connectedApps?: string[];
  limit?: number;
  similarityThreshold?: number;
}

export interface CreateToolExperienceParams {
  experienceType: string;
  appName: string;
  experienceContent: string;
  successRate?: number;
  priority?: string;
}

/**
 * Retrieves relevant tool experiences using vector similarity - simplified like user memory
 */
export async function getRelevantToolExperiences({
  userRequest,
  targetApp,
  connectedApps = [],
  limit = 5,
  similarityThreshold = 0.3
}: ToolExperienceQuery) {
  try {
    console.log(`🔧 Retrieving tool experiences for: "${userRequest}"`);

    if (!userRequest) {
      return [];
    }

    // Create search query - simple and clean like user memory
    let searchQuery = userRequest;

    // Add app context if specified
    if (targetApp) {
      searchQuery += ` ${targetApp}`;
    } else if (connectedApps.length > 0) {
      searchQuery += ` ${connectedApps.join(' ')}`;
    }

    console.log(`Tool experience search query: "${searchQuery}"`);

    // Generate embedding for the search query
    const queryEmbedding = await generateEmbedding(searchQuery);
    console.log(`Generated tool experience embedding with ${queryEmbedding.length} dimensions`);

    // Simple vector similarity query - following user memory pattern exactly
    const experiences = await withRetry(async () => {
      return await db.execute(sql`
        SELECT
          te.id,
          te.experience_type,
          te.app_name,
          te.experience_content,
          te.success_rate,
          te.priority,
          te."createdAt",
          te."isActive",
          1 - (te.embedding <=> ${JSON.stringify(queryEmbedding)}::vector) AS similarity
        FROM
          "ToolExperiences" te
        WHERE
          te."isActive" = true
          AND te.embedding IS NOT NULL
          AND 1 - (te.embedding <=> ${JSON.stringify(queryEmbedding)}::vector) >= ${similarityThreshold}
          ${targetApp
            ? sql`AND te.app_name = ${targetApp}`
            : connectedApps.length > 0
              ? sql`AND te.app_name = ANY(${`{${connectedApps.join(',')}}`}::text[])`
              : sql``}
        ORDER BY
          similarity DESC,
          CASE te.priority
            WHEN 'critical' THEN 1
            WHEN 'high' THEN 2
            WHEN 'medium' THEN 3
            WHEN 'low' THEN 4
          END,
          te.success_rate DESC
        LIMIT ${limit}
      `);
    });

    console.log(`Found ${experiences.length} relevant tool experiences`);

    if (experiences.length > 0) {
      console.log('Tool experience similarities:');
      experiences.forEach((exp: any, index: number) => {
        console.log(`${index + 1}. "${exp.experience_content.substring(0, 50)}..." - Similarity: ${exp.similarity.toFixed(4)} (${exp.app_name}/${exp.experience_type}/${exp.priority})`);
      });
    }

    return experiences.map((exp: any) => ({
      id: exp.id,
      experienceType: exp.experience_type,
      appName: exp.app_name,
      content: exp.experience_content,
      successRate: exp.success_rate,
      priority: exp.priority,
      createdAt: exp.createdAt,
      similarity: exp.similarity
    }));
  } catch (error) {
    console.error('Error retrieving tool experiences:', error);
    return [];
  }
}

/**
 * Creates a new tool experience entry with vector embedding
 */
export async function createToolExperience({
  experienceType,
  appName,
  experienceContent,
  successRate = 1.0,
  priority = 'medium'
}: CreateToolExperienceParams) {
  try {
    console.log(`Creating tool experience for ${appName}: "${experienceContent}"`);

    // Generate embedding for the experience content
    const embeddingText = `${experienceType} ${appName} ${experienceContent} ${priority}`;
    const embedding = await generateEmbedding(embeddingText);
    console.log(`Generated embedding with ${embedding.length} dimensions for tool experience`);

    return await withRetry(async () => {
      return await db.execute(sql`
        INSERT INTO "ToolExperiences" (
          "id",
          "experience_type",
          "app_name",
          "experience_content",
          "embedding",
          "success_rate",
          "priority",
          "createdAt",
          "isActive"
        )
        VALUES (
          gen_random_uuid(),
          ${experienceType},
          ${appName},
          ${experienceContent},
          ${JSON.stringify(embedding)}::vector,
          ${successRate},
          ${priority},
          NOW(),
          true
        )
        RETURNING *
      `);
    });
  } catch (error) {
    console.error('Failed to create tool experience:', error);
    throw error;
  }
}

/**
 * Gets all tool experiences for admin management
 */
export async function getAllToolExperiences() {
  try {
    return await withRetry(async () => {
      return await db.execute(sql`
        SELECT
          te.id,
          te.experience_type,
          te.app_name,
          te.experience_content,
          te.success_rate,
          te.priority,
          te."createdAt",
          te."isActive"
        FROM
          "ToolExperiences" te
        WHERE
          te."isActive" = true
        ORDER BY
          CASE te.priority
            WHEN 'critical' THEN 1
            WHEN 'high' THEN 2
            WHEN 'medium' THEN 3
            WHEN 'low' THEN 4
          END,
          te.success_rate DESC,
          te."createdAt" DESC
      `);
    });
  } catch (error) {
    console.error('Failed to get all tool experiences:', error);
    return [];
  }
}

/**
 * Deactivates a tool experience
 */
export async function deactivateToolExperience(id: string) {
  try {
    return await withRetry(async () => {
      return await db.execute(sql`
        UPDATE "ToolExperiences"
        SET "isActive" = false
        WHERE id = ${id}
        RETURNING *
      `);
    });
  } catch (error) {
    console.error('Failed to deactivate tool experience:', error);
    throw error;
  }
}
