import { NextResponse } from 'next/server';
import { OpenAIToolSet } from "composio-core";
import { auth } from '@/app/(auth)/auth';
import { getConnectionsByUserId } from '@/lib/db/queries';

// Helper function to ensure connection ID is in UUID format
// Composio API requires UUID format for connection IDs
function ensureUuidFormat(connectionId: string): string {
  // If it's already in UUID format, return it
  if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(connectionId)) {
    return connectionId;
  }

  // If it's in ca_ format, convert it to a UUID-like format
  // This is just a fallback and may not work in all cases
  if (connectionId.startsWith('ca_')) {
    // Remove the ca_ prefix
    const idPart = connectionId.substring(3);

    // Create a UUID-like string (this is just a placeholder, not a real conversion)
    // In a real implementation, you would need to get the actual UUID for this connection
    console.log(`Warning: Converting connection ID from ca_ format to UUID format. This may not work.`);
    return `00000000-0000-0000-0000-${idPart.padStart(12, '0')}`;
  }

  // If it's neither, return as is and hope for the best
  console.log(`Warning: Connection ID ${connectionId} is not in UUID format. This may cause errors.`);
  return connectionId;
}

export async function POST(request: Request) {
  try {
    const session = await auth();

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    let { action, params } = await request.json();

    if (!action) {
      return NextResponse.json(
        { error: 'Action name is required' },
        { status: 400 }
      );
    }

    // Check if parameters are incorrectly included in the action name
    // Example: "GITHUB_LIST_BRANCHES owner=mizouzour86 repo=IRS"
    // Regular expression to match the base action name (before any parameters)
    const actionNameRegex = /^([A-Z0-9_]+)(?:\s+(.*))?$/;
    const actionNameMatch = action.match(actionNameRegex);

    if (actionNameMatch && actionNameMatch[2]) {
      // We found parameters in the action name
      const cleanedAction = actionNameMatch[1]; // The clean action name
      const paramsString = actionNameMatch[2]; // The parameters part

      // Try to extract parameters in format "key=value key2=value2"
      const extractedParams = { ...(params || {}) };
      const paramPairs = paramsString.match(/(\w+)=([^\s]+)/g) || [];
      paramPairs.forEach((pair: string) => {
        const [key, value] = pair.split('=');
        if (key && value) {
          extractedParams[key] = value;
        }
      });

      console.log(`Extracted parameters from action name: ${JSON.stringify(extractedParams)}`);
      console.log(`Cleaned action name: ${cleanedAction}`);

      // Use the cleaned action name and merged parameters
      action = cleanedAction;
      params = extractedParams;
    }

    // Get user's connected services
    const connections = await getConnectionsByUserId(session.user.id);

    if (!connections || connections.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No connected services',
        message: 'You need to connect at least one service before executing actions.',
        redirectUrl: "/profile/connections"
      });
    }

    // Extract connected app names
    const connectedApps = connections.map(conn => conn.provider.toLowerCase());
    console.log(`User has active connections to: ${connectedApps.join(', ')}`);

    // Find the appropriate connection for this action
    const appName = action.toLowerCase().split('_')[0];

    // Get all connections for this app
    const appConnections = connections.filter(conn =>
      conn.provider.toLowerCase() === appName &&
      conn.status === 'ACTIVE'
    );

    if (appConnections.length === 0) {
      return NextResponse.json({
        success: false,
        error: `No active connection found for ${appName}`,
        message: `You need to connect your ${appName} account before executing this action, or your existing connection may need to be refreshed.`,
        redirectUrl: "/profile/connections"
      });
    }

    // Use the most recently created connection for all services
    let connectionId;
    const connection = appConnections.sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )[0];

    // Get the connection ID from the connectionId column (not the record's id)
    connectionId = connection.connectionId;

    // Log connection details for debugging
    console.log(`Connection details for ${appName}:`, {
      recordId: connection.id,
      connectionId: connection.connectionId,
      provider: connection.provider,
      status: connection.status,
      createdAt: connection.createdAt
    });

    if (connectionId) {
      connectionId = ensureUuidFormat(connectionId);
    }

    console.log(`Using connection ID: ${connectionId} for ${appName}`);

    // Verify the connection is valid
    if (!connectionId) {
      return NextResponse.json({
        success: false,
        error: `Invalid connection for ${appName}`,
        message: `Your ${appName} connection appears to be invalid. Please reconnect your account.`,
        redirectUrl: "/profile/connections"
      });
    }

    // Initialize the Composio toolset
    const toolset = new OpenAIToolSet();

    try {
      console.log(`Executing action: ${action}`);
      console.log(`With parameters:`, params || {});
      console.log(`Using connection ID: ${connectionId}`);

      // Execute the action
      let result;
      try {
        result = await toolset.client.actions.execute({
          actionName: action,
          requestBody: {
            appName: appName,
            input: params || {},
            entityId: session.user.id,
            connectedAccountId: connectionId
          }
        });
      } catch (error) {
        const execError = error as Error;
        console.error("Error executing action:", execError);

        // Handle specific error types
        if (execError.message && execError.message.includes('ConnectedAccountNotFoundError')) {
          return NextResponse.json({
            success: false,
            error: `Connection not found or expired`,
            message: `Your ${appName} connection appears to have expired or been invalidated. Please reconnect your ${appName} account and try again.`,
            redirectUrl: "/profile/connections"
          }, { status: 400 });
        }

        if (execError.message && execError.message.includes('NotFoundError')) {
          return NextResponse.json({
            success: false,
            error: `Action not found: ${action}`,
            message: `The action "${action}" could not be found. Please verify the action name and try again.`
          }, { status: 404 });
        }

        if (execError.message && execError.message.includes('AuthenticationError')) {
          return NextResponse.json({
            success: false,
            error: `Authentication error`,
            message: `There was an authentication error with your ${appName} connection. Please reconnect your account and try again.`,
            redirectUrl: "/profile/connections"
          }, { status: 401 });
        }

        // General error
        return NextResponse.json({
          success: false,
          error: execError instanceof Error ? execError.message : String(execError),
          message: 'Failed to execute action. Please check the action name and parameters.'
        }, { status: 500 });
      }

      console.log(`Action executed successfully`);

      // Return the result
      return NextResponse.json({
        success: true,
        result: result.data || result,
        action,
        params
      });
    } catch (error) {
      console.error("Error executing action:", error);
      return NextResponse.json({
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: 'Failed to execute action. Please check the action name and parameters.'
      }, { status: 500 });
    }
  } catch (error) {
    console.error("Request error:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }, { status: 400 });
  }
}
