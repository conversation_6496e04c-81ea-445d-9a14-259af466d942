'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from '@/components/toast';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { requestPasswordReset } from '../actions';

export default function ForgotPasswordPage() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccessful, setIsSuccessful] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!email) {
      toast({ type: 'error', description: 'Please enter your email address' });
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await requestPasswordReset(email);
      
      if (result.status === 'success') {
        setIsSuccessful(true);
        toast({ 
          type: 'success', 
          description: 'Password reset link sent! Please check your email.' 
        });
      } else if (result.status === 'user_not_found') {
        toast({ 
          type: 'error', 
          description: 'No account found with this email address.' 
        });
      } else {
        toast({ 
          type: 'error', 
          description: 'Failed to send reset link. Please try again.' 
        });
      }
    } catch (error) {
      console.error('Error requesting password reset:', error);
      toast({ 
        type: 'error', 
        description: 'An error occurred. Please try again later.' 
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-12">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h3 className="text-xl font-semibold dark:text-zinc-50">Reset Password</h3>
          <p className="text-sm text-gray-500 dark:text-zinc-400">
            {isSuccessful 
              ? 'Check your email for a reset link' 
              : 'Enter your email to receive a password reset link'}
          </p>
        </div>

        {isSuccessful ? (
          <div className="flex flex-col gap-4 px-4 sm:px-16">
            <p className="text-center text-sm text-gray-500 dark:text-zinc-400">
              We've sent a password reset link to <strong>{email}</strong>.
              Please check your email and follow the instructions to reset your password.
            </p>
            <div className="flex flex-col gap-2 mt-4">
              <Button
                onClick={() => router.push('/login')}
                className="w-full"
              >
                Back to Login
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setIsSuccessful(false);
                  setEmail('');
                }}
                className="w-full"
              >
                Try Another Email
              </Button>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="flex flex-col gap-4 px-4 sm:px-16">
            <div className="flex flex-col gap-2">
              <Label
                htmlFor="email"
                className="text-zinc-600 font-normal dark:text-zinc-400"
              >
                Email Address
              </Label>
              <Input
                id="email"
                name="email"
                className="bg-muted text-md md:text-sm"
                type="email"
                placeholder="<EMAIL>"
                autoComplete="email"
                required
                autoFocus
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>

            <Button
              type="submit"
              className="mt-2"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Sending...' : 'Send Reset Link'}
            </Button>

            <p className="text-center text-sm text-gray-600 mt-4 dark:text-zinc-400">
              {'Remember your password? '}
              <Link
                href="/login"
                className="font-semibold text-gray-800 hover:underline dark:text-zinc-200"
              >
                Sign in
              </Link>
            </p>
          </form>
        )}
      </div>
    </div>
  );
}
