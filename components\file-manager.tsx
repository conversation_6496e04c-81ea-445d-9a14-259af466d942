'use client';

import { useEffect, useState } from 'react';
import { useFileStorage } from '@/lib/hooks/use-file-storage';
import { Button } from './ui/button';
import { toast } from 'sonner';
import { Loader2, Trash2, Download, FileIcon, ImageIcon, FileTextIcon } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

type FileItem = {
  name: string;
  id: string;
  created_at: string;
  updated_at: string;
  last_accessed_at: string;
  metadata: any;
  url: string;
  path: string;
};

export function FileManager() {
  const { listFiles, deleteFile, isUploading } = useFileStorage();
  const [files, setFiles] = useState<FileItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState<Record<string, boolean>>({});

  useEffect(() => {
    loadFiles();
  }, []);

  const loadFiles = async () => {
    setIsLoading(true);
    try {
      const fileList = await listFiles();
      setFiles(fileList);
    } catch (error) {
      console.error('Error loading files:', error);
      toast.error('Failed to load files');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteFile = async (path: string) => {
    setIsDeleting(prev => ({ ...prev, [path]: true }));
    try {
      const success = await deleteFile(path);
      if (success) {
        setFiles(files.filter(file => file.path !== path));
        toast.success('File deleted successfully');
      }
    } catch (error) {
      console.error('Error deleting file:', error);
      toast.error('Failed to delete file');
    } finally {
      setIsDeleting(prev => ({ ...prev, [path]: false }));
    }
  };

  const getFileIcon = (contentType: string) => {
    if (contentType.startsWith('image/')) {
      return <ImageIcon className="h-5 w-5" />;
    } else if (contentType.includes('text') || contentType.includes('document')) {
      return <FileTextIcon className="h-5 w-5" />;
    } else {
      return <FileIcon className="h-5 w-5" />;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (files.length === 0) {
    return (
      <div className="text-center py-10 text-muted-foreground">
        <p>No files uploaded yet</p>
        <Button 
          variant="outline" 
          className="mt-4"
          onClick={loadFiles}
        >
          Refresh
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Your Files</h2>
        <Button 
          variant="outline" 
          size="sm"
          onClick={loadFiles}
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : null}
          Refresh
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {files.map((file) => (
          <div 
            key={file.path} 
            className="border rounded-lg p-4 flex flex-col space-y-2 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center space-x-2">
              {getFileIcon(file.metadata?.mimetype || '')}
              <span className="font-medium truncate flex-1" title={file.name}>
                {file.name}
              </span>
            </div>
            
            <div className="text-xs text-muted-foreground">
              Uploaded {formatDistanceToNow(new Date(file.created_at), { addSuffix: true })}
            </div>
            
            <div className="flex justify-between mt-auto pt-2">
              <a 
                href={file.url} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-sm text-blue-600 hover:underline flex items-center"
              >
                <Download className="h-4 w-4 mr-1" />
                Download
              </a>
              
              <Button
                variant="ghost"
                size="sm"
                className="text-red-600 hover:text-red-800 hover:bg-red-50 dark:hover:bg-red-950/20"
                onClick={() => handleDeleteFile(file.path)}
                disabled={isDeleting[file.path]}
              >
                {isDeleting[file.path] ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
