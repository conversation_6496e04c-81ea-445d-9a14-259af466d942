import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel,
} from 'ai';
import { google } from '@ai-sdk/google';
import { isTestEnvironment } from '../constants';
import {
  artifactModel,
  chatModel,
  reasoningModel,
  titleModel,
} from './models.test';
import { enhancedToolMiddleware } from './middleware/enhancedToolMiddleware';

export const myProvider = isTestEnvironment
  ? customProvider({
      languageModels: {
        'chat-model': chatModel,
        'chat-model-reasoning': reasoningModel,
        'title-model': titleModel,
        'artifact-model': artifactModel,
      },
    })
  : customProvider({
      languageModels: {
        'chat-model': google('gemini-1.5-flash'),
        // 'chat-model': wrapLanguageModel({
        //   model: google('gemini-1.5-flash'),
        //   middleware: enhancedToolMiddleware(),
        // }),
        'chat-model-reasoning': wrapLanguageModel({
          model: google('gemini-1.5-flash'),
          middleware: extractReasoningMiddleware({ tagName: 'think' }),
        }),
        'title-model': google('gemini-1.5-flash'),
        'artifact-model': google('gemini-1.5-flash'),
      },
      imageModels: {
        'small-model': {
          ...google('gemini-1.5-flash'),
          maxImagesPerCall: 10,
        },
      },
    });
