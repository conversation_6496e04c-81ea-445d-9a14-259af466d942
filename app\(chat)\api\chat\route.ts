import {
  UIMessage,
  appendResponseMessages,
  createDataStreamResponse,
  smoothStream,
  streamText,
} from 'ai';
import { auth } from '@/app/(auth)/auth';
import { getCoreSystemPrompt, ConversationContext } from '@/lib/ai/core-prompt';
import { getConversationContext } from '@/lib/ai/middleware/memoryMiddleware';
import {
  deleteChatById,
  getChatById,
  saveChat,
  saveMessages,
} from '@/lib/db/queries';
import {
  generateUUID,
  getMostRecentUserMessage,
  getTrailingMessageId,
} from '@/lib/utils';
import { generateTitleFromUserMessage } from '../../actions';
import { createDocument } from '@/lib/ai/tools/create-document';
import { updateDocument } from '@/lib/ai/tools/update-document';
import { requestSuggestions } from '@/lib/ai/tools/request-suggestions';
import { getWeather } from '@/lib/ai/tools/get-weather';
import { getCurrentTime } from '@/lib/ai/tools/get-current-time';
import { searchInternet } from '@/lib/ai/tools/search-internet';
import { extractPage } from '@/lib/ai/tools/extract-page';
import { fetchActions } from '@/lib/ai/tools/fetch-actions';
import { enhancedExecuteAction } from '@/lib/ai/tools/enhanced-execute-action';
import { isProductionEnvironment } from '@/lib/constants';
import { myProvider } from '@/lib/ai/providers';
import { getRelevantMemories } from '@/lib/memory/queries';
import { createRememberTool } from '@/lib/ai/tools/remember';
import { createUpdateMemoryTool } from '@/lib/ai/tools/update-memory';
import { createSearchMemoriesTool } from '@/lib/ai/tools/search-memories';
import { googleCalendarAgent } from '@/lib/ai/tools/google-calendar-agent';

import { getContextualMemories } from '@/lib/ai/middleware/memoryMiddleware';
import { getTaskMemory, isMultiAppRequest } from '@/lib/ai/memory/strategicMemoryManager';

export const maxDuration = 60; // Maximum allowed for Vercel Hobby plan

/**
 * AI-controlled task detection: Determines if a request requires strategic memory
 * Replaces the old message-count based logic with intelligent content analysis
 */
function detectTaskRequest(queryText: string, messages: any[]): boolean {
  const query = queryText.toLowerCase();

  // Skip greetings and casual conversation
  const casualPatterns = [
    /^(hi|hello|hey|good morning|good afternoon|good evening)$/,
    /^(how are you|what's up|how's it going)$/,
    /^(thanks|thank you|bye|goodbye)$/,
    /^(yes|no|ok|okay)$/
  ];

  if (casualPatterns.some(pattern => pattern.test(query.trim()))) {
    return false;
  }

  // Detect task-oriented requests
  const taskPatterns = [
    // Action verbs
    /\b(create|make|build|generate|write|send|fetch|get|find|search|update|delete|modify|change|add|remove|list|show|display)\b/,
    // App-specific keywords (fixed: added "repos" plural)
    /\b(github|gmail|notion|jira|calendar|repository|repo|repos|email|mail|page|issue|ticket|meeting|event)\b/,
    // Work-related keywords
    /\b(project|task|work|job|assignment|deadline|schedule|plan|organize|manage)\b/,
    // Question words indicating information seeking (simplified pattern)
    /\b(what|how|when|where|why|which|who)\b/,
    // Multi-app scenarios
    /\b(then|after|next|also|and then|followed by)\b/
  ];

  const hasTaskPattern = taskPatterns.some(pattern => pattern.test(query));

  // Multi-app requests are always strategic
  const isMultiApp = isMultiAppRequest(query);

  // Context from conversation history
  const hasWorkContext = messages.length > 1 && messages.some((msg: any) => {
    if (msg.role === 'assistant') {
      const content = msg.parts?.find((p: any) => p.type === 'text')?.text || '';
      return /\b(tool|action|execute|fetch|create|update)\b/i.test(content);
    }
    return false;
  });

  return hasTaskPattern || isMultiApp || hasWorkContext;
}

export async function POST(request: Request) {
  try {
    const {
      id,
      messages,
      selectedChatModel,
    }: {
      id: string;
      messages: Array<UIMessage>;
      selectedChatModel: string;
    } = await request.json();

    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    const userMessage = getMostRecentUserMessage(messages);

    if (!userMessage) {
      return new Response('No user message found', { status: 400 });
    }

    const chat = await getChatById({ id });

    if (!chat) {
      const title = await generateTitleFromUserMessage({
        message: userMessage,
      });

      await saveChat({ id, userId: session.user.id, title });
    } else {
      if (chat.userId !== session.user.id) {
        return new Response('Unauthorized', { status: 401 });
      }
    }

    await saveMessages({
      messages: [
        {
          chatId: id,
          id: userMessage.id,
          role: 'user',
          parts: userMessage.parts,
          attachments: userMessage.experimental_attachments ?? [],
          createdAt: new Date(),
        },
      ],
    });

    // Optimized Memory Retrieval - Reduced redundancy for better performance
    let strategicMemory;
    let memoriesContext = '';

    try {
      // Extract text from user message parts
      const queryText = userMessage.parts
        .filter(part => part.type === 'text')
        .map(part => (part as { type: 'text', text: string }).text)
        .join(' ');

      console.log(`🧠 Optimized memory retrieval for: "${queryText}"`);

      // Simplified memory retrieval - only get user memories for personalization
      // Skip tool experiences at chat level to reduce latency
      const userMemories = await getRelevantMemories({
        userId: session.user.id,
        query: queryText,
        limit: 3, // Reduced from 5 to 3 for faster retrieval
        similarityThreshold: 0.3 // Increased threshold for more relevant results
      });

      strategicMemory = {
        userMemories,
        toolExperiences: [], // Tool experiences will be fetched by individual tools when needed
        combinedContext: userMemories.length > 0
          ? `\n\n📋 User Context:\n${userMemories.map((m: any) => `- ${m.content}`).join('\n')}`
          : '',
        retrievalStats: {
          userMemoriesCount: userMemories.length,
          toolExperiencesCount: 0,
          totalRetrievalTime: 0
        }
      };

      memoriesContext = strategicMemory.combinedContext;

      console.log(`✅ Memory retrieved: ${strategicMemory.retrievalStats.userMemoriesCount} user memories (optimized)`);
    } catch (error) {
      console.error('Error retrieving memory:', error);
      // Continue without memories if there's an error
      memoriesContext = '';
    }

    // Get optimized conversation context for stronger short-term memory
    const conversationContext: ConversationContext = getConversationContext(messages);

    // Optimized logging - only log when there's meaningful context
    if (conversationContext.toolsUsed.length > 0 || conversationContext.userPatterns.length > 0) {
      console.log('🧠 Conversation Awareness:', {
        tools: conversationContext.toolsUsed.length,
        patterns: conversationContext.userPatterns.length,
        flow: conversationContext.conversationFlow
      });
    }

    // Extract text from user message for greeting detection
    const queryText = userMessage.parts
      .filter(part => part.type === 'text')
      .map(part => (part as { type: 'text', text: string }).text)
      .join(' ');

    // Detect simple greetings and use simplified prompt
    const isSimpleGreeting = /^(hi|hello|hey|good morning|good afternoon|good evening|how are you|what's up)$/i.test(queryText.trim());

    // Add memories and conversation context to system prompt
    const baseSystemPrompt = getCoreSystemPrompt({
      selectedChatModel,
      conversationContext,
    });

    const systemPrompt = isSimpleGreeting
      ? "You are a helpful AI assistant. Respond to greetings in a friendly and professional manner."
      : baseSystemPrompt + memoriesContext;

    return createDataStreamResponse({
      execute: (dataStream) => {
        const result = streamText({
          model: myProvider.languageModel(selectedChatModel),
          system: systemPrompt,
          messages,
          maxSteps: 15, // Optimized: Reduced from 25 to 15 to prevent long loops and improve response time
          onStepFinish: ({ toolCalls }) => {
            // Log tool usage for monitoring
            if (toolCalls && toolCalls.length > 0) {
              console.log(`Step completed with ${toolCalls.length} tool calls`);
            }
          },
          experimental_activeTools:
            selectedChatModel === 'chat-model-reasoning' || isSimpleGreeting
              ? []
              : [
                  'getWeather',
                  'getCurrentTime',
                  'createDocument',
                  'updateDocument',
                  'requestSuggestions',
                  'searchInternet',
                  'extractPage',
                  'fetchActions',
                  'executeAction',
                  'googleCalendarAgent',
                  'remember',
                  'updateMemory',
                  'searchMemories',
                ],
          experimental_transform: smoothStream({ chunking: 'word' }),
          experimental_generateMessageId: generateUUID,
          tools: {
            getWeather,
            getCurrentTime,
            createDocument: createDocument({ session, dataStream }),
            updateDocument: updateDocument({ session, dataStream }),
            requestSuggestions: requestSuggestions({
              session,
              dataStream,
            }),
            searchInternet,
            extractPage,
            fetchActions,
            executeAction: enhancedExecuteAction,
            googleCalendarAgent,
            remember: createRememberTool({ session }),
            updateMemory: createUpdateMemoryTool({ session }),
            searchMemories: createSearchMemoriesTool({ session }),
          },
          onFinish: async ({ response }) => {
            if (session.user?.id) {
              try {
                const assistantId = getTrailingMessageId({
                  messages: response.messages.filter(
                    (message) => message.role === 'assistant',
                  ),
                });

                if (!assistantId) {
                  throw new Error('No assistant message found!');
                }

                const [, assistantMessage] = appendResponseMessages({
                  messages: [userMessage],
                  responseMessages: response.messages,
                });

                await saveMessages({
                  messages: [
                    {
                      id: assistantId,
                      chatId: id,
                      role: assistantMessage.role,
                      parts: assistantMessage.parts,
                      attachments:
                        assistantMessage.experimental_attachments ?? [],
                      createdAt: new Date(),
                    },
                  ],
                });
              } catch (_) {
                console.error('Failed to save chat');
              }
            }
          },
          experimental_telemetry: {
            isEnabled: isProductionEnvironment,
            functionId: 'stream-text',
          },
        });

        result.consumeStream();

        result.mergeIntoDataStream(dataStream, {
          sendReasoning: true,
        });
      },
      onError: (error) => {
        console.error('Stream error:', error);
        return 'Oops, an error occurred!';
      },
    });
  } catch (error) {
    console.error('Chat API error:', error);
    return new Response('An error occurred while processing your request!', {
      status: 500,
    });
  }
}



export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new Response('Not Found', { status: 404 });
  }

  const session = await auth();

  if (!session || !session.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const chat = await getChatById({ id });

    if (chat.userId !== session.user.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    await deleteChatById({ id });

    return new Response('Chat deleted', { status: 200 });
  } catch (error) {
    return new Response('An error occurred while processing your request!', {
      status: 500,
    });
  }
}
