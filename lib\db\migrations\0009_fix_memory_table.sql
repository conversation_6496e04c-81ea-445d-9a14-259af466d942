-- This migration fixes the Memory table by dropping and recreating it with the correct structure

-- Drop the existing Memory table
DROP TABLE IF EXISTS "Memory";

-- Create Memory table with text column for embeddings
CREATE TABLE IF NOT EXISTS "Memory" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "userId" uuid NOT NULL,
  "content" text NOT NULL,
  "embedding" text NOT NULL,
  "createdAt" timestamp DEFAULT now() NOT NULL,
  "isActive" boolean DEFAULT true NOT NULL,
  "category" text
);

-- Add foreign key constraint
DO $$ BEGIN
 ALTER TABLE "Memory" ADD CONSTRAINT "Memory_userId_User_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create regular index on userId for faster queries
CREATE INDEX IF NOT EXISTS "Memory_userId_idx" ON "Memory" ("userId");
