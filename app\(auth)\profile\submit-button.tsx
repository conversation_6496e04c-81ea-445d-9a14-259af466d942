'use client';

import { But<PERSON> } from '@/components/ui/button';
import { CheckIcon } from '@/components/icons';
import { memo } from 'react';

function SubmitButtonComponent({
  children,
  isSuccessful,
  isSubmitting,
}: {
  children: React.ReactNode;
  isSuccessful: boolean;
  isSubmitting?: boolean;
}) {
  return (
    <Button
      className="mt-4 w-full"
      type="submit"
      aria-disabled={isSubmitting}
      disabled={isSubmitting}
    >
      {isSuccessful ? (
        <div className="flex items-center gap-1">
          <CheckIcon />
          <span>Updated!</span>
        </div>
      ) : isSubmitting ? (
        <div className="flex items-center gap-1">
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
          <span>Updating...</span>
        </div>
      ) : (
        children
      )}
    </Button>
  );
}

// Memoize the button to prevent unnecessary re-renders
export const SubmitButton = memo(SubmitButtonComponent);
