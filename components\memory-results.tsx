'use client';

import { useState } from 'react';
import { BrainIcon } from 'lucide-react';
import { ToolUsageCard } from './ui/tool-usage-card';

interface MemoryResultsProps {
  memoryData: {
    success: boolean;
    message: string;
    memoryId?: string;
    memory?: string;
    error?: string;
    isExecuting?: boolean;
  };
}

export function MemoryResults({ memoryData }: MemoryResultsProps) {
  const [showDetails, setShowDetails] = useState(false);

  if (!memoryData) {
    // Loading state
    return (
      <ToolUsageCard
        icon={BrainIcon}
        toolName="Memory"
        status="loading"
        isExecuting={true}
      >
        <div className="space-y-2">
          <div className="h-5 w-3/4 animate-pulse rounded-md bg-muted"></div>
          <div className="h-4 w-full animate-pulse rounded-md bg-muted"></div>
        </div>
      </ToolUsageCard>
    );
  }

  // Handle error case
  if (!memoryData.success) {
    return (
      <ToolUsageCard
        icon={BrainIcon}
        toolName="Memory"
        status="error"
        errorMessage={memoryData.error || "Failed to store memory"}
        isExecuting={memoryData.isExecuting}
      >
        <div className="text-sm text-red-500">
          {memoryData.message}
        </div>
      </ToolUsageCard>
    );
  }

  // Create truncated preview (first 3-4 words + "..." but without closing quote)
  const truncatedMemory = memoryData.memory
    ? memoryData.memory.split(' ').slice(0, 4).join(' ') + (memoryData.memory.split(' ').length > 4 ? '...' : '')
    : '';

  return (
    <ToolUsageCard
      icon={BrainIcon}
      toolName="Memory"
      status="success"
      query={showDetails ? memoryData.memory : truncatedMemory}
      showContent={showDetails}
      onToggleContent={() => setShowDetails(!showDetails)}
      isExecuting={memoryData.isExecuting}
    />
  );
}
