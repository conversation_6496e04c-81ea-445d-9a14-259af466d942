{"name": "ai-chatbot", "version": "3.0.3", "private": true, "scripts": {"dev": "next dev --turbo", "build": "tsx lib/db/migrate && next build", "vercel-build": "tsx lib/db/migrate && next build", "start": "next start", "lint": "next lint && biome lint --write --unsafe", "lint:fix": "next lint --fix && biome lint --write --unsafe", "format": "biome format --write", "db:generate": "drizzle-kit generate", "db:migrate": "npx tsx lib/db/migrate.ts", "db:studio": "drizzle-kit studio", "db:push": "drizzle-kit push", "db:pull": "drizzle-kit pull", "db:check": "drizzle-kit check", "db:up": "drizzle-kit up", "test": "export PLAYWRIGHT=True && pnpm exec playwright test --workers=4"}, "dependencies": {"@ai-sdk/google": "^1.2.17", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@ai-sdk/xai": "^1.2.16", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/lang-python": "^6.2.0", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.7", "@google/generative-ai": "^0.24.1", "@libsql/client": "^0.14.0", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@radix-ui/react-visually-hidden": "^1.2.2", "@supabase/supabase-js": "^2.49.4", "@tavily/core": "^0.3.7", "@vercel/analytics": "^1.5.0", "@vercel/blob": "^0.24.1", "@vercel/postgres": "^0.10.0", "ai": "4.3.4", "bcrypt-ts": "^5.0.3", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "codemirror": "^6.0.1", "composio-core": "^0.5.39", "date-fns": "^4.1.0", "diff-match-patch": "^1.0.5", "dotenv": "^16.5.0", "drizzle-orm": "^0.34.1", "fast-deep-equal": "^3.1.3", "framer-motion": "^11.18.2", "geist": "^1.4.2", "libsql": "^0.4.7", "lucide-react": "^0.446.0", "nanoid": "^5.1.5", "next": "15.3.0-canary.31", "next-auth": "5.0.0-beta.25", "next-themes": "^0.3.0", "orderedmap": "^2.1.1", "papaparse": "^5.5.2", "postgres": "^3.4.5", "prosemirror-example-setup": "^1.2.3", "prosemirror-inputrules": "^1.5.0", "prosemirror-markdown": "^1.13.2", "prosemirror-model": "^1.25.1", "prosemirror-schema-basic": "^1.2.4", "prosemirror-schema-list": "^1.5.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.39.2", "react": "19.0.0-rc-45804af1-20241021", "react-data-grid": "7.0.0-beta.47", "react-dom": "19.0.0-rc-45804af1-20241021", "react-markdown": "^9.1.0", "react-resizable-panels": "^2.1.9", "remark-gfm": "^4.0.1", "server-only": "^0.0.1", "sonner": "^1.7.4", "swr": "^2.3.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.1", "zod": "^3.24.4"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@playwright/test": "^1.52.0", "@tailwindcss/typography": "^0.5.16", "@types/d3-scale": "^4.0.9", "@types/node": "^22.15.17", "@types/papaparse": "^5.3.16", "@types/pdf-parse": "^1.1.5", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "drizzle-kit": "^0.25.0", "eslint": "^8.57.1", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.10.1", "eslint-plugin-tailwindcss": "^3.18.0", "ignore-loader": "^0.1.2", "null-loader": "^4.0.1", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "packageManager": "pnpm@9.12.3", "pnpm": {"overrides": {"@libsql/client": "0.14.0", "libsql": "0.4.7"}}}