/**
 * Interactive Tool Experience Generator with Embeddings
 * Run with: node generate-tool-experiences.js
 */

const { GoogleGenerativeAI } = require('@google/generative-ai');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

// Initialize Google AI
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * Generate embedding for text using Google's embedding model
 */
async function generateEmbedding(text) {
  try {
    const model = genAI.getGenerativeModel({ model: 'embedding-001' });
    const result = await model.embedContent(text);
    return result.embedding.values;
  } catch (error) {
    console.error('Error generating embedding:', error);
    throw error;
  }
}

/**
 * Create optimized embedding text for tool experiences
 */
function createEmbeddingText(experience) {
  return [
    experience.experience_type,
    experience.app_name,
    experience.experience_content,
    experience.priority
  ].filter(Boolean).join(' ');
}

/**
 * Validate experience object structure
 */
function validateExperience(exp, index) {
  const required = ['experience_type', 'app_name', 'experience_content', 'success_rate', 'priority'];
  const missing = required.filter(field => !exp.hasOwnProperty(field));

  if (missing.length > 0) {
    throw new Error(`Experience ${index + 1} missing required fields: ${missing.join(', ')}`);
  }

  // Validate experience_type
  if (!['workflow', 'pattern', 'insight', 'advice', 'parameter_combo'].includes(exp.experience_type)) {
    throw new Error(`Experience ${index + 1} has invalid experience_type: ${exp.experience_type}`);
  }

  // Validate priority
  if (!['critical', 'high', 'medium', 'low'].includes(exp.priority)) {
    throw new Error(`Experience ${index + 1} has invalid priority: ${exp.priority}`);
  }

  // Validate success_rate
  if (typeof exp.success_rate !== 'number' || exp.success_rate < 0 || exp.success_rate > 1) {
    throw new Error(`Experience ${index + 1} has invalid success_rate: ${exp.success_rate} (must be between 0 and 1)`);
  }

  return true;
}

/**
 * Get user input for experiences
 */
function getUserInput() {
  return new Promise((resolve) => {
    console.log('\n🎯 Tool Experience Generator');
    console.log('=' * 50);
    console.log('\n📋 Please paste your AI-generated experiences in the following format:');
    console.log('\n📝 Required JSON Array Format:');
    console.log(`[
  {
    "experience_type": "insight|workflow|pattern|advice|parameter_combo",
    "app_name": "github|gmail|notion|jira|googlecalendar",
    "experience_content": "Detailed, actionable guidance",
    "success_rate": 0.88,
    "priority": "critical|high|medium|low"
  }
]`);

    console.log('\n📥 Paste your experiences JSON array and press Enter twice when done:');
    console.log('(Type "exit" to quit)\n');

    let input = '';
    let emptyLineCount = 0;

    rl.on('line', (line) => {
      if (line.trim() === 'exit') {
        rl.close();
        process.exit(0);
      }

      if (line.trim() === '') {
        emptyLineCount++;
        if (emptyLineCount >= 2) {
          rl.removeAllListeners('line');
          resolve(input.trim());
          return;
        }
      } else {
        emptyLineCount = 0;
      }

      input += line + '\n';
    });
  });
}

/**
 * Create output directory
 */
function createOutputDirectory() {
  const outputDir = path.join(__dirname, 'tool-experiences-extracted');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
    console.log(`📁 Created directory: ${outputDir}`);
  }
  return outputDir;
}

/**
 * Main function to process experiences and generate SQL
 */
async function main() {
  try {
    // Get user input
    const userInput = await getUserInput();

    if (!userInput) {
      console.log('❌ No input provided. Exiting...');
      rl.close();
      return;
    }

    // Parse JSON
    let toolExperiences;
    try {
      toolExperiences = JSON.parse(userInput);
    } catch (error) {
      console.log('❌ Invalid JSON format. Please check your input and try again.');
      console.log('Error:', error.message);
      rl.close();
      return;
    }

    if (!Array.isArray(toolExperiences)) {
      console.log('❌ Input must be a JSON array. Please check your format.');
      rl.close();
      return;
    }

    console.log(`\n✅ Parsed ${toolExperiences.length} experiences`);

    // Validate experiences
    console.log('\n🔍 Validating experiences...');
    for (let i = 0; i < toolExperiences.length; i++) {
      validateExperience(toolExperiences[i], i);
    }
    console.log('✅ All experiences validated successfully');

    // Create output directory
    const outputDir = createOutputDirectory();

    // Generate SQL with embeddings
    console.log('\n🚀 Generating embeddings and SQL...');

    let sqlOutput = `-- Tool Experiences with Vector Embeddings
-- Generated on ${new Date().toISOString()}
-- Total experiences: ${toolExperiences.length}
-- Run this SQL in Supabase SQL Editor

-- Clear existing data (optional)
-- DELETE FROM "ToolExperiences" WHERE phase_category IS NOT NULL;

-- Insert optimized tool experiences with embeddings

`;

    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < toolExperiences.length; i++) {
      const exp = toolExperiences[i];

      try {
        console.log(`Processing ${i + 1}/${toolExperiences.length}: ${exp.app_name}`);

        // Create optimized embedding text
        const embeddingText = createEmbeddingText(exp);

        // Generate embedding
        const embedding = await generateEmbedding(embeddingText);
        console.log(`  ✅ Generated embedding (${embedding.length} dimensions)`);

        // Format embedding as PostgreSQL vector
        const embeddingVector = `[${embedding.join(',')}]`;

        // Generate SQL INSERT statement
        sqlOutput += `
INSERT INTO "ToolExperiences" (
  "experience_type",
  "app_name",
  "experience_content",
  "embedding",
  "success_rate",
  "priority",
  "isActive"
) VALUES (
  '${exp.experience_type}',
  '${exp.app_name}',
  '${exp.experience_content.replace(/'/g, "''")}',
  '${embeddingVector}',
  ${exp.success_rate},
  '${exp.priority}',
  true
);
`;

        successCount++;

        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 200));

      } catch (error) {
        console.error(`  ❌ Error processing experience ${i + 1}:`, error.message);
        errorCount++;
      }
    }

    // Add verification queries
    sqlOutput += `
-- Verify the data
SELECT
  experience_type,
  app_name,
  LEFT(experience_content, 50) as content_preview,
  success_rate,
  priority,
  "createdAt"
FROM "ToolExperiences"
WHERE "isActive" = true
ORDER BY "createdAt" DESC;

-- Check embedding status
SELECT
  app_name,
  experience_type,
  COUNT(*) as total_experiences,
  COUNT(CASE WHEN embedding IS NOT NULL THEN 1 END) as with_embeddings,
  ROUND(AVG(success_rate)::numeric, 3) as avg_success_rate
FROM "ToolExperiences"
WHERE "isActive" = true
GROUP BY app_name, experience_type;

-- Priority distribution
SELECT
  priority,
  COUNT(*) as count,
  ROUND(AVG(success_rate)::numeric, 3) as avg_success_rate
FROM "ToolExperiences"
WHERE "isActive" = true
GROUP BY priority
ORDER BY
  CASE priority
    WHEN 'critical' THEN 1
    WHEN 'high' THEN 2
    WHEN 'medium' THEN 3
    WHEN 'low' THEN 4
  END;
`;

    // Write to file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `tool-experiences-${timestamp}.sql`;
    const filepath = path.join(outputDir, filename);

    fs.writeFileSync(filepath, sqlOutput);

    console.log('\n📊 Generation Summary:');
    console.log('=' * 50);
    console.log(`✅ Successfully processed: ${successCount} experiences`);
    console.log(`❌ Errors: ${errorCount} experiences`);
    console.log(`📁 SQL file saved: ${filepath}`);
    console.log(`📂 Output directory: ${outputDir}`);

    // Show app distribution
    const appStats = toolExperiences.reduce((acc, exp) => {
      acc[exp.app_name] = (acc[exp.app_name] || 0) + 1;
      return acc;
    }, {});

    console.log('\n📈 App Distribution:');
    Object.entries(appStats).forEach(([app, count]) => {
      console.log(`  ${app}: ${count} experiences`);
    });

    console.log('\n🎯 Next steps:');
    console.log('1. Review the generated SQL file');
    console.log('2. Run the SQL in Supabase SQL Editor');
    console.log('3. Test the AI agent with improved experiences');
    console.log('4. Monitor similarity scores and task success rates');

  } catch (error) {
    console.error('\n❌ Fatal error:', error.message);
  } finally {
    rl.close();
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n👋 Process interrupted. Goodbye!');
  rl.close();
  process.exit(0);
});

// Run the script
main().catch(console.error);
