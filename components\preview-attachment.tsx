import type { Attachment } from 'ai';
import {
  LoaderIcon,
  FileIcon,
  FileTextIcon,
  FileImageIcon,
  FileCodeIcon,
  FilePdfIcon,
  CrossIcon
} from './icons';
import { useState, useEffect } from 'react';
import { Button } from './ui/button';

export const PreviewAttachment = ({
  attachment,
  isUploading = false,
  onRemove,
}: {
  attachment: Attachment;
  isUploading?: boolean;
  onRemove?: (attachment: Attachment) => void;
}) => {
  const { name, url, contentType } = attachment;
  const [isImageLoading, setIsImageLoading] = useState(contentType?.startsWith('image/') ?? false);
  const [isHovered, setIsHovered] = useState(false);

  // Reset loading state when URL changes
  useEffect(() => {
    if (contentType?.startsWith('image/') && url) {
      setIsImageLoading(true);
    }
  }, [url, contentType]);

  // Function to get the appropriate icon based on file type
  const getFileIcon = () => {
    if (!contentType) return <FileIcon size={24} />;

    if (contentType.startsWith('image/')) {
      return <FileImageIcon size={24} />;
    } else if (contentType.includes('pdf')) {
      return <FilePdfIcon size={24} />;
    } else if (contentType.includes('text/plain')) {
      return <FileTextIcon size={24} />;
    } else if (contentType.includes('application/json') ||
               contentType.includes('text/javascript') ||
               contentType.includes('text/html') ||
               contentType.includes('text/css')) {
      return <FileCodeIcon size={24} />;
    } else if (contentType.includes('document') ||
               contentType.includes('msword') ||
               contentType.includes('wordprocessing')) {
      return <FileTextIcon size={24} />;
    } else {
      return <FileIcon size={24} />;
    }
  };

  return (
    <div
      data-testid="input-attachment-preview"
      className="flex flex-col gap-2"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="w-20 h-16 aspect-video bg-muted rounded-md relative flex flex-col items-center justify-center overflow-hidden">
        {contentType && contentType.startsWith('image') ? (
          <>
            {/* Show placeholder while image is loading */}
            {isImageLoading && (
              <div className="flex items-center justify-center text-zinc-500 h-full w-full absolute inset-0 z-10">
                <FileImageIcon size={24} />
              </div>
            )}

            {/* Actual image with onLoad handler */}
            {/* NOTE: it is recommended to use next/image for images */}
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img
              key={url}
              src={url}
              alt={name ?? 'An image attachment'}
              className="rounded-md size-full object-cover transition-opacity duration-300"
              style={{ opacity: isImageLoading ? 0 : 1 }}
              onLoad={() => setIsImageLoading(false)}
              onError={() => setIsImageLoading(false)}
            />
          </>
        ) : (
          <div className="flex items-center justify-center text-zinc-500 h-full w-full">
            {getFileIcon()}
          </div>
        )}

        {/* Show loader when uploading */}
        {(isUploading || isImageLoading) && (
          <div
            data-testid="input-attachment-loader"
            className="animate-spin absolute text-zinc-500 z-20"
          >
            <LoaderIcon />
          </div>
        )}

        {/* Remove button */}
        {onRemove && !isUploading && isHovered && (
          <Button
            data-testid="remove-attachment-button"
            className="absolute top-1 right-1 p-1 h-fit w-fit rounded-full bg-zinc-800/70 hover:bg-red-600 text-white z-30"
            onClick={(e) => {
              e.stopPropagation();
              onRemove(attachment);
            }}
            variant="ghost"
            size="sm"
          >
            <CrossIcon size={12} />
          </Button>
        )}
      </div>
      <div className="text-xs text-zinc-500 max-w-16 truncate">{name}</div>
    </div>
  );
};
