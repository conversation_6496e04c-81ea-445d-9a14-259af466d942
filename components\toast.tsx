'use client';

import React, { ReactNode } from 'react';
import { toast as sonnerToast } from 'sonner';
import { CheckCircleFillIcon, WarningIcon } from './icons';

const iconsByType: Record<'success' | 'error', ReactNode> = {
  success: <CheckCircleFillIcon />,
  error: <WarningIcon />,
};

export function toast(props: Omit<ToastProps, 'id'>) {
  return sonnerToast.custom((id) => (
    <Toast id={id} type={props.type} description={props.description} />
  ));
}

function Toast(props: ToastProps) {
  const { id, type, description } = props;

  return (
    <div className="flex w-full toast-mobile:w-[356px] justify-center">
      <div
        data-testid="toast"
        key={id}
        className="bg-white dark:bg-zinc-800 shadow-md p-3 rounded-md w-full toast-mobile:w-fit flex flex-row gap-3 items-center border-l-4 data-[type=error]:border-red-500 data-[type=success]:border-green-500"
        data-type={type}
      >
        <div
          className="data-[type=error]:text-red-500 data-[type=success]:text-green-500"
          data-type={type}
        >
          {iconsByType[type]}
        </div>
        <div className="text-zinc-900 dark:text-zinc-100 text-sm font-medium">{description}</div>
      </div>
    </div>
  );
}

interface ToastProps {
  id: string | number;
  type: 'success' | 'error';
  description: string | ReactNode;
}
