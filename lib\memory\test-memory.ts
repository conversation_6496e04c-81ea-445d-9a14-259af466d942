/**
 * Test script for the memory system
 * Run with: npx tsx lib/memory/test-memory.ts
 */
import { config } from 'dotenv';
import { createMemory, getRelevantMemories, getAllMemories } from './queries';
import { generateEmbedding } from './embeddings';

// Load environment variables
config({
  path: '.env.local',
});

// Test user ID - replace with a valid user ID from your database
const TEST_USER_ID = ''; // You'll need to fill this in with a real user ID

async function testMemorySystem() {
  if (!TEST_USER_ID) {
    console.error('Please set a valid TEST_USER_ID in the script');
    process.exit(1);
  }

  try {
    console.log('🧠 Testing Memory System...');

    // Test 1: Generate an embedding
    console.log('\n1. Testing embedding generation...');
    const testText = 'The user prefers dark mode in all applications';
    const embedding = await generateEmbedding(testText);
    console.log(`✅ Successfully generated embedding with ${embedding.length} dimensions`);

    // Test 2: Create a memory
    console.log('\n2. Creating test memories...');
    const memories = [
      {
        content: 'The user prefers dark mode in all applications',
        category: 'preferences',
      },
      {
        content: 'The user\'s name is Anas',
        category: 'personal',
      },
      {
        content: 'The user is working on an AI chatbot project',
        category: 'work',
      },
      {
        content: 'The user prefers clean, minimalist UI designs',
        category: 'preferences',
      },
      {
        content: 'The user is interested in vector databases and AI memory systems',
        category: 'interests',
      },
    ];

    for (const memory of memories) {
      const result = await createMemory({
        userId: TEST_USER_ID,
        content: memory.content,
        category: memory.category,
      });
      console.log(`✅ Created memory: "${memory.content}" (${memory.category})`);
    }

    // Test 3: Retrieve all memories
    console.log('\n3. Retrieving all memories...');
    const allMemories = await getAllMemories({ userId: TEST_USER_ID });
    console.log(`✅ Retrieved ${allMemories.length} memories`);

    // Test 4: Retrieve relevant memories
    console.log('\n4. Testing memory retrieval with different queries...');
    
    const queries = [
      'What are the user\'s UI preferences?',
      'What is the user\'s name?',
      'What projects is the user working on?',
      'What is the user interested in?',
    ];

    for (const query of queries) {
      console.log(`\nQuery: "${query}"`);
      const relevantMemories = await getRelevantMemories({
        userId: TEST_USER_ID,
        query,
        limit: 2,
      });

      console.log('Relevant memories:');
      relevantMemories.forEach((memory: any, index: number) => {
        console.log(`  ${index + 1}. ${memory.content} (${memory.category})`);
      });
    }

    console.log('\n✅ Memory system tests completed successfully!');
  } catch (error) {
    console.error('❌ Error testing memory system:', error);
  }
}

// Run the test
testMemorySystem();
