import { OpenAIToolSet } from "composio-core";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    console.log("Executing GitHub action...");
    
    // Initialize the Composio toolset
    const toolset = new OpenAIToolSet();
    console.log("Toolset initialized");
    
    // Get the entity for the user
    const userIdentifier = "default_user";
    console.log("Getting entity for user:", userIdentifier);
    const entity = await toolset.getEntity(userIdentifier);
    console.log("Entity retrieved");
    
    // Check if the entity has a GitHub connection
    console.log("Checking GitHub connection");
    const connections = await entity.getConnections();
    const githubConnection = connections.find(conn => conn.appName === "github");
    
    if (!githubConnection) {
      console.log("No GitHub connection found");
      return NextResponse.json({
        success: false,
        message: "GitHub connection not found. Please connect your GitHub account first.",
      }, { status: 400 });
    }
    
    console.log("GitHub connection found, status:", githubConnection.status);
    
    if (githubConnection.status !== "ACTIVE") {
      console.log("GitHub connection is not active");
      return NextResponse.json({
        success: false,
        message: `GitHub connection is not active (status: ${githubConnection.status}). Please reconnect your GitHub account.`,
      }, { status: 400 });
    }
    
    // Execute a simple GitHub action - get authenticated user info
    console.log("Executing GitHub action: Get authenticated user");
    
    // Direct execution approach
    const result = await toolset.executeAction({
      action: "GITHUB_GET_THE_AUTHENTICATED_USER",
      params: {},
      entityId: userIdentifier
    });
    
    console.log("GitHub action executed successfully");
    console.log("Result:", result);
    
    return NextResponse.json({
      success: true,
      message: "GitHub action executed successfully",
      result: result,
      username: result.login,
      name: result.name || result.login,
      avatarUrl: result.avatar_url,
      profileUrl: result.html_url,
    });
  } catch (error) {
    console.error("Error executing GitHub action:", error);
    
    return NextResponse.json({
      success: false,
      message: "Failed to execute GitHub action",
      error: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}
