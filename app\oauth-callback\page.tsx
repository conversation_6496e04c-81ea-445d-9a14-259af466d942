'use client';

import { useEffect, useState } from 'react';
import { LoaderIcon } from '@/components/icons';

export default function OAuthCallbackPage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    // Check URL for error parameters
    const url = new URL(window.location.href);
    const errorParam = url.searchParams.get('error');
    const statusParam = url.searchParams.get('status');
    const appName = url.searchParams.get('appName') || 'service';

    const hasError = errorParam || statusParam === 'error';

    // Send a message to the opener window with the OAuth flow result
    if (window.opener) {
      if (hasError) {
        // Send error message
        window.opener.postMessage({
          type: 'OAUTH_ERROR',
          error: errorParam || 'Authorization was canceled or failed',
          appName: appName
        }, window.location.origin);

        // Set error status and message
        setStatus('error');
        setErrorMessage(errorParam || `Failed to connect to ${appName}`);

        // Close the window automatically after 5 seconds
        setTimeout(() => {
          window.close();
        }, 5000);
      } else {
        // Send success message
        window.opener.postMessage({
          type: 'OAUTH_COMPLETE',
          appName: appName
        }, window.location.origin);

        // Set success status after a short delay
        setTimeout(() => {
          setStatus('success');
          // Close the window automatically after 3 seconds
          setTimeout(() => {
            window.close();
          }, 3000);
        }, 1000);
      }
    } else {
      // If there's no opener, something went wrong
      setStatus('error');
    }
  }, []);

  return (
    <div className="flex min-h-screen w-full items-center justify-center py-8 px-4 bg-background">
      <div className="w-full max-w-md bg-card shadow-sm rounded-lg border flex flex-col gap-6 p-8 text-center">
        {status === 'loading' && (
          <>
            <div className="flex justify-center">
              <div className="animate-spin h-12 w-12 rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
            <h3 className="text-xl font-semibold">Completing connection...</h3>
            <p className="text-sm text-muted-foreground">
              Please wait while we finish setting up your connection.
            </p>
          </>
        )}

        {status === 'success' && (
          <>
            <div className="flex justify-center">
              <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-green-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-semibold text-green-600">Connection Successful!</h3>
            <p className="text-sm text-muted-foreground">
              Your account has been connected successfully. This window will close automatically.
            </p>
          </>
        )}

        {status === 'error' && (
          <>
            <div className="flex justify-center">
              <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-red-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
            </div>
            <h3 className="text-xl font-semibold text-red-600">Connection Error</h3>
            <p className="text-sm text-muted-foreground">
              {errorMessage || "There was an error completing your connection."} Please close this window and try again.
            </p>
          </>
        )}
      </div>
    </div>
  );
}
