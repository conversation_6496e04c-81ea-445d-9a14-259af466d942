# Composio Integration Setup Guide

This guide explains how to set up integrations with Composio for various providers like GitHub, Gmail, Google Calendar, Notion, and Jira.

## Prerequisites

1. A Composio account with an API key
2. Node.js installed on your machine
3. The Composio API key added to your `.env.local` file

## Setting Up Integrations

### Step 1: Create Integrations in Composio

You can create integrations in two ways:

#### Option 1: Using the Composio Dashboard (Recommended)

1. Log in to your [Composio Dashboard](https://app.composio.dev/)
2. Navigate to the Integrations section
3. Click "Create Integration" for each provider you want to add
4. Copy the integration IDs and add them to your `.env.local` file

#### Option 2: Using the Provided Script

We've created a script to help you create integrations programmatically:

```bash
# Install dependencies if you haven't already
npm install composio-core dotenv

# Run the script for each provider
node scripts/create-composio-integration.js github
node scripts/create-composio-integration.js gmail
node scripts/create-composio-integration.js googlecalendar
node scripts/create-composio-integration.js notion
node scripts/create-composio-integration.js jira
```

### Step 2: Add Integration IDs to Environment Variables

Add the integration IDs to your `.env.local` file:

```
# Composio Integration IDs
COMPOSIO_GITHUB_INTEGRATION_ID=your_github_integration_id
COMPOSIO_GMAIL_INTEGRATION_ID=your_gmail_integration_id
COMPOSIO_GOOGLECALENDAR_INTEGRATION_ID=your_googlecalendar_integration_id
COMPOSIO_NOTION_INTEGRATION_ID=your_notion_integration_id
COMPOSIO_JIRA_INTEGRATION_ID=your_jira_integration_id
```

### Step 3: Test the Connections

You can test the connections using the provided script:

```bash
# Test each provider
node scripts/test-composio-connection.js github
node scripts/test-composio-connection.js gmail
node scripts/test-composio-connection.js googlecalendar
node scripts/test-composio-connection.js notion
node scripts/test-composio-connection.js jira
```

## Using Custom OAuth Credentials (Optional)

If you want to use your own OAuth credentials instead of Composio's, you can add them to your `.env.local` file:

```
# GitHub OAuth Credentials
OAUTH_GITHUB_CLIENT_ID=your_github_client_id
OAUTH_GITHUB_CLIENT_SECRET=your_github_client_secret

# Gmail OAuth Credentials
OAUTH_GMAIL_CLIENT_ID=your_gmail_client_id
OAUTH_GMAIL_CLIENT_SECRET=your_gmail_client_secret

# Google Calendar OAuth Credentials
OAUTH_GOOGLECALENDAR_CLIENT_ID=your_googlecalendar_client_id
OAUTH_GOOGLECALENDAR_CLIENT_SECRET=your_googlecalendar_client_secret

# Notion OAuth Credentials
OAUTH_NOTION_CLIENT_ID=your_notion_client_id
OAUTH_NOTION_CLIENT_SECRET=your_notion_client_secret

# Jira OAuth Credentials
OAUTH_JIRA_CLIENT_ID=your_jira_client_id
OAUTH_JIRA_CLIENT_SECRET=your_jira_client_secret
```

Then run the create-integration script again to create integrations with your custom credentials.

## Troubleshooting

### Connection Issues

If you encounter connection issues:

1. Check that your integration IDs are correct in `.env.local`
2. Verify that your Composio API key is valid
3. Make sure the callback URL is set correctly in your OAuth app settings
4. Check the server logs for detailed error messages

### OAuth Callback Issues

If the OAuth callback isn't working:

1. Make sure your app is running on the same URL that you configured in the OAuth app settings
2. Check that the callback URL is set to `/oauth-callback`
3. Verify that your OAuth credentials are correct

## Additional Resources

- [Composio Documentation](https://docs.composio.dev/)
- [OAuth Connection Flow](https://docs.composio.dev/auth/connection/oauth)
- [White-labeling OAuth](https://docs.composio.dev/auth/white-labelling)
