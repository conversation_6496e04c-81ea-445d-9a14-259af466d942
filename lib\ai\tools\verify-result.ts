import { z } from 'zod';
import { tool } from '@ai-sdk/tools';

/**
 * Verify Result Tool
 * 
 * This tool verifies if an action result satisfies the original user intent.
 * It's part of the VIPER (Verification-Integrated Proactive Execution and Recovery) framework.
 */
export const verifyResult = tool({
  description: 'Verify if an action result satisfies the original intent. Use this to check if the result meets the user\'s expectations.',
  parameters: z.object({
    intent: z.string().describe('The user\'s original intent/goal (e.g., "get commits from branch X in repo Y")'),
    result: z.any().describe('The result to verify'),
    criteria: z.array(z.string()).optional().describe('Specific criteria to check (e.g., ["branch name", "commit messages"])'),
    expectedCount: z.number().optional().describe('Expected number of items in the result (if applicable)'),
  }),
  async handler({ intent, result, criteria = [], expectedCount }) {
    console.log(`Verifying result for intent: ${intent}`);
    
    // Initialize verification object
    const verification = {
      success: true,
      missingElements: [] as string[],
      suggestions: [] as string[],
      details: {} as Record<string, any>
    };
    
    // Check if result is empty or error
    if (!result || (typeof result === 'object' && Object.keys(result).length === 0)) {
      verification.success = false;
      verification.missingElements.push('complete_data');
      verification.suggestions.push('try_alternative_action');
      verification.details.emptyResult = true;
    }
    
    // Check if result is an array and has expected count
    if (Array.isArray(result) && expectedCount !== undefined) {
      verification.details.actualCount = result.length;
      verification.details.expectedCount = expectedCount;
      
      if (result.length < expectedCount) {
        verification.success = false;
        verification.missingElements.push('expected_count');
        verification.suggestions.push('fetch_more_items');
        verification.details.countMismatch = true;
      }
    }
    
    // Check against specific criteria if provided
    if (criteria.length > 0) {
      const resultStr = JSON.stringify(result).toLowerCase();
      const missingCriteria = [];
      
      for (const criterion of criteria) {
        const criterionLower = criterion.toLowerCase();
        const hasCriterion = resultStr.includes(criterionLower);
        
        if (!hasCriterion) {
          missingCriteria.push(criterion);
          verification.success = false;
        }
      }
      
      if (missingCriteria.length > 0) {
        verification.missingElements.push(...missingCriteria);
        verification.suggestions.push('try_more_specific_action');
        verification.details.missingCriteria = missingCriteria;
      }
    }
    
    // Check for specific intents
    if (intent.toLowerCase().includes('branch')) {
      // For branch-related queries, check if branch information is present
      const hasBranchInfo = JSON.stringify(result).toLowerCase().includes('branch');
      
      if (!hasBranchInfo) {
        verification.success = false;
        verification.missingElements.push('branch_information');
        verification.suggestions.push('try_branch_specific_action');
        verification.details.missingBranchInfo = true;
      }
    }
    
    if (intent.toLowerCase().includes('commit')) {
      // For commit-related queries, check if commit information is present
      const hasCommitInfo = JSON.stringify(result).toLowerCase().includes('commit') || 
                           JSON.stringify(result).toLowerCase().includes('sha');
      
      if (!hasCommitInfo) {
        verification.success = false;
        verification.missingElements.push('commit_information');
        verification.suggestions.push('try_commit_specific_action');
        verification.details.missingCommitInfo = true;
      }
    }
    
    // Return the verification result
    return {
      verified: verification.success,
      missingElements: verification.missingElements,
      suggestions: verification.suggestions,
      details: verification.details
    };
  }
});
