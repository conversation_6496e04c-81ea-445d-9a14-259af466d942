import 'server-only';
import { sql } from 'drizzle-orm';
import { and, eq } from 'drizzle-orm';
import { db, withRetry } from '@/lib/db/client';
import { memory } from '@/lib/db/schema';
import { generateEmbedding } from './embeddings';

/**
 * Creates a new memory for a user
 * @param userId The user ID
 * @param content The memory content
 * @param category Optional category for the memory
 * @returns The created memory
 */
export async function createMemory({
  userId,
  content,
  category,
}: {
  userId: string;
  content: string;
  category?: string;
}) {
  try {
    // Generate embedding for the memory content
    const embedding = await generateEmbedding(content);

    // Insert the memory into the database
    console.log(`Creating memory with ${embedding.length} dimensions`);

    return await withRetry(async () => {
      // Use SQL to insert with proper vector type
      return await db.execute(sql`
        INSERT INTO "Memory" (
          "id",
          "userId",
          "content",
          "embedding",
          "createdAt",
          "isActive",
          "category"
        )
        VALUES (
          gen_random_uuid(),
          ${userId},
          ${content},
          ${JSON.stringify(embedding)}::vector,
          NOW(),
          true,
          ${category || null}
        )
        RETURNING *
      `);
    });
  } catch (error) {
    console.error('Failed to create memory:', error);
    throw error;
  }
}

/**
 * Analyzes query intent to improve memory retrieval
 * @param query The user query
 * @returns Intent analysis with keywords and context
 */
async function analyzeQueryIntent(query: string) {
  try {
    // Extract key concepts and intent from the query
    const keywords = query.toLowerCase()
      .split(/\s+/)
      .filter(word => word.length > 2)
      .filter(word => !['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'man', 'way', 'she', 'use', 'her', 'how', 'its', 'our', 'out', 'two', 'way', 'who', 'oil', 'sit', 'set', 'run', 'eat', 'far', 'sea', 'eye'].includes(word));

    // Determine query intent categories
    const intentCategories = [];

    // Check for preference-related queries
    if (/prefer|like|want|need|favorite|choose|select|option/.test(query.toLowerCase())) {
      intentCategories.push('preferences');
    }

    // Check for factual information queries
    if (/what|where|when|who|how|tell me|information|details/.test(query.toLowerCase())) {
      intentCategories.push('facts');
    }

    // Check for task/work-related queries
    if (/project|task|work|job|meeting|deadline|schedule/.test(query.toLowerCase())) {
      intentCategories.push('work');
    }

    // Check for personal information queries
    if (/name|personal|family|background|about me/.test(query.toLowerCase())) {
      intentCategories.push('personal');
    }

    return {
      keywords,
      intentCategories,
      isPreferenceQuery: intentCategories.includes('preferences'),
      isFactualQuery: intentCategories.includes('facts'),
      isWorkQuery: intentCategories.includes('work'),
      isPersonalQuery: intentCategories.includes('personal')
    };
  } catch (error) {
    console.error('Error analyzing query intent:', error);
    return {
      keywords: query.toLowerCase().split(/\s+/),
      intentCategories: [],
      isPreferenceQuery: false,
      isFactualQuery: false,
      isWorkQuery: false,
      isPersonalQuery: false
    };
  }
}

/**
 * Retrieves relevant memories for a user based on a query with intent analysis
 * @param userId The user ID
 * @param query The query to find relevant memories for
 * @param limit Maximum number of memories to return
 * @param similarityThreshold Minimum similarity threshold (default: 0.3)
 * @param context Optional context for better retrieval
 * @returns Array of relevant memories
 */
export async function getRelevantMemories({
  userId,
  query,
  limit = 5,
  similarityThreshold = 0.3,
  context,
}: {
  userId: string;
  query: string;
  limit?: number;
  similarityThreshold?: number;
  context?: string;
}) {
  try {
    // Add a timestamp to ensure we're not getting cached results
    const timestamp = new Date().getTime();

    // Analyze query intent for better retrieval
    const intentAnalysis = await analyzeQueryIntent(query);
    console.log(`Query intent analysis:`, intentAnalysis);

    // Create enhanced query for embedding generation
    let enhancedQuery = query;
    if (context) {
      enhancedQuery = `${query} ${context}`;
    }

    // Add intent-based keywords to improve matching
    if (intentAnalysis.intentCategories.length > 0) {
      enhancedQuery += ` ${intentAnalysis.intentCategories.join(' ')}`;
    }

    console.log(`Original query: "${query}"`);
    console.log(`Enhanced query: "${enhancedQuery}"`);

    // Generate embedding for the enhanced query
    const queryEmbedding = await generateEmbedding(enhancedQuery + ` (${timestamp})`);
    console.log(`Generated query embedding with ${queryEmbedding.length} dimensions`);

    // First, get all memories to analyze their similarity distribution
    const allMemories = await withRetry(async () => {
      return await db.execute(sql`
        SELECT
          m.id,
          m.content,
          m.category,
          m."createdAt",
          m."isActive",
          1 - (m.embedding <=> ${JSON.stringify(queryEmbedding)}::vector) AS similarity
        FROM
          "Memory" m
        WHERE
          m."userId" = ${userId}
          AND m."isActive" = true
        ORDER BY
          similarity DESC
      `);
    });

    // Analyze similarity distribution to set a dynamic threshold if needed
    let effectiveThreshold = similarityThreshold;

    // Adjust threshold based on intent analysis
    if (intentAnalysis.isPreferenceQuery) {
      effectiveThreshold = Math.max(0.2, similarityThreshold - 0.1); // Lower threshold for preferences
    } else if (intentAnalysis.isPersonalQuery) {
      effectiveThreshold = Math.max(0.25, similarityThreshold - 0.05); // Slightly lower for personal info
    }

    if (allMemories.length > 0) {
      // Log all similarities for analysis
      console.log("All memory similarities:");
      allMemories.forEach((mem: any, index: number) => {
        console.log(`${index+1}. "${mem.content.substring(0, 30)}..." - Similarity: ${mem.similarity.toFixed(4)}`);
      });

      // Apply intent-based boosting instead of just keyword boosting
      const intentBoost = getIntentBasedBoost(intentAnalysis, allMemories);

      // Apply intent-based boosting to adjust similarities
      const boostedMemories = allMemories.map((mem: any) => {
        const boost = intentBoost[mem.id] || 0;
        return {
          ...mem,
          originalSimilarity: mem.similarity,
          similarity: mem.similarity + boost
        };
      });

      // Sort by boosted similarity
      boostedMemories.sort((a: any, b: any) => b.similarity - a.similarity);

      // Take the top results based on limit
      const topMemories = boostedMemories.slice(0, limit);

      console.log(`Found ${topMemories.length} relevant memories after intent-based boosting`);

      // Log each memory and its similarity for debugging
      topMemories.forEach((mem: any) => {
        const boostInfo = mem.originalSimilarity !== mem.similarity
          ? ` (original: ${mem.originalSimilarity.toFixed(4)})`
          : '';
        console.log(`Memory: "${mem.content.substring(0, 30)}..." - Similarity: ${mem.similarity.toFixed(4)}${boostInfo}`);
      });

      return topMemories;
    }

    // Fallback to original method if no memories or analysis fails
    const memories = await withRetry(async () => {
      return await db.execute(sql`
        SELECT
          m.id,
          m.content,
          m.category,
          m."createdAt",
          m."isActive",
          1 - (m.embedding <=> ${JSON.stringify(queryEmbedding)}::vector) AS similarity
        FROM
          "Memory" m
        WHERE
          m."userId" = ${userId}
          AND m."isActive" = true
          AND 1 - (m.embedding <=> ${JSON.stringify(queryEmbedding)}::vector) >= ${effectiveThreshold}
        ORDER BY
          similarity DESC
        LIMIT
          ${limit}
      `);
    });

    console.log(`Found ${memories.length} relevant memories with similarity >= ${effectiveThreshold}`);

    // Log each memory and its similarity for debugging
    memories.forEach((mem: any) => {
      console.log(`Memory: "${mem.content.substring(0, 30)}..." - Similarity: ${mem.similarity.toFixed(4)}`);
    });

    return memories;
  } catch (error) {
    console.error('Failed to get relevant memories:', error);
    throw error;
  }
}

/**
 * Calculates intent-based boost values for memories
 * @param intentAnalysis The intent analysis results
 * @param memories The list of memories
 * @returns A map of memory IDs to boost values
 */
function getIntentBasedBoost(intentAnalysis: any, memories: any[]): Record<string, number> {
  const boosts: Record<string, number> = {};

  memories.forEach(mem => {
    const content = mem.content.toLowerCase();
    const category = mem.category?.toLowerCase() || '';
    let boost = 0;

    // Boost based on intent categories
    if (intentAnalysis.isPreferenceQuery) {
      // Boost memories that contain preference-related words or are categorized as preferences
      if (content.includes('prefer') || content.includes('like') || content.includes('want') ||
          content.includes('favorite') || category === 'preferences') {
        boost += 0.3;
      }
    }

    if (intentAnalysis.isPersonalQuery) {
      // Boost personal information memories
      if (content.includes('name') || content.includes('personal') ||
          category === 'personal' || category === 'user') {
        boost += 0.25;
      }
    }

    if (intentAnalysis.isWorkQuery) {
      // Boost work-related memories
      if (content.includes('project') || content.includes('work') || content.includes('task') ||
          category === 'work' || category === 'professional') {
        boost += 0.2;
      }
    }

    // Boost based on keyword matches from intent analysis
    intentAnalysis.keywords.forEach((keyword: string) => {
      if (content.includes(keyword)) {
        boost += 0.15;
      }
    });

    // Additional boost for recent memories if query seems time-sensitive
    if (intentAnalysis.keywords.some((k: string) => ['recent', 'latest', 'current', 'now'].includes(k))) {
      const daysSinceCreated = (Date.now() - new Date(mem.createdAt).getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceCreated < 7) {
        boost += 0.1;
      }
    }

    if (boost > 0) {
      boosts[mem.id] = Math.min(boost, 0.5); // Cap boost at 0.5 to prevent over-boosting
    }
  });

  return boosts;
}



/**
 * Deactivates a memory
 * @param id The memory ID
 * @returns The updated memory
 */
export async function deactivateMemory({ id }: { id: string }) {
  try {
    return await withRetry(async () => {
      // Use SQL to update the memory
      return await db.execute(sql`
        UPDATE "Memory"
        SET "isActive" = false
        WHERE id = ${id}
        RETURNING *
      `);
    });
  } catch (error) {
    console.error('Failed to deactivate memory:', error);
    throw error;
  }
}

/**
 * Gets all memories for a user
 * @param userId The user ID
 * @returns Array of all memories for the user
 */
export async function getAllMemories({ userId }: { userId: string }) {
  try {
    return await withRetry(async () => {
      // Use SQL to ensure we get the vector data properly
      return await db.execute(sql`
        SELECT
          id,
          "userId",
          content,
          category,
          "createdAt",
          "isActive"
        FROM
          "Memory"
        WHERE
          "userId" = ${userId}
          AND "isActive" = true
        ORDER BY
          "createdAt" DESC
      `);
    });
  } catch (error) {
    console.error('Failed to get all memories:', error);
    throw error;
  }
}
