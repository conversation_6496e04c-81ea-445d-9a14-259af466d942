-- Enable pgvector extension if not already enabled
CREATE EXTENSION IF NOT EXISTS vector;

-- Drop the existing Memory table if it exists
DROP TABLE IF EXISTS "Memory";

-- Create Memory table
CREATE TABLE IF NOT EXISTS "Memory" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "userId" uuid NOT NULL,
  "content" text NOT NULL,
  "embedding" text NOT NULL,
  "createdAt" timestamp DEFAULT now() NOT NULL,
  "isActive" boolean DEFAULT true NOT NULL,
  "category" text
);

-- Add foreign key constraint
DO $$ BEGIN
 ALTER TABLE "Memory" ADD CONSTRAINT "Memory_userId_User_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create regular index on userId for faster queries
CREATE INDEX IF NOT EXISTS "Memory_userId_idx" ON "Memory" ("userId");

-- We'll use a function-based index for vector similarity
-- First, create a function to convert text to vector
CREATE OR REPLACE FUNCTION embedding_to_vector(text) RETURNS vector
AS $$
BEGIN
  RETURN $1::vector;
EXCEPTION WHEN OTHERS THEN
  RETURN NULL;
END;
$$ LANGUAGE plpgsql IMMUTABLE;
