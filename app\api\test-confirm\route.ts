'use server';

import { NextRequest, NextResponse } from 'next/server';
import { updateUserConfirmedStatus } from '@/lib/db/queries';

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const email = url.searchParams.get('email');
    
    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }
    
    console.log(`Testing confirmation for user: ${email}`);
    
    const result = await updateUserConfirmedStatus({
      email,
      confirmed: true
    });
    
    return NextResponse.json({ success: true, result });
  } catch (error) {
    console.error('Error confirming user:', error);
    return NextResponse.json({ error: 'Failed to confirm user' }, { status: 500 });
  }
}
