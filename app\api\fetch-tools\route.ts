import { OpenAIToolSet } from "composio-core";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const toolset = new OpenAIToolSet();
    let tools = [];
    let searchMetadata = {};

    // Support fetching by use case (experimental semantic search)
    if (body.useCase) {
      try {
        // First try to find relevant actions by use case
        console.log("Searching for use case:", {
          useCase: body.useCase,
          apps: body.apps,
          advanced: body.advanced || false
        });

        const relevantActions = await toolset.client.actions.findActionEnumsByUseCase({
          useCase: body.useCase,
          apps: body.apps?.length > 0 ? body.apps : ["github", "gmail", "jira", "notion", "googlecalendar"],
          advanced: body.advanced || false
        });

        console.log("Found relevant actions:", relevantActions);

        if (relevantActions && relevantActions.length > 0) {
          // Then fetch the full tool definitions for those actions
          tools = await toolset.getTools({ 
            actions: relevantActions,
            apps: body.apps // Include apps to ensure proper context
          });

          searchMetadata = {
            relevantActionsFound: relevantActions.length,
            toolsFetched: tools.length,
            query: body.useCase,
            actions: relevantActions
          };
        }
      } catch (searchError) {
        console.error("Error in use case search:", searchError);
        return NextResponse.json({ 
          success: false, 
          error: searchError instanceof Error ? searchError.message : String(searchError),
          metadata: {
            query: body.useCase,
            apps: body.apps,
            advanced: body.advanced
          }
        }, { status: 500 });
      }
    } else {
      // Standard tool fetching by apps, actions, or tags
      tools = await toolset.getTools({
        actions: body.actions,
        apps: body.apps,
        tags: body.tags,
      });
    }

    return NextResponse.json({ 
      success: true, 
      tools,
      metadata: {
        count: tools.length,
        fetchType: body.useCase ? 'useCase' : 
                  body.tags ? 'tags' : 
                  body.actions ? 'actions' : 
                  body.apps ? 'apps' : 'unknown',
        searchScope: Array.isArray(body.apps) ? body.apps : 'all apps',
        advanced: body.useCase ? body.advanced || false : undefined,
        ...searchMetadata
      }
    });
  } catch (error) {
    console.error('Error fetching tools:', error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : String(error),
      metadata: {
        query: body.useCase,
        apps: body.apps,
        advanced: body.advanced
      }
    }, { status: 500 });
  }
}