'use client';

import type { UIMessage } from 'ai';
import cx from 'classnames';
import { AnimatePresence, motion } from 'framer-motion';
import { memo, useState } from 'react';
import type { Vote } from '@/lib/db/schema';
import { DocumentToolCall, DocumentToolResult } from './document';
import { PencilEditIcon, SparklesIcon } from './icons';
import { Markdown } from './markdown';
import { MessageActions } from './message-actions';
import { PreviewAttachment } from './preview-attachment';
import { Weather } from './weather';
import { CurrentTime } from './current-time';
import { SearchResults } from './search-results';
import { ExtractPageResults } from './extract-page-results';
import { FetchActionsResults } from './fetch-actions-results';
import { MemoryResults } from './memory-results';
import { UpdateMemoryResults } from './update-memory-results';
import { SearchMemoriesResults } from './search-memories-results';

import { ExecuteActionResults } from './execute-action-results';
import { CalendarEvents } from './calendar-events';
import equal from 'fast-deep-equal';
import { cn } from '@/lib/utils';
import { Button } from './ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { MessageEditor } from './message-editor';
import { DocumentPreview } from './document-preview';
import { MessageReasoning } from './message-reasoning';
import { UseChatHelpers } from '@ai-sdk/react';

const PurePreviewMessage = ({
  chatId,
  message,
  vote,
  isLoading,
  setMessages,
  reload,
  isReadonly,
}: {
  chatId: string;
  message: UIMessage;
  vote: Vote | undefined;
  isLoading: boolean;
  setMessages: UseChatHelpers['setMessages'];
  reload: UseChatHelpers['reload'];
  isReadonly: boolean;
}) => {
  const [mode, setMode] = useState<'view' | 'edit'>('view');

  return (
    <AnimatePresence>
      <motion.div
        data-testid={`message-${message.role}`}
        className="w-full mx-auto max-w-3xl px-4 group/message"
        initial={{ y: 5, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        data-role={message.role}
      >
        <div
          className={cn(
            'flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl',
            {
              'w-full': mode === 'edit',
              'group-data-[role=user]/message:w-fit': mode !== 'edit',
            },
          )}
        >
          {/* AI avatar removed */}

          <div className="flex flex-col gap-4 w-full">
            {message.experimental_attachments && (
              <div
                data-testid={`message-attachments`}
                className="flex flex-row justify-end gap-2"
              >
                {message.experimental_attachments.map((attachment) => (
                  <PreviewAttachment
                    key={attachment.url}
                    attachment={attachment}
                  />
                ))}
              </div>
            )}

            {message.parts?.map((part, index) => {
              const { type } = part;
              const key = `message-${message.id}-part-${index}`;

              if (type === 'reasoning') {
                return (
                  <MessageReasoning
                    key={key}
                    isLoading={isLoading}
                    reasoning={part.reasoning}
                  />
                );
              }

              if (type === 'text') {
                if (mode === 'view') {
                  return (
                    <div key={key} className="flex flex-row gap-2 items-start">
                      {message.role === 'user' && !isReadonly && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              data-testid="message-edit-button"
                              variant="ghost"
                              className="px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100 hover:bg-primary hover:text-white transition-colors"
                              onClick={() => {
                                setMode('edit');
                              }}
                            >
                              <PencilEditIcon />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Edit message</TooltipContent>
                        </Tooltip>
                      )}

                      <div
                        data-testid="message-content"
                        className={cn('flex flex-col gap-4', {
                          'bg-primary text-primary-foreground px-3 py-2 rounded-xl shadow-[0_0_10px_rgba(120,100,255,0.1)] dark:shadow-[0_0_15px_rgba(120,100,255,0.15)]':
                            message.role === 'user',
                        })}
                      >
                        <Markdown>{part.text}</Markdown>
                      </div>
                    </div>
                  );
                }

                if (mode === 'edit') {
                  return (
                    <div key={key} className="flex flex-row gap-2 items-start">
                      <div className="size-8" />

                      <MessageEditor
                        key={message.id}
                        message={message}
                        setMode={setMode}
                        setMessages={setMessages}
                        reload={reload}
                      />
                    </div>
                  );
                }
              }

              if (type === 'tool-invocation') {
                const { toolInvocation } = part;
                const { toolName, toolCallId, state } = toolInvocation;

                if (state === 'call') {
                  const { args } = toolInvocation;

                  return (
                    <div
                      key={toolCallId}
                      className={cx({
                        skeleton: ['getWeather', 'getCurrentTime'].includes(toolName),
                      })}
                    >
                      {toolName === 'getWeather' ? (
                        <Weather />
                      ) : toolName === 'getCurrentTime' ? (
                        <CurrentTime timeData={{
                          datetime: new Date().toISOString().replace('T', ' ').substring(0, 19),
                          timezone_name: 'Loading...',
                          timezone_location: 'Loading...',
                          timezone_abbreviation: '...',
                          gmt_offset: 0,
                          is_dst: false,
                          requested_location: 'Loading...',
                          latitude: 0,
                          longitude: 0
                        }} />
                      ) : toolName === 'searchInternet' ? (
                        <SearchResults />
                      ) : toolName === 'extractPage' ? (
                        <ExtractPageResults />
                      ) : toolName === 'fetchActions' ? (
                        <FetchActionsResults />
                      ) : toolName === 'executeAction' ? (
                        <ExecuteActionResults />
                      ) : toolName === 'googleCalendarAgent' ? (
                        <div className="border-l-4 border-yellow-500 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg p-3">
                          <div className="flex items-center gap-2 mb-2">
                            <div className="w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center">
                              <span className="text-white text-xs">📅</span>
                            </div>
                            <span className="font-medium text-yellow-800 dark:text-yellow-200">Google Calendar</span>
                          </div>
                          <div className="text-sm text-yellow-700 dark:text-yellow-300">
                            Processing calendar request...
                          </div>
                        </div>
                      ) : toolName === 'createDocument' ? (
                        <DocumentPreview isReadonly={isReadonly} args={args} />
                      ) : toolName === 'updateDocument' ? (
                        <DocumentToolCall
                          type="update"
                          args={args}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'requestSuggestions' ? (
                        <DocumentToolCall
                          type="request-suggestions"
                          args={args}
                          isReadonly={isReadonly}
                        />
                      ) : null}
                    </div>
                  );
                }

                if (state === 'result') {
                  const { result } = toolInvocation;

                  return (
                    <div key={toolCallId}>
                      {toolName === 'getWeather' ? (
                        <Weather weatherAtLocation={result} />
                      ) : toolName === 'getCurrentTime' ? (
                        <CurrentTime timeData={result} />
                      ) : toolName === 'searchInternet' ? (
                        <SearchResults searchData={result} />
                      ) : toolName === 'extractPage' ? (
                        <ExtractPageResults extractData={result} />
                      ) : toolName === 'fetchActions' ? (
                        <FetchActionsResults actionsData={result} />
                      ) : toolName === 'executeAction' ? (
                        // Check if this is a calendar action result
                        result && result.action && result.action.toLowerCase().includes('calendar') && result.result && Array.isArray(result.result.items) ? (
                          <CalendarEvents calendarData={result.result} />
                        ) : (
                          <ExecuteActionResults actionData={result} />
                        )
                      ) : toolName === 'googleCalendarAgent' ? (
                        <div className="border-l-4 border-yellow-500 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg p-3">
                          <div className="flex items-center gap-2 mb-2">
                            <div className="w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center">
                              <span className="text-white text-xs">📅</span>
                            </div>
                            <span className="font-medium text-yellow-800 dark:text-yellow-200">Google Calendar</span>
                          </div>
                          <div className="text-sm">
                            {result.success ? (
                              <div className="text-yellow-700 dark:text-yellow-300">
                                {result.message}
                                {result.detectedIntent && (
                                  <div className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                                    Intent: {result.detectedIntent}
                                  </div>
                                )}
                              </div>
                            ) : (
                              <div className="text-red-700 dark:text-red-300">
                                ❌ {result.message || 'Calendar operation failed'}
                              </div>
                            )}
                          </div>
                          {/* Show calendar events if available */}
                          {result.success && result.result && Array.isArray(result.result.items) && (
                            <div className="mt-3">
                              <CalendarEvents calendarData={result.result} />
                            </div>
                          )}
                        </div>
                      ) : toolName === 'createDocument' ? (
                        <DocumentPreview
                          isReadonly={isReadonly}
                          result={result}
                        />
                      ) : toolName === 'updateDocument' ? (
                        <DocumentToolResult
                          type="update"
                          result={result}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'requestSuggestions' ? (
                        <DocumentToolResult
                          type="request-suggestions"
                          result={result}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'remember' ? (
                        <MemoryResults memoryData={result} />
                      ) : toolName === 'updateMemory' ? (
                        <UpdateMemoryResults updateData={result} />
                      ) : toolName === 'searchMemories' ? (
                        <SearchMemoriesResults searchData={result} />
                      ) : (
                        <pre>{JSON.stringify(result, null, 2)}</pre>
                      )}
                    </div>
                  );
                }
              }
            })}

            {!isReadonly && (
              <MessageActions
                key={`action-${message.id}`}
                chatId={chatId}
                message={message}
                vote={vote}
                isLoading={isLoading}
              />
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export const PreviewMessage = memo(
  PurePreviewMessage,
  (prevProps, nextProps) => {
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (prevProps.message.id !== nextProps.message.id) return false;
    if (!equal(prevProps.message.parts, nextProps.message.parts)) return false;
    if (!equal(prevProps.vote, nextProps.vote)) return false;

    return true;
  },
);

export const ThinkingMessage = () => {
  const role = 'assistant';

  return (
    <motion.div
      data-testid="message-assistant-loading"
      className="w-full mx-auto max-w-3xl px-4 group/message "
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1, transition: { delay: 1 } }}
      data-role={role}
    >
      <div
        className={cx(
          'flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:py-2 rounded-xl',
          {
            'group-data-[role=user]/message:bg-muted': true,
          },
        )}
      >
        {/* AI avatar removed */}

        <div className="flex flex-col gap-2 w-full">
          <div className="flex flex-col gap-4 text-muted-foreground">
            Hmm...
          </div>
        </div>
      </div>
    </motion.div>
  );
};
