# Streamara Environment Variables

# Database
POSTGRES_URL=

# Authentication
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=

# Supabase
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# Composio API Key
COMPOSIO_API_KEY=

# Google OAuth Credentials (for white-labeling)
OAUTH_GMAIL_CLIENT_ID=
OAUTH_GMAIL_CLIENT_SECRET=
OAUTH_GOOGLECALENDAR_CLIENT_ID=
OAUTH_GOOGLECALENDAR_CLIENT_SECRET=

# GitHub OAuth Credentials (for white-labeling)
OAUTH_GITHUB_CLIENT_ID=
OAUTH_GITHUB_CLIENT_SECRET=

# Other providers as needed
OAUTH_NOTION_CLIENT_ID=
OAUTH_NOTION_CLIENT_SECRET=

# App URL (used for redirects in production)
NEXT_PUBLIC_APP_URL=http://localhost:3000
