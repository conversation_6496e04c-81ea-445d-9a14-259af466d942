# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# next.js
.next/
out/
build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
# .env.local
.env.development.local
.env.test.local
.env.production.local

# turbo
.turbo

# .env
.vercel
# .env*.local

# Playwright
/test-results/
/playwright-report/
/blob-report/
/playwright/*


