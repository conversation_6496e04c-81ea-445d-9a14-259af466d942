'use client';

import { useState, useEffect } from 'react';
import { EditProfileForm } from './edit-profile-form';

export interface ProfileActionState {
  status: 'idle' | 'success' | 'failed' | 'invalid_data' | 'password_mismatch';
}

export default function ProfilePage() {
  const [userData, setUserData] = useState({
    email: '',
    firstName: '',
    lastName: ''
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Fetch user data from session
    const fetchUserData = async () => {
      setIsLoading(true);
      try {
        const response = await fetch('/api/user');
        if (response.ok) {
          const data = await response.json();
          setUserData({
            email: data.email,
            firstName: data.firstName || '',
            lastName: data.lastName || ''
          });
        }
      } catch (error) {
        console.error('Failed to fetch user data', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, []);

  return (
    <div className="flex min-h-screen w-full items-center justify-center py-8 px-4 bg-background">
      <div className="w-full max-w-md bg-card shadow-sm rounded-lg border flex flex-col gap-6 p-6">
        <div className="flex flex-col items-center justify-center gap-2 text-center">
          <h3 className="text-xl font-semibold dark:text-zinc-50">Profile Settings</h3>
          <p className="text-sm text-gray-500 dark:text-zinc-400">
            Manage your account settings
          </p>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
          </div>
        ) : (
          <div className="w-full mt-6">
            <EditProfileForm userData={userData} />
          </div>
        )}
      </div>
    </div>
  );
}
