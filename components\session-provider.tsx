'use client';

import { ReactNode } from 'react';
import { useSessionCheck } from '@/lib/auth/session-check';

interface SessionProviderProps {
  children: ReactNode;
}

/**
 * Component that provides session validation
 * This will check if the user's session is still valid and log them out if not
 */
export function SessionProvider({ children }: SessionProviderProps) {
  // Use the session check hook
  useSessionCheck();

  return <>{children}</>;
}
