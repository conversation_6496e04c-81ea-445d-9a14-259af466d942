'use client';

import { useState } from 'react';
import { Button } from './ui/button';
import { PlayIcon, CheckCircleIcon, XCircleIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { ToolUsageCard } from './ui/tool-usage-card';

interface ExecuteActionResultsProps {
  actionData?: {
    success: boolean;
    message: string;
    result?: any;
    action?: string;
    params?: Record<string, any>;
    error?: string;
    redirectUrl?: string;
  };
}

export function ExecuteActionResults({ actionData }: ExecuteActionResultsProps) {
  const [showResult, setShowResult] = useState(false);
  const router = useRouter();

  if (!actionData) {
    // Loading state
    return (
      <ToolUsageCard
        icon={PlayIcon}
        toolName="Execute Action"
        status="loading"
      >
        <div className="mt-1 h-5 w-3/4 animate-pulse rounded-md bg-muted"></div>
        <div className="mt-2 h-4 w-full animate-pulse rounded-md bg-muted"></div>
        <div className="mt-1 h-4 w-1/2 animate-pulse rounded-md bg-muted"></div>
      </ToolUsageCard>
    );
  }

  // Handle error cases
  if (!actionData.success) {
    return (
      <ToolUsageCard
        icon={XCircleIcon}
        toolName="Execute Action"
        status="error"
        query={actionData.action}
        errorMessage={actionData.error || "Failed to execute action"}
      >
        <div className="space-y-3">
          <div className="text-sm text-muted-foreground">
            {actionData.message || "Please check the action name and parameters and try again."}
          </div>

          {actionData.error && (actionData.error.includes('Connection') || actionData.redirectUrl) && (
            <div className="mt-3 p-2 bg-amber-50 border border-amber-200 rounded-md text-xs">
              <p className="font-medium text-amber-800">Connection Issue Detected</p>
              <p className="mt-1 text-amber-700">
                It appears your connection to {actionData.action?.split('_')[0].toUpperCase() || 'the service'} may have expired or been revoked.
              </p>
              <div className="mt-2 flex items-center">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs bg-amber-100 border-amber-300 text-amber-800 hover:bg-amber-200 hover:text-amber-900"
                  onClick={() => router.push(actionData.redirectUrl || '/profile/connections')}
                >
                  Go to Connections Page
                </Button>
              </div>
            </div>
          )}

          {actionData.error && actionData.error.includes('Bad Request') && (
            <div className="mt-3 p-2 bg-amber-50 border border-amber-200 rounded-md text-xs">
              <p className="font-medium text-amber-800">Format Issue Detected</p>
              <p className="mt-1 text-amber-700">
                There may be an issue with the connection ID format. The Composio API requires connection IDs to be in UUID format.
              </p>
              <div className="mt-2 flex items-center">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs bg-amber-100 border-amber-300 text-amber-800 hover:bg-amber-200 hover:text-amber-900"
                  onClick={() => router.push('/profile/connections')}
                >
                  Go to Connections Page
                </Button>
              </div>
            </div>
          )}
        </div>
      </ToolUsageCard>
    );
  }

  return (
    <ToolUsageCard
      icon={CheckCircleIcon}
      toolName="Execute Action"
      status="success"
      query={actionData.action}
      showContent={showResult}
      onToggleContent={() => setShowResult(!showResult)}
    >
      <div className="space-y-3">
        <div className="text-sm whitespace-pre-wrap">
          {actionData.message}
        </div>

        {actionData.result && (
          <div className="border rounded-md p-3 bg-background">
            <div className="text-xs font-medium mb-1">Result Details:</div>
            <pre className="text-xs text-muted-foreground whitespace-pre-wrap max-h-[300px] overflow-y-auto">
              {JSON.stringify(actionData.result, null, 2)}
            </pre>
          </div>
        )}

        {actionData.params && (
          <div className="border rounded-md p-3 bg-background">
            <div className="text-xs font-medium mb-1">Parameters Used:</div>
            <pre className="text-xs text-muted-foreground whitespace-pre-wrap max-h-[100px] overflow-y-auto">
              {JSON.stringify(actionData.params, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </ToolUsageCard>
  );
}
