'use client';

import { ReactNode } from 'react';
import { But<PERSON> } from './button';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';

interface ToolUsageCardProps {
  icon: LucideIcon;
  toolName: string;
  query?: string;
  status?: 'loading' | 'success' | 'error';
  children?: ReactNode;
  showContent?: boolean;
  onToggleContent?: () => void;
  errorMessage?: string;
  url?: string;
  isExecuting?: boolean;
}

export function ToolUsageCard({
  icon: Icon,
  toolName,
  query,
  status = 'success',
  children,
  showContent = false,
  onToggleContent,
  errorMessage,
  url,
  isExecuting = false,
}: ToolUsageCardProps) {
  return (
    <div className="flex flex-col gap-2 rounded-lg border border-border/40 shadow-sm max-w-[500px] bg-background overflow-hidden transition-all hover:shadow-md">
      <div className="flex items-center justify-between px-4 py-2.5 border-b border-border/40 bg-gradient-to-b from-muted/20 to-muted/30">
        <div className="flex items-center gap-2.5">
          <div className="relative">
            {isExecuting && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-5 h-5 border border-blue-200 border-t-blue-500 rounded-full animate-spin"></div>
              </div>
            )}
            <Icon
              size={20}
              className={cn(
                "transition-all",
                isExecuting && "opacity-0",
                status === 'error' && 'text-red-500',
                status === 'success' && 'text-blue-500',
                status === 'loading' && 'text-blue-400'
              )}
            />
          </div>
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground text-sm">Using Tool</span>
            <span className="text-muted-foreground/50 mx-0.5">|</span>
            <span className={cn(
              "font-medium text-sm",
              status === 'error'
                ? "text-red-500"
                : "text-blue-500"
            )}>
              {toolName}
            </span>
          </div>
        </div>
        {onToggleContent && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleContent}
            disabled={isExecuting}
            className={cn(
              "text-xs h-7 px-3 transition-colors relative rounded",
              isExecuting && "pointer-events-none",
              showContent
                ? "text-blue-500 hover:bg-blue-50"
                : "text-muted-foreground hover:bg-blue-50 hover:text-blue-500"
            )}
          >
            {isExecuting ? (
              <>
                <span className="opacity-0">View</span>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-3.5 h-3.5 border border-blue-200 border-t-blue-500 rounded-full animate-spin"></div>
                </div>
              </>
            ) : (
              showContent ? 'Hide' : 'View'
            )}
          </Button>
        )}
      </div>

      <div className="px-4 pb-3">
        {query && (
          <div className="text-sm font-medium text-foreground border-l-2 border-primary pl-2 py-1 mb-2 bg-primary/5 rounded-r-sm">
            {query.endsWith('...') ? `"${query}` : `"${query}"`}
          </div>
        )}

        {errorMessage && (
          <div className="text-sm text-red-500 mt-1 p-2 bg-red-50 rounded-md border border-red-100">
            {errorMessage}
          </div>
        )}

        {url && (
          <div className="flex items-center gap-1 text-xs text-muted-foreground mt-2 bg-muted/20 p-1.5 rounded-md">
            <a
              href={url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:text-primary/80 truncate transition-colors flex items-center gap-1"
            >
              <span className="underline">{url}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-external-link">
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
                <polyline points="15 3 21 3 21 9" />
                <line x1="10" y1="14" x2="21" y2="3" />
              </svg>
            </a>
          </div>
        )}

        {showContent && children && (
          <div className={cn(
            "mt-3 p-3 rounded-md",
            status === 'error' ? "bg-red-50" : "bg-muted/10"
          )}>
            {children}
          </div>
        )}
      </div>
    </div>
  );
}
