'use server';

import { NextRequest, NextResponse } from 'next/server';
import postgres from 'postgres';
import { drizzle } from 'drizzle-orm/postgres-js';
import { eq } from 'drizzle-orm';
import { user } from '@/lib/db/schema';

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const email = url.searchParams.get('email');
    
    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }
    
    console.log(`Attempting to fix confirmation for user: ${email}`);
    
    // Create a direct database connection
    const client = postgres(process.env.POSTGRES_URL!);
    
    // First, check if the user exists
    const checkUser = await client`
      SELECT "id", "email", "confirmed" 
      FROM "User" 
      WHERE "email" = ${email}
    `;
    
    console.log('User check result:', checkUser);
    
    if (checkUser.length === 0) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    const userId = checkUser[0].id;
    const currentConfirmed = checkUser[0].confirmed;
    
    console.log(`User found: ${userId}, current confirmed status: ${currentConfirmed}`);
    
    // Try multiple approaches to update the confirmation status
    
    // Approach 1: Direct SQL update
    try {
      console.log('Trying approach 1: Direct SQL update');
      const result1 = await client`
        UPDATE "User" 
        SET "confirmed" = true 
        WHERE "email" = ${email}
        RETURNING "id", "email", "confirmed"
      `;
      console.log('Approach 1 result:', result1);
    } catch (error) {
      console.error('Approach 1 failed:', error);
    }
    
    // Approach 2: Direct SQL update with lowercase column name
    try {
      console.log('Trying approach 2: Direct SQL update with lowercase column name');
      const result2 = await client`
        UPDATE "User" 
        SET confirmed = true 
        WHERE "email" = ${email}
        RETURNING "id", "email", "confirmed"
      `;
      console.log('Approach 2 result:', result2);
    } catch (error) {
      console.error('Approach 2 failed:', error);
    }
    
    // Approach 3: Using Drizzle ORM
    try {
      console.log('Trying approach 3: Using Drizzle ORM');
      const db = drizzle(client);
      const result3 = await db.update(user)
        .set({ confirmed: true })
        .where(eq(user.email, email))
        .returning();
      console.log('Approach 3 result:', result3);
    } catch (error) {
      console.error('Approach 3 failed:', error);
    }
    
    // Approach 4: Direct SQL update with explicit casting
    try {
      console.log('Trying approach 4: Direct SQL update with explicit casting');
      const result4 = await client`
        UPDATE "User" 
        SET "confirmed" = true::boolean
        WHERE "email" = ${email}
        RETURNING "id", "email", "confirmed"
      `;
      console.log('Approach 4 result:', result4);
    } catch (error) {
      console.error('Approach 4 failed:', error);
    }
    
    // Check if any of the approaches worked
    const finalCheck = await client`
      SELECT "id", "email", "confirmed" 
      FROM "User" 
      WHERE "email" = ${email}
    `;
    
    console.log('Final check result:', finalCheck);
    
    // Close the connection
    await client.end();
    
    return NextResponse.json({ 
      success: true, 
      initialStatus: currentConfirmed,
      finalStatus: finalCheck[0].confirmed,
      user: finalCheck[0] 
    });
  } catch (error) {
    console.error('Error fixing user confirmation:', error);
    return NextResponse.json({ error: 'Failed to fix user confirmation' }, { status: 500 });
  }
}
