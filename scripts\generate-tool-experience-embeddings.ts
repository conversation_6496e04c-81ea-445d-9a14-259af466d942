/**
 * <PERSON><PERSON><PERSON> to generate embeddings for existing tool experiences
 * Run this after adding the embedding column to populate existing records
 */

import { db } from '@/lib/db/client';
import { toolExperiences } from '@/lib/db/schema';
import { generateEmbedding } from '@/lib/memory/embeddings';
import { sql } from 'drizzle-orm';

async function generateToolExperienceEmbeddings() {
  try {
    console.log('🚀 Starting tool experience embedding generation...');

    // Get all tool experiences without embeddings
    const experiences = await db
      .select()
      .from(toolExperiences)
      .where(sql`embedding IS NULL OR embedding = ''`);

    console.log(`Found ${experiences.length} tool experiences to process`);

    for (let i = 0; i < experiences.length; i++) {
      const experience = experiences[i];
      console.log(`Processing ${i + 1}/${experiences.length}: ${experience.experienceType} for ${experience.appName}`);

      try {
        // Create embedding text from experience content and context
        const embeddingText = `${experience.experienceType} ${experience.appName} ${experience.actionName || 'general'} ${experience.contextTags?.join(' ') || ''} ${experience.experienceContent}`;

        // Generate embedding
        const embedding = await generateEmbedding(embeddingText);
        console.log(`Generated embedding with ${embedding.length} dimensions`);

        // Update the record with the embedding
        await db
          .update(toolExperiences)
          .set({ embedding: JSON.stringify(embedding) })
          .where(sql`id = ${experience.id}`);

        console.log(`✅ Updated experience: ${experience.id}`);

        // Add a small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error(`❌ Failed to process experience ${experience.id}:`, error);
      }
    }

    console.log('🎉 Tool experience embedding generation completed!');
  } catch (error) {
    console.error('💥 Error generating tool experience embeddings:', error);
  }
}

// Run the script if called directly
if (require.main === module) {
  generateToolExperienceEmbeddings()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { generateToolExperienceEmbeddings };
