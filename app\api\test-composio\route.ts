import { OpenAIToolSet } from "composio-core";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    console.log("Testing Composio connection...");

    // Initialize the Composio toolset using OpenAIToolSet
    const toolset = new OpenAIToolSet();
    console.log("Toolset initialized");

    // Get tools for GitHub and Google Calendar (your existing connections)
    console.log("Fetching tools...");
    const tools = await toolset.getTools({
      apps: ["github", "googlecalendar"]
    });
    console.log("Tools fetched successfully");

    // Log the structure of the tools object to understand its format
    console.log("Tools structure:", JSON.stringify(tools).substring(0, 500) + "...");

    // Try to extract tool names using a different approach
    let toolNames = [];

    // Check if tools is an array
    if (Array.isArray(tools)) {
      console.log("Tools is an array");
      toolNames = tools.map(tool => {
        // Try to find a name property or similar
        return tool.name || tool.id || tool.action || JSON.stringify(tool);
      });
    }
    // Check if tools is an object with a tools property
    else if (tools && typeof tools === 'object' && tools.tools) {
      console.log("Tools has a tools property");
      toolNames = Array.isArray(tools.tools)
        ? tools.tools.map(t => t.name || t.id || t.action || JSON.stringify(t))
        : Object.keys(tools.tools);
    }
    // Check if tools is a plain object
    else if (tools && typeof tools === 'object') {
      console.log("Tools is an object");
      // Try different properties that might contain the actual tool names
      if (tools.actions && Array.isArray(tools.actions)) {
        toolNames = tools.actions.map(a => a.name || a.id || a.action || JSON.stringify(a));
      } else {
        // Last resort: use the keys of the object
        toolNames = Object.keys(tools);
      }
    }

    console.log(`Found ${toolNames.length} tools`);

    // Return success response with tool information
    return NextResponse.json({
      success: true,
      message: "Successfully connected to Composio",
      toolCount: toolNames.length,
      toolNames: toolNames,
      // Include a sample of the raw tools object to help debug
      toolsSample: JSON.stringify(tools).substring(0, 1000) + "..."
    });
  } catch (error) {
    console.error("Error connecting to Composio:", error);

    // Return error response
    return NextResponse.json({
      success: false,
      message: "Failed to connect to Composio",
      error: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}
