'use client';

import { useEffect } from 'react';
import { NotFound } from '@/components/not-found';

export default function ChatError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Chat error:', error);
  }, [error]);

  return (
    <NotFound
      title="Something went wrong"
      message={
        error.message === 'Failed to get chat by id from database'
          ? 'The chat you are looking for does not exist or you don\'t have permission to access it.'
          : 'An error occurred while loading this chat. Please try again later.'
      }
      showHomeButton={true}
      showBackButton={true}
      showRefreshButton={true}
    />
  );
}
