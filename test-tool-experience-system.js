/**
 * Test script for the enhanced 2-phase tool experience memory system
 * Run with: node test-tool-experience-system.js
 */

// Test the AI-controlled task detection logic
function detectTaskRequest(queryText, messages = []) {
  const query = queryText.toLowerCase();

  // Skip greetings and casual conversation
  const casualPatterns = [
    /^(hi|hello|hey|good morning|good afternoon|good evening)$/,
    /^(how are you|what's up|how's it going)$/,
    /^(thanks|thank you|bye|goodbye)$/,
    /^(yes|no|ok|okay)$/
  ];

  if (casualPatterns.some(pattern => pattern.test(query.trim()))) {
    return false;
  }

  // Detect task-oriented requests
  const taskPatterns = [
    // Action verbs
    /\b(create|make|build|generate|write|send|fetch|get|find|search|update|delete|modify|change|add|remove|list|show|display)\b/,
    // App-specific keywords
    /\b(github|gmail|notion|jira|calendar|repository|repo|email|mail|page|issue|ticket|meeting|event)\b/,
    // Work-related keywords
    /\b(project|task|work|job|assignment|deadline|schedule|plan|organize|manage)\b/,
    // Question words indicating information seeking
    /\b(what|how|when|where|why|which|who)\b.*\b(is|are|can|could|should|would|do|does|did)\b/,
    // Multi-app scenarios
    /\b(then|after|next|also|and then|followed by)\b/
  ];

  const hasTaskPattern = taskPatterns.some(pattern => pattern.test(query));

  // Context from conversation history
  const hasWorkContext = messages.length > 1 && messages.some((msg) => {
    if (msg.role === 'assistant') {
      const content = msg.parts?.find((p) => p.type === 'text')?.text || '';
      return /\b(tool|action|execute|fetch|create|update)\b/i.test(content);
    }
    return false;
  });

  return hasTaskPattern || hasWorkContext;
}

// Test cases for the new system
const testCases = [
  // Should NOT trigger tool experience retrieval (casual conversation)
  // Note: User memories are ALWAYS retrieved for personal context
  { query: "hi", expected: false, description: "Simple greeting - gets user memories, skips tool experiences" },
  { query: "hello there", expected: false, description: "Greeting with extra words - gets user memories, skips tool experiences" },
  { query: "how are you", expected: false, description: "Casual question - gets user memories, skips tool experiences" },
  { query: "thanks", expected: false, description: "Simple thanks - gets user memories, skips tool experiences" },
  { query: "yes", expected: false, description: "Simple confirmation - gets user memories, skips tool experiences" },

  // Should trigger tool experience retrieval (task requests)
  // Note: These also get user memories + tool experiences
  { query: "create a new repository", expected: true, description: "Action verb + task - gets user memories + tool experiences" },
  { query: "find my emails from yesterday", expected: true, description: "Search request - gets user memories + tool experiences" },
  { query: "show me my github repositories", expected: true, description: "Display + app name - gets user memories + tool experiences" },
  { query: "what are my recent notion pages", expected: true, description: "Question + app name - gets user memories + tool experiences" },
  { query: "update the project status", expected: true, description: "Work-related action - gets user memories + tool experiences" },
  { query: "list all my calendar events", expected: true, description: "List + app functionality - gets user memories + tool experiences" },
  { query: "how do I create a new issue in jira", expected: true, description: "How-to question + app - gets user memories + tool experiences" },
  { query: "send an email to the team", expected: true, description: "Communication action - gets user memories + tool experiences" },
  { query: "I finished the project, now update notion and then create a jira ticket", expected: true, description: "Multi-app workflow - gets user memories + tool experiences" },
  { query: "check my github repos and then send a summary email", expected: true, description: "Sequential tasks - gets user memories + tool experiences" },
];

console.log("🧪 Testing 3-Phase Granular Tool Experience Memory System\n");
console.log("============================================================");

let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
  const result = detectTaskRequest(testCase.query);
  const status = result === testCase.expected ? "✅ PASS" : "❌ FAIL";

  console.log(`Test ${index + 1}: ${status}`);
  console.log(`  Query: "${testCase.query}"`);
  console.log(`  Description: ${testCase.description}`);
  console.log(`  Expected: ${testCase.expected}, Got: ${result}`);
  console.log("");

  if (result === testCase.expected) {
    passed++;
  } else {
    failed++;
  }
});

console.log("============================================================");
console.log(`📊 Test Results: ${passed} passed, ${failed} failed`);
console.log(`Success Rate: ${((passed / testCases.length) * 100).toFixed(1)}%`);

if (failed === 0) {
  console.log("\n🎉 All tests passed! The 3-phase granular system is working correctly.");
  console.log("\n🎯 Key Improvements:");
  console.log("✅ User memories ALWAYS retrieved for personal context (name, preferences)");
  console.log("✅ Tool experiences only retrieved for actual task requests");
  console.log("✅ 3-phase granular system: task_start → pre_fetch → pre_execute");
  console.log("✅ Phase-specific retrieval with phase_category column");
  console.log("✅ Maximum precision: each phase gets exactly the guidance it needs");
} else {
  console.log("\n⚠️  Some tests failed. Review the task detection patterns.");
}

console.log("\n🔧 Next Steps:");
console.log("1. Deploy the 3-phase granular system");
console.log("2. Update existing tool experiences with phase_category values");
console.log("3. Monitor phase-specific similarity scores");
console.log("4. Import optimized tool experience data for each phase");
console.log("5. Measure 80% task success rate goal");

console.log("\n📊 3-Phase System Benefits:");
console.log("🎯 task_start: High-level workflow planning");
console.log("🔍 pre_fetch: Precise action discovery");
console.log("⚡ pre_execute: Targeted parameter optimization");
