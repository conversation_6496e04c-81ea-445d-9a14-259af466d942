import { tool } from 'ai';
import { z } from 'zod';
import { OpenAIToolSet } from "composio-core";
import { getConnectionsByUserId } from '@/lib/db/queries';
import { auth } from '@/app/(auth)/auth';
import { extractAppFromAction } from '@/lib/ai/memory/strategicMemoryManager';
import { getCurrentTimeForLocation } from '@/lib/utils/date-time';

// Helper function to ensure connection ID is in UUID format
function ensureUuidFormat(connectionId: string): string {
  // If it's already in UUID format, return it
  if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(connectionId)) {
    return connectionId;
  }

  // If it's in ca_ format, convert it to a UUID-like format
  if (connectionId.startsWith('ca_')) {
    // Remove the ca_ prefix
    const idPart = connectionId.substring(3);
    console.log(`Warning: Converting connection ID from ca_ format to UUID format. This may not work.`);
    return `00000000-0000-0000-0000-${idPart.padStart(12, '0')}`;
  }

  // If it's neither, return as is and hope for the best
  console.log(`Warning: Connection ID ${connectionId} is not in UUID format. This may cause errors.`);
  return connectionId;
}

/**
 * Infer missing parameters based on action type and context
 * This helps the AI fill in common parameters without asking the user
 */
async function inferParameters(action: string, params: Record<string, any> = {}): Promise<Record<string, any>> {
  const inferredParams = { ...params };
  const actionLower = action.toLowerCase();

  try {
    // Calendar-related parameter inference
    if (actionLower.includes('calendar')) {
      // If dealing with dates and no date is provided, use current date
      if (
        (actionLower.includes('list_events') || actionLower.includes('get_events')) &&
        (!inferredParams.timeMin || !inferredParams.timeMax)
      ) {
        // Get current date/time
        const timeData = await getCurrentTimeForLocation('auto');
        const today = timeData.datetime.split(' ')[0]; // YYYY-MM-DD

        // If timeMin not provided, use start of today
        if (!inferredParams.timeMin) {
          inferredParams.timeMin = `${today}T00:00:00Z`;
        }

        // If timeMax not provided, use end of today
        if (!inferredParams.timeMax) {
          inferredParams.timeMax = `${today}T23:59:59Z`;
        }
      }
    }

    // GitHub-related parameter inference
    if (actionLower.includes('github')) {
      // Enhanced owner inference
      if (!inferredParams.owner) {
        if (actionLower.includes('list_repositories_for_the_authenticated_user') ||
            actionLower.includes('authenticated_user')) {
          // No owner parameter needed for authenticated user endpoints
        } else if (actionLower.includes('list_repositories') ||
                   actionLower.includes('get_repository') ||
                   actionLower.includes('list_branches')) {
          inferredParams.owner = 'authenticated';
        }
      }

      // Repository inference for branch operations
      if (actionLower.includes('list_branches') && !inferredParams.repo) {
        // If we have owner but no repo, we can't proceed - this will be caught by validation
        if (inferredParams.owner && !inferredParams.repo) {
          console.log('Warning: Branch operation requires both owner and repo parameters');
        }
      }

      // Pagination defaults for list operations
      if (actionLower.includes('list') && !inferredParams.per_page) {
        inferredParams.per_page = 30;
      }
      if (actionLower.includes('list') && !inferredParams.page) {
        inferredParams.page = 1;
      }

      // Default branch for branch operations
      if (actionLower.includes('branch') && !inferredParams.branch) {
        inferredParams.branch = 'main';
      }
    }

    // Gmail-related parameter inference
    if (actionLower.includes('gmail')) {
      // For list operations without maxResults, set a reasonable default
      if (actionLower.includes('list') && !inferredParams.maxResults) {
        inferredParams.maxResults = 10;
      }

      // Default label for list operations
      if (actionLower.includes('list_messages') && !inferredParams.labelIds) {
        inferredParams.labelIds = ['INBOX'];
      }

      // Default query for unread messages
      if (actionLower.includes('list') && !inferredParams.q) {
        // Don't set a default query - let user specify what they want
      }
    }

    // Notion-related parameter inference
    if (actionLower.includes('notion')) {
      // For page operations, try to infer page_size
      if (actionLower.includes('query_database') && !inferredParams.page_size) {
        inferredParams.page_size = 100;
      }
    }

    // Jira-related parameter inference
    if (actionLower.includes('jira')) {
      // Default issue type for creation
      if (actionLower.includes('create_issue') && !inferredParams.issueType) {
        inferredParams.issueType = 'Task';
      }

      // Default max results for search
      if (actionLower.includes('search') && !inferredParams.maxResults) {
        inferredParams.maxResults = 50;
      }
    }

    // Calendar-related parameter inference enhancements
    if (actionLower.includes('calendar')) {
      // Default to primary calendar
      if (!inferredParams.calendarId) {
        inferredParams.calendarId = 'primary';
      }

      // For event creation, set default duration if end time not specified
      if (actionLower.includes('create') && inferredParams.start && !inferredParams.end) {
        const startTime = new Date(inferredParams.start);
        const endTime = new Date(startTime.getTime() + 60 * 60 * 1000); // 1 hour later
        inferredParams.end = endTime.toISOString();
      }
    }

    return inferredParams;
  } catch (error) {
    console.error("Error inferring parameters:", error);
    // Return original params if inference fails
    return params;
  }
}

/**
 * Validate required parameters before execution
 */
async function validateParameters(action: string, params: Record<string, any>): Promise<{
  valid: boolean;
  missing: string[];
  suggestions: string[];
}> {
  const missing: string[] = [];
  const suggestions: string[] = [];
  const actionLower = action.toLowerCase();

  // GitHub parameter validation
  if (actionLower.includes('github')) {
    if (actionLower.includes('list_branches')) {
      if (!params.owner) {
        missing.push('owner');
        suggestions.push("Use 'authenticated' for your own repositories");
      }
      if (!params.repo) {
        missing.push('repo');
        suggestions.push("Specify the repository name (e.g., 'my-project')");
      }
    }

    if (actionLower.includes('create_issue')) {
      if (!params.title) {
        missing.push('title');
        suggestions.push("Provide a descriptive title for the issue");
      }
      if (!params.body) {
        missing.push('body');
        suggestions.push("Include a detailed description in the body");
      }
    }
  }

  // Gmail parameter validation
  if (actionLower.includes('gmail')) {
    if (actionLower.includes('send')) {
      if (!params.to) {
        missing.push('to');
        suggestions.push("Specify recipient email address");
      }
      if (!params.subject) {
        missing.push('subject');
        suggestions.push("Provide an email subject");
      }
    }
  }

  // Calendar parameter validation
  if (actionLower.includes('calendar')) {
    if (actionLower.includes('create')) {
      if (!params.summary) {
        missing.push('summary');
        suggestions.push("Provide an event title/summary");
      }
      if (!params.start) {
        missing.push('start');
        suggestions.push("Specify event start time in ISO format");
      }
    }
  }

  // Jira parameter validation
  if (actionLower.includes('jira')) {
    if (actionLower.includes('create_issue')) {
      if (!params.summary) {
        missing.push('summary');
        suggestions.push("Provide a brief issue summary");
      }
      if (!params.project) {
        missing.push('project');
        suggestions.push("Specify the project key (e.g., 'PROJ')");
      }
    }
  }

  return {
    valid: missing.length === 0,
    missing,
    suggestions
  };
}

export const enhancedExecuteAction = tool({
  description: 'Execute an action on a connected service. IMPORTANT: You must first use fetchActions to find available actions, then use this tool to execute one of the EXACT action names returned by fetchActions. Do not attempt to guess action names.',
  parameters: z.object({
    action: z.string().describe('The EXACT action name to execute as returned by fetchActions (e.g., GITHUB_LIST_REPOSITORIES)'),
    params: z.record(z.any()).optional().describe('The parameters required by the action'),
  }),
  execute: async ({ action, params }) => {
    try {
      // Get the current user session
      const session = await auth();

      if (!session?.user?.id) {
        return {
          success: false,
          error: 'User not authenticated',
          message: 'You need to be logged in to execute actions on connected services.'
        };
      }

      // Get user's connected services
      const connections = await getConnectionsByUserId(session.user.id);

      if (!connections || connections.length === 0) {
        return {
          success: false,
          error: 'No connected services',
          message: 'You need to connect at least one service before executing actions. Please connect a service first.',
          redirectUrl: "/profile/connections"
        };
      }

      // Extract connected app names
      const connectedApps = connections.map(conn => conn.provider.toLowerCase());
      console.log(`User has active connections to: ${connectedApps.join(', ')}`);

      // Clean up action name and extract parameters if included in the action string
      let cleanedAction = action;
      let extractedParams = { ...(params || {}) };

      // Regular expression to match the base action name (before any parameters)
      const actionNameRegex = /^([A-Z0-9_]+)(?:\s+(.*))?$/;
      const actionNameMatch = action.match(actionNameRegex);

      if (actionNameMatch && actionNameMatch[2]) {
        // We found parameters in the action name
        cleanedAction = actionNameMatch[1]; // The clean action name
        const paramsString = actionNameMatch[2]; // The parameters part

        // Try to extract parameters in format "key=value key2=value2"
        const paramPairs = paramsString.match(/(\w+)=([^\s]+)/g) || [];
        paramPairs.forEach((pair: string) => {
          const [key, value] = pair.split('=');
          if (key && value) {
            extractedParams[key] = value;
          }
        });

        console.log(`Extracted parameters from action name: ${JSON.stringify(extractedParams)}`);
        console.log(`Cleaned action name: ${cleanedAction}`);
      }

      // Use the cleaned action name
      action = cleanedAction;

      // Infer missing parameters based on action type and context
      const enhancedParams = await inferParameters(action, extractedParams);

      // Log any parameters that were inferred
      const inferredKeys = Object.keys(enhancedParams).filter(key =>
        !extractedParams[key] || extractedParams[key] !== enhancedParams[key]
      );

      if (inferredKeys.length > 0) {
        console.log(`Inferred parameters: ${inferredKeys.join(', ')}`);
        console.log(`Original params:`, extractedParams);
        console.log(`Enhanced params:`, enhancedParams);
      }

      // Use the enhanced parameters
      params = enhancedParams;

      // Validate parameters before execution
      const validation = await validateParameters(action, params);
      if (!validation.valid) {
        const missingParams = validation.missing.join(', ');
        const suggestions = validation.suggestions.join('; ');

        return {
          success: false,
          error: `Missing required parameters: ${missingParams}`,
          message: `The action "${action}" requires the following parameters: ${missingParams}. ${suggestions}`,
          missingParameters: validation.missing,
          suggestions: validation.suggestions
        };
      }

      // Find the appropriate connection for this action
      const appName = action.toLowerCase().split('_')[0];

      // Get all connections for this app
      const appConnections = connections.filter(conn =>
        conn.provider.toLowerCase() === appName &&
        conn.status === 'ACTIVE'
      );

      if (appConnections.length === 0) {
        return {
          success: false,
          error: `No active connection found for ${appName}`,
          message: `You need to connect your ${appName} account before executing this action, or your existing connection may need to be refreshed.`,
          redirectUrl: "/profile/connections"
        };
      }

      // Use the most recently created connection for all services
      let connectionId;
      const connection = appConnections.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      // Get the connection ID from the connectionId column (not the record's id)
      connectionId = connection.connectionId;

      if (connectionId) {
        connectionId = ensureUuidFormat(connectionId);
        console.log(`Using connection ID: ${connectionId} for ${appName}`);
      }

      // Verify the connection is valid
      if (!connectionId) {
        return {
          success: false,
          error: `Invalid connection for ${appName}`,
          message: `Your ${appName} connection appears to be invalid. Please reconnect your account.`,
          redirectUrl: "/profile/connections"
        };
      }



      // Initialize the Composio toolset
      const toolset = new OpenAIToolSet();

      console.log(`Executing action: ${action}`);
      console.log(`With parameters:`, params || {});

      // Execute the action
      let result;
      try {
        console.log(`Executing action ${action} with connection ID ${connectionId}`);

        result = await toolset.client.actions.execute({
          actionName: action,
          requestBody: {
            appName: appName,
            input: params || {},
            entityId: session.user.id,
            connectedAccountId: connectionId
          }
        });
      } catch (error) {
        const execError = error as Error;
        console.error("Error executing action:", execError);

        // Handle specific error types
        if (execError.message && execError.message.includes('ConnectedAccountNotFoundError')) {
          return {
            success: false,
            error: `Connection not found or expired`,
            message: `Your ${appName} connection appears to have expired or been invalidated. Please reconnect your ${appName} account and try again.`,
            redirectUrl: "/profile/connections"
          };
        }

        if (execError.message && execError.message.includes('NotFoundError')) {
          return {
            success: false,
            error: `Action not found: ${action}`,
            message: `The action "${action}" could not be found. Please verify the action name and try again.`
          };
        }

        if (execError.message && execError.message.includes('AuthenticationError')) {
          return {
            success: false,
            error: `Authentication error`,
            message: `There was an authentication error with your ${appName} connection. Please reconnect your account and try again.`,
            redirectUrl: "/profile/connections"
          };
        }

        // Re-throw for general error handling
        throw execError;
      }

      console.log(`Action executed successfully`);

      // Format the result display message - simplified to avoid redundancy with UI
      let resultMessage = `Action executed successfully.`;

      // Add minimal summary based on action type without duplicating what's shown in the UI
      if (action.includes('LIST') && Array.isArray(result.data)) {
        resultMessage += ` Found ${result.data.length} items.`;
      } else if (action.includes('CREATE')) {
        resultMessage += ` Item created successfully.`;
      } else if (action.includes('UPDATE')) {
        resultMessage += ` Item updated successfully.`;
      } else if (action.includes('DELETE')) {
        resultMessage += ` Item deleted successfully.`;
      }

      // Return the result with metadata
      return {
        success: true,
        message: resultMessage,
        result: result.data || result,
        action,
        params,
        inferredParams: inferredKeys
      };
    } catch (error) {
      console.error("Error in execute-action tool:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: 'Failed to execute action. Please check the action name and parameters and try again.'
      };
    }
  },
});
