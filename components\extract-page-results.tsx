'use client';

import { useState } from 'react';
import { FileTextIcon, ExternalLinkIcon } from 'lucide-react';
import { ToolUsageCard } from './ui/tool-usage-card';

interface ExtractPageResultsProps {
  extractData?: {
    url: string;
    title: string;
    content: string;
    message: string;
    error?: string;
    isExecuting?: boolean;
  };
}

export function ExtractPageResults({ extractData }: ExtractPageResultsProps) {
  const [showContent, setShowContent] = useState(false);

  if (!extractData) {
    // Loading state
    return (
      <ToolUsageCard
        icon={FileTextIcon}
        toolName="Extract Page"
        status="loading"
        isExecuting={true}
      >
        <div className="space-y-2">
          <div className="h-5 w-3/4 animate-pulse rounded-md bg-muted"></div>
          <div className="h-4 w-full animate-pulse rounded-md bg-muted"></div>
          <div className="h-4 w-1/2 animate-pulse rounded-md bg-muted"></div>
        </div>
      </ToolUsageCard>
    );
  }

  // Handle error cases
  if (extractData.content === 'No content extracted' || extractData.error) {
    return (
      <ToolUsageCard
        icon={FileTextIcon}
        toolName="Extract Page"
        status="error"
        errorMessage={`Error extracting content from ${extractData.url}`}
        url={extractData.url}
        isExecuting={extractData.isExecuting}
      />
    );
  }

  return (
    <ToolUsageCard
      icon={FileTextIcon}
      toolName="Extract Page"
      status="success"
      showContent={showContent}
      onToggleContent={() => setShowContent(!showContent)}
      url={extractData.url}
      isExecuting={extractData.isExecuting}
    >
      <div className="space-y-3">
        {extractData.title && (
          <div className="border border-border/50 rounded-md overflow-hidden">
            <div className="px-3 py-2 bg-gradient-to-b from-muted/10 to-muted/20 border-b border-border/40">
              <h3 className="text-sm font-medium text-primary truncate">{extractData.title}</h3>
            </div>
            <div className="p-3">
              <div className="text-xs text-muted-foreground whitespace-pre-wrap max-h-[300px] overflow-y-auto">
                {extractData.content}
              </div>
            </div>
          </div>
        )}
        {!extractData.title && (
          <div className="border border-border/50 rounded-md p-3">
            <div className="text-xs text-muted-foreground whitespace-pre-wrap max-h-[300px] overflow-y-auto">
              {extractData.content}
            </div>
          </div>
        )}
      </div>
    </ToolUsageCard>
  );
}
