/**
 * <PERSON><PERSON><PERSON> to get a valid user ID for testing
 * Run with: npx tsx lib/memory/get-test-user.ts
 */
import { config } from 'dotenv';
import { db } from '@/lib/db/client';
import { user } from '@/lib/db/schema';

// Load environment variables
config({
  path: '.env.local',
});

async function getTestUser() {
  try {
    console.log('🔍 Looking for a valid user ID...');
    
    // Get the first user from the database
    const users = await db.select().from(user).limit(1);
    
    if (users.length === 0) {
      console.error('❌ No users found in the database');
      return;
    }
    
    const testUser = users[0];
    console.log(`✅ Found user: ${testUser.email}`);
    console.log(`✅ User ID: ${testUser.id}`);
    console.log('\nUse this ID in your test-memory.ts script:');
    console.log(`const TEST_USER_ID = '${testUser.id}';`);
  } catch (error) {
    console.error('❌ Error getting test user:', error);
  } finally {
    process.exit(0);
  }
}

// Run the script
getTestUser();
