/**
 * This script updates all database client instances in the codebase to use the centralized client
 * with connection pooling and retry logic.
 * 
 * Usage: node scripts/update-db-clients.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Patterns to search for
const POSTGRES_IMPORT_PATTERN = /import\s+postgres\s+from\s+['"]postgres['"];/;
const DRIZZLE_IMPORT_PATTERN = /import\s+{\s*drizzle\s*}\s+from\s+['"]drizzle-orm\/postgres-js['"];/;
const CLIENT_CREATION_PATTERN = /const\s+client\s*=\s*postgres\(.*\);/;
const DB_CREATION_PATTERN = /const\s+db\s*=\s*drizzle\(client\);/;

// Replacement imports
const REPLACEMENT_IMPORT = `import { db, withRetry } from '@/lib/db/client';`;

// Function to recursively find files
function findFiles(dir, pattern, excludeDirs = []) {
  let results = [];
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Skip excluded directories
      if (excludeDirs.includes(file)) continue;
      
      // Recursively search subdirectories
      results = results.concat(findFiles(filePath, pattern, excludeDirs));
    } else if (pattern.test(file)) {
      results.push(filePath);
    }
  }
  
  return results;
}

// Function to update a file
function updateFile(filePath) {
  console.log(`Processing ${filePath}...`);
  
  // Skip our new client file
  if (filePath.includes('lib/db/client.ts')) {
    console.log('  Skipping our new client file');
    return false;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Check if the file imports postgres and drizzle
  const hasPostgresImport = POSTGRES_IMPORT_PATTERN.test(content);
  const hasDrizzleImport = DRIZZLE_IMPORT_PATTERN.test(content);
  
  if (!hasPostgresImport && !hasDrizzleImport) {
    console.log('  No postgres or drizzle imports found, skipping');
    return false;
  }
  
  // Check if the file creates a client and db instance
  const hasClientCreation = CLIENT_CREATION_PATTERN.test(content);
  const hasDbCreation = DB_CREATION_PATTERN.test(content);
  
  if (!hasClientCreation && !hasDbCreation) {
    console.log('  No client or db creation found, skipping');
    return false;
  }
  
  // Replace imports
  if (hasPostgresImport) {
    content = content.replace(POSTGRES_IMPORT_PATTERN, '');
    modified = true;
    console.log('  Removed postgres import');
  }
  
  if (hasDrizzleImport) {
    content = content.replace(DRIZZLE_IMPORT_PATTERN, REPLACEMENT_IMPORT);
    modified = true;
    console.log('  Replaced drizzle import with centralized client import');
  } else if (modified) {
    // Add our import if we removed the postgres import but there was no drizzle import to replace
    const importIndex = content.indexOf('import');
    if (importIndex !== -1) {
      // Find the end of the import section
      let endOfImports = content.indexOf('\n\n', importIndex);
      if (endOfImports === -1) endOfImports = content.indexOf('\n', importIndex);
      
      content = content.slice(0, endOfImports) + '\n' + REPLACEMENT_IMPORT + content.slice(endOfImports);
      console.log('  Added centralized client import');
    }
  }
  
  // Replace client creation
  if (hasClientCreation) {
    content = content.replace(CLIENT_CREATION_PATTERN, '');
    modified = true;
    console.log('  Removed client creation');
  }
  
  // Replace db creation
  if (hasDbCreation) {
    content = content.replace(DB_CREATION_PATTERN, '');
    modified = true;
    console.log('  Removed db creation');
  }
  
  // Clean up any double newlines created by our replacements
  content = content.replace(/\n\n\n+/g, '\n\n');
  
  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log('  File updated successfully');
    return true;
  }
  
  return false;
}

// Main function
function main() {
  console.log('Searching for TypeScript files with database client creation...');
  
  const rootDir = path.resolve(__dirname, '..');
  const tsFiles = findFiles(rootDir, /\.(ts|tsx)$/, ['node_modules', '.next', '.git']);
  
  console.log(`Found ${tsFiles.length} TypeScript files`);
  
  let updatedCount = 0;
  
  for (const file of tsFiles) {
    if (updateFile(file)) {
      updatedCount++;
    }
  }
  
  console.log(`\nUpdated ${updatedCount} files to use the centralized database client`);
  
  if (updatedCount > 0) {
    console.log('\nRunning type check to ensure everything still works...');
    try {
      execSync('pnpm tsc --noEmit', { stdio: 'inherit' });
      console.log('\nType check passed! All updates are compatible.');
    } catch (error) {
      console.error('\nType check failed. You may need to manually fix some imports.');
    }
  }
}

main();
