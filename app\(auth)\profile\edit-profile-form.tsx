'use client';

import { useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';

interface UserData {
  email: string;
  firstName: string;
  lastName: string;
}

export function EditProfileForm({ userData }: { userData: UserData }) {
  const router = useRouter();
  const [isUpdating, setIsUpdating] = useState(false);
  const [formData, setFormData] = useState({
    firstName: userData.firstName || '',
    lastName: userData.lastName || ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleUpdateProfile = async () => {
    setIsUpdating(true);
    try {
      const response = await fetch('/api/profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          firstName: formData.firstName,
          lastName: formData.lastName
        })
      });

      if (response.ok) {
        toast.success('Profile updated successfully');
      } else {
        const data = await response.json();
        toast.error(data.message || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('An error occurred while updating your profile');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/signout', { method: 'POST' });
      router.push('/login');
    } catch (error) {
      console.error('Error signing out:', error);
      toast.error('Failed to sign out');
    }
  };

  const handleDeleteAccount = async () => {
    const confirmed = window.confirm('Are you sure you want to delete your account? This action cannot be undone.');
    if (!confirmed) return;

    try {
      const response = await fetch('/api/user', {
        method: 'DELETE'
      });

      if (response.ok) {
        toast.success('Account deleted successfully');
        router.push('/login');
      } else {
        const data = await response.json();
        toast.error(data.message || 'Failed to delete account');
      }
    } catch (error) {
      console.error('Error deleting account:', error);
      toast.error('An error occurred while deleting your account');
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col items-center justify-center">
        <div className="relative size-24 mb-4">
          <Image
            src={`https://avatar.vercel.sh/${userData.email}`}
            alt="User Avatar"
            fill
            className="rounded-full"
          />
        </div>
        <p className="text-sm text-gray-500 dark:text-zinc-400">
          Avatar is automatically generated based on your email
        </p>
      </div>

      <div className="flex flex-col gap-6">
        {/* Name Fields */}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex flex-col gap-2">
            <Label
              htmlFor="firstName"
              className="text-zinc-600 font-normal dark:text-zinc-400"
            >
              First Name
            </Label>
            <Input
              id="firstName"
              name="firstName"
              className="bg-muted text-md md:text-sm"
              type="text"
              placeholder="John"
              value={formData.firstName}
              onChange={handleChange}
            />
          </div>
          <div className="flex flex-col gap-2">
            <Label
              htmlFor="lastName"
              className="text-zinc-600 font-normal dark:text-zinc-400"
            >
              Last Name
            </Label>
            <Input
              id="lastName"
              name="lastName"
              className="bg-muted text-md md:text-sm"
              type="text"
              placeholder="Doe"
              value={formData.lastName}
              onChange={handleChange}
            />
          </div>
        </div>

        {/* Email Field */}
        <div className="flex flex-col gap-2">
          <Label
            htmlFor="email"
            className="text-zinc-600 font-normal dark:text-zinc-400"
          >
            Email Address
          </Label>
          <Input
            id="email"
            name="email"
            className="bg-muted text-md md:text-sm opacity-70 cursor-not-allowed"
            type="email"
            placeholder="<EMAIL>"
            autoComplete="email"
            required
            value={userData.email}
            readOnly
            disabled
          />
          <p className="text-xs text-gray-500 dark:text-zinc-400">
            Email cannot be changed
          </p>
        </div>

        {/* Update Profile Button */}
        <Button
          onClick={handleUpdateProfile}
          className="w-full"
          disabled={isUpdating}
        >
          {isUpdating ? (
            <>
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent mr-2" />
              Updating...
            </>
          ) : (
            'Update Profile'
          )}
        </Button>

        {/* Manage Connections Button */}
        <Button
          onClick={() => router.push('/profile/connections')}
          variant="outline"
          className="w-full"
        >
          Manage Connections
        </Button>

        {/* Reset Password Button */}
        <Button
          onClick={() => router.push('/reset-password-request')}
          variant="outline"
          className="w-full"
        >
          Reset Password
        </Button>

        {/* Logout Button */}
        <Button
          onClick={handleLogout}
          variant="outline"
          className="w-full"
        >
          Logout
        </Button>

        {/* Delete Account Button */}
        <Button
          onClick={handleDeleteAccount}
          variant="destructive"
          className="w-full"
        >
          Delete Account
        </Button>
      </div>

      <div className="flex justify-center mt-4">
        <Button
          variant="ghost"
          onClick={() => router.push('/')}
          className="text-sm text-gray-600 dark:text-zinc-400"
        >
          Back to Chat
        </Button>
      </div>
    </div>
  );
}
