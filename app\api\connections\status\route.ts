import { NextResponse } from 'next/server';
import { OpenAIToolSet } from "composio-core";
import { auth } from '@/app/(auth)/auth';
import { saveConnection } from '@/lib/db/queries';

export async function GET(request: Request) {
  try {
    const session = await auth();

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const provider = searchParams.get('provider');

    if (!provider) {
      return NextResponse.json(
        { error: 'Provider is required' },
        { status: 400 }
      );
    }

    // Initialize the Composio toolset
    const toolset = new OpenAIToolSet();

    // Get the entity for the user - ensure we have a string ID
    const userId = session.user.id as string;
    const entity = await toolset.getEntity(userId);

    try {
      // Check if the entity has a connection for the specified provider
      const connections = await entity.getConnections();

      // Find the connection for the specified provider
      const providerConnection = connections.find(conn =>
        conn.appName.toLowerCase() === provider.toLowerCase()
      );

      if (providerConnection) {
        // Check if we already have this connection in our database
        // If not, save it
        await saveConnectionToDatabase(
          userId,
          provider,
          providerConnection.id,
          providerConnection.status
        );

        return NextResponse.json({
          success: true,
          connected: true,
          status: providerConnection.status,
          connectionId: providerConnection.id,
        });
      } else {
        return NextResponse.json({
          success: true,
          connected: false,
          status: "NOT_CONNECTED",
          connectionId: null,
        });
      }
    } catch (connError) {
      console.log(`Error getting ${provider} connection, assuming not connected:`, connError);
      return NextResponse.json({
        success: true,
        connected: false,
        status: "ERROR",
        connectionId: null,
        errorDetails: connError instanceof Error ? connError.message : String(connError)
      });
    }
  } catch (error) {
    console.error(`Error checking ${request.url} connection status:`, error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}

// Helper function to save connection to database
async function saveConnectionToDatabase(
  userId: string,
  provider: string,
  connectionId: string,
  status: string
) {
  try {
    // Save the connection using our database function
    // This will handle creating a new connection or updating an existing one
    await saveConnection({
      userId,
      provider,
      connectionId,
      status: status as 'ACTIVE' | 'EXPIRED' | 'REVOKED',
      metadata: null
    });
  } catch (error) {
    console.error('Error saving connection to database:', error);
    // We don't want to fail the whole request if this fails
    // Just log the error
  }
}
