import { describe, expect, it } from 'vitest';
import { getEnhancedSystemPrompt } from '../lib/ai/enhanced-prompts';
import { optimizedSystemPrompt } from '../lib/ai/optimized-prompts';

describe('Optimized Prompt Tests', () => {
  it('should return the optimized prompt when useOptimizedPrompt is true', () => {
    const prompt = getEnhancedSystemPrompt({
      selectedChatModel: 'chat-model-reasoning',
      useEnhancedPrompt: true,
      useOptimizedPrompt: true,
    });

    expect(prompt).toBe(optimizedSystemPrompt);
  });

  it('should return the enhanced prompt when useOptimizedPrompt is false', () => {
    const prompt = getEnhancedSystemPrompt({
      selectedChatModel: 'chat-model-reasoning',
      useEnhancedPrompt: true,
      useOptimizedPrompt: false,
    });

    expect(prompt).not.toBe(optimizedSystemPrompt);
    expect(prompt).toContain('MENTAL MODEL FOR TASK HANDLING');
  });

  it('should include artifacts prompt for non-reasoning models', () => {
    const prompt = getEnhancedSystemPrompt({
      selectedChatModel: 'chat-model',
      useEnhancedPrompt: true,
      useOptimizedPrompt: true,
    });

    expect(prompt).toContain(optimizedSystemPrompt);
    expect(prompt).toContain('Artifacts is a special user interface mode');
  });

  it('should include URL handling instructions', () => {
    const prompt = getEnhancedSystemPrompt({
      selectedChatModel: 'chat-model-reasoning',
      useEnhancedPrompt: true,
      useOptimizedPrompt: true,
    });

    expect(prompt).toContain('URL AND EXTERNAL CONTENT HANDLING');
    expect(prompt).toContain('For platform-specific URLs');
    expect(prompt).toContain('PRINCIPLES FOR EFFECTIVE ACTION QUERIES');
    expect(prompt).toContain('TWO-STEP MENTAL MODEL FOR EXTERNAL CONTENT');
  });

  it('should include failure recovery strategies', () => {
    const prompt = getEnhancedSystemPrompt({
      selectedChatModel: 'chat-model-reasoning',
      useEnhancedPrompt: true,
      useOptimizedPrompt: true,
    });

    expect(prompt).toContain('FAILURE RECOVERY STRATEGIES');
    expect(prompt).toContain('NEVER state something is impossible without trying at least 3 different approaches');
    expect(prompt).toContain('INTELLIGENT RECOVERY STRATEGIES');
  });

  it('should include verification strategies for URL access', () => {
    const prompt = getEnhancedSystemPrompt({
      selectedChatModel: 'chat-model-reasoning',
      useEnhancedPrompt: true,
      useOptimizedPrompt: true,
    });

    expect(prompt).toContain('For URL access attempts');
  });

  it('should include guidance for action selection', () => {
    const prompt = getEnhancedSystemPrompt({
      selectedChatModel: 'chat-model-reasoning',
      useEnhancedPrompt: true,
      useOptimizedPrompt: true,
    });

    expect(prompt).toContain('CRITICAL: When the exact action you need isn\'t returned');
  });

  it('should include action naming pattern recognition', () => {
    const prompt = getEnhancedSystemPrompt({
      selectedChatModel: 'chat-model-reasoning',
      useEnhancedPrompt: true,
      useOptimizedPrompt: true,
    });

    expect(prompt).toContain('ACTION NAMING PATTERN RECOGNITION');
    expect(prompt).toContain('SERVICE_OPERATION_RESOURCE_CONTEXT');
    expect(prompt).toContain('When user refers to "my" resources');
    expect(prompt).toContain('QUERY CONSTRUCTION EXCELLENCE');
    expect(prompt).toContain('BAD: "list my github repositories"');
    expect(prompt).toContain('GOOD: "list repositories for the authenticated github user"');
  });

  it('should emphasize detailed query construction', () => {
    const prompt = getEnhancedSystemPrompt({
      selectedChatModel: 'chat-model-reasoning',
      useEnhancedPrompt: true,
      useOptimizedPrompt: true,
    });

    expect(prompt).toContain('Craft COMPREHENSIVE, DETAILED queries');
    expect(prompt).toContain('NEVER use short, simplistic queries');
    expect(prompt).toContain('Aim for queries that are 5-10 words minimum');
    expect(prompt).toContain('DRAMATICALLY EXPAND your queries');
    expect(prompt).toContain('If a short query fails, make it 2-3 TIMES LONGER');
  });
});
