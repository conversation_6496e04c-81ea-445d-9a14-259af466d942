import type { NextAuthConfig } from 'next-auth';

export const authConfig = {
  pages: {
    signIn: '/login',
    newUser: '/',
  },
  providers: [
    // added later in auth.ts since it requires bcrypt which is only compatible with Node.js
    // while this file is also used in non-Node.js environments
  ],
  callbacks: {
    authorized({ auth, request: { nextUrl } }) {
      const isLoggedIn = !!auth?.user;
      const isOnChat = nextUrl.pathname.startsWith('/');
      const isOnRegister = nextUrl.pathname.startsWith('/register');
      const isOnRegisterConfirmation = nextUrl.pathname.startsWith('/register/confirmation');
      const isOnLogin = nextUrl.pathname.startsWith('/login');
      const isOnProfile = nextUrl.pathname.startsWith('/profile');
      const isOnConfirm = nextUrl.pathname.startsWith('/confirm');
      const isOnAuthCallback = nextUrl.pathname.startsWith('/auth/callback');
      const isOnForgotPassword = nextUrl.pathname.startsWith('/reset-password-request');
      const isOnResetPassword = nextUrl.pathname.startsWith('/reset-password');

      if (isLoggedIn && (isOnLogin || isOnRegister)) {
        return Response.redirect(new URL('/', nextUrl as unknown as URL));
      }

      if (isOnRegister || isOnRegisterConfirmation || isOnLogin || isOnConfirm || isOnAuthCallback || isOnForgotPassword || isOnResetPassword) {
        return true; // Always allow access to register, login, confirmation, password reset, and auth callback pages
      }

      if (isOnChat || isOnProfile) {
        if (isLoggedIn) return true;
        return false; // Redirect unauthenticated users to login page
      }

      if (isLoggedIn) {
        return Response.redirect(new URL('/', nextUrl as unknown as URL));
      }

      return true;
    },
  },
} satisfies NextAuthConfig;
