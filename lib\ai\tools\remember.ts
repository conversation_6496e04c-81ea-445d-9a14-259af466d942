import { tool } from 'ai';
import { z } from 'zod';
import { createMemory } from '@/lib/memory/queries';
import { Session } from 'next-auth';

/**
 * Selective memory tool - only for valuable information in 4 key categories
 */
export const createRememberTool = ({ session }: { session: Session | null }) => {
  return tool({
    name: 'remember',
    description: 'Remember valuable information that fits 4 categories: PERSONAL (identity, background, role), GOALS (objectives, projects, what they\'re building), APPS (workflow tools and how they use them - specific repos, notion pages, calendar setups, integrations), PREFERENCES (how they like things done, choices). Only use when information clearly adds understanding. Skip greetings, acknowledgments, conversational filler.',
    parameters: z.object({
      memory: z.string().describe('Valuable information that clearly fits one of the 4 categories and adds understanding of the user'),
      category: z.enum(['personal', 'goals', 'apps', 'preferences']).describe('Must be one of: personal, goals, apps, preferences'),
      context: z.string().optional().describe('Why this information is valuable for future assistance'),
    }),
    execute: async ({ memory, category, context }) => {
      if (!session?.user?.id) {
        return { error: 'User not authenticated', success: false };
      }

      try {
        // Check for similar existing memories to prevent duplicates
        console.log(`🔍 Checking for similar memories before creating: "${memory}"`);

        const { getRelevantMemories } = await import('@/lib/memory/queries');
        const similarMemories = await getRelevantMemories({
          userId: session.user.id,
          query: memory,
          limit: 3,
          similarityThreshold: 0.7 // High threshold for duplicate detection
        });

        // Log similar memories for awareness but don't block creation
        if (similarMemories && similarMemories.length > 0) {
          const mostSimilar = similarMemories[0];
          if (mostSimilar.similarity > 0.8) {
            console.log(`ℹ️ Found highly similar memory (${mostSimilar.similarity}): "${mostSimilar.content}" - Adding anyway as requested`);
          }
        }

        // Use the provided category (now required)
        const finalCategory = category;

        // Enhance memory content with context
        let enhancedMemory = memory;
        if (context) {
          enhancedMemory += ` (Context: ${context})`;
        }

        console.log(`🧠 Creating new memory: "${enhancedMemory}" with category: "${finalCategory}"`);

        const result = await createMemory({
          userId: session.user.id,
          content: enhancedMemory,
          category: finalCategory,
        });

        console.log(`✅ Successfully created memory with ID: ${result[0].id}`);

        return {
          success: true,
          message: `Remembered: ${memory}`,
          memoryId: result[0].id,
          memory: enhancedMemory,
          category: finalCategory,
          isExecuting: false
        };
      } catch (error) {
        console.error('Failed to create memory:', error);

        // Provide detailed error information
        let errorMessage = 'Unknown error';
        if (error instanceof Error) {
          errorMessage = error.message;
          console.error('Error details:', error.stack);
        }

        return {
          error: 'Failed to store memory',
          success: false,
          message: errorMessage,
          isExecuting: false
        };
      }
    },
  });
};


