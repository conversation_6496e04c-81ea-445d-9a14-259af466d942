/**
 * Date and time utility functions using Abstract API for accurate time information
 * This helps solve the date misalignment issue where the agent uses incorrect dates in queries
 */

/**
 * Interface for the Abstract API current time response
 */
interface AbstractCurrentTimeResponse {
  datetime: string;
  timezone_name: string;
  timezone_location: string;
  timezone_abbreviation: string;
  gmt_offset: number;
  is_dst: boolean;
  requested_location: string;
  latitude: number;
  longitude: number;
}

/**
 * Gets the current date and time for a specific location using Abstract API
 * @param location The location to get the current time for (city, country, coordinates, or IP)
 * @returns The current date and time information for the location
 */
export async function getCurrentTimeForLocation(location: string): Promise<AbstractCurrentTimeResponse> {
  try {
    const apiKey = process.env.ABSTRACT_API_KEY;
    if (!apiKey) {
      console.warn('⚠️ ABSTRACT_API_KEY not found, using fallback time');
      throw new Error('ABSTRACT_API_KEY is not defined in environment variables');
    }

    const url = `https://timezone.abstractapi.com/v1/current_time?api_key=${apiKey}&location=${encodeURIComponent(location)}`;
    const response = await fetch(url);

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      console.warn(`⚠️ Abstract API error (${response.status}): ${errorText}, using fallback time`);
      throw new Error(`Abstract API returned status ${response.status}: ${errorText}`);
    }

    const data = await response.json();
    console.log(`✅ Successfully got time from Abstract API for ${location}`);
    return data as AbstractCurrentTimeResponse;
  } catch (error) {
    console.warn('⚠️ Abstract API failed, using system time with Morocco timezone adjustment:', error);

    // Fallback: Create Morocco time manually
    const now = new Date();

    // Morocco is UTC+1 (no DST since 2018)
    const moroccoTime = new Date(now.getTime() + (1 * 60 * 60 * 1000)); // Add 1 hour for UTC+1

    return {
      datetime: moroccoTime.toISOString().replace('T', ' ').substring(0, 19),
      timezone_name: 'Africa/Casablanca',
      timezone_location: 'Morocco',
      timezone_abbreviation: '+01',
      gmt_offset: 1,
      is_dst: false, // Morocco doesn't use DST since 2018
      requested_location: location,
      latitude: 33.5731, // Casablanca coordinates
      longitude: -7.5898
    };
  }
}

/**
 * Gets the current date in YYYY-MM-DD format for a specific location
 * @param location The location to get the current date for
 * @returns The current date in YYYY-MM-DD format
 */
export async function getCurrentDateForLocation(location: string): Promise<string> {
  const timeData = await getCurrentTimeForLocation(location);
  return timeData.datetime.split(' ')[0];
}

/**
 * Gets the current time in HH:MM:SS format for a specific location
 * @param location The location to get the current time for
 * @returns The current time in HH:MM:SS format
 */
export async function getCurrentTimeStringForLocation(location: string): Promise<string> {
  const timeData = await getCurrentTimeForLocation(location);
  return timeData.datetime.split(' ')[1];
}

/**
 * Gets the current date and time as a Date object for a specific location
 * @param location The location to get the current date and time for
 * @returns A Date object representing the current date and time
 */
export async function getCurrentDateObjectForLocation(location: string): Promise<Date> {
  const timeData = await getCurrentTimeForLocation(location);
  return new Date(timeData.datetime.replace(' ', 'T') + 'Z');
}

/**
 * Formats a date string in a consistent format
 * @param dateString The date string to format
 * @param format The format to use (default: 'YYYY-MM-DD')
 * @returns The formatted date string
 */
export function formatDate(dateString: string, format: string = 'YYYY-MM-DD'): string {
  const date = new Date(dateString);

  // Replace format tokens with actual values
  return format
    .replace('YYYY', date.getFullYear().toString())
    .replace('MM', (date.getMonth() + 1).toString().padStart(2, '0'))
    .replace('DD', date.getDate().toString().padStart(2, '0'))
    .replace('HH', date.getHours().toString().padStart(2, '0'))
    .replace('mm', date.getMinutes().toString().padStart(2, '0'))
    .replace('ss', date.getSeconds().toString().padStart(2, '0'));
}

/**
 * Gets a human-readable description of the current date for a location
 * @param location The location to get the current date for
 * @returns A human-readable description of the current date
 */
export async function getHumanReadableDateForLocation(location: string): Promise<string> {
  const timeData = await getCurrentTimeForLocation(location);
  const date = new Date(timeData.datetime.replace(' ', 'T'));

  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    timeZone: timeData.timezone_location
  });
}
