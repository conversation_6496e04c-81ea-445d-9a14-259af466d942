'use server';

import { NextRequest, NextResponse } from 'next/server';
import postgres from 'postgres';

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const email = url.searchParams.get('email');

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    console.log(`Directly confirming user: ${email}`);

    // Create a direct database connection
    const client = postgres(process.env.POSTGRES_URL!);

    // First, check if the user exists
    const checkUser = await client`
      SELECT "id", "email", "confirmed"
      FROM "User"
      WHERE "email" = ${email}
    `;

    console.log('User check result:', checkUser);

    if (checkUser.length === 0) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Execute a direct SQL update
    const result = await client`
      UPDATE "User"
      SET "confirmed" = true
      WHERE "email" = ${email}
      RETURNING "id", "email", "confirmed"
    `;

    console.log('Update result:', result);

    // Close the connection
    await client.end();

    return NextResponse.json({ success: true, result });
  } catch (error) {
    console.error('Error confirming user:', error);
    return NextResponse.json({ error: 'Failed to confirm user' }, { status: 500 });
  }
}
