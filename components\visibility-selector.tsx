'use client';

import { ReactNode, useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

import {
  CheckCircleFillIcon,
  ChevronDownIcon,
  GlobeIcon,
  LockIcon,
} from './icons';
import { useChatVisibility } from '@/hooks/use-chat-visibility';

export type VisibilityType = 'private' | 'public';

const visibilities: Array<{
  id: VisibilityType;
  label: string;
  description: string;
  icon: ReactNode;
}> = [
  {
    id: 'private',
    label: 'Private',
    description: 'Only you can access this chat',
    icon: <LockIcon />,
  },
  {
    id: 'public',
    label: 'Public',
    description: 'Anyone with the link can access this chat',
    icon: <GlobeIcon />,
  },
];

export function VisibilitySelector({
  chatId,
  className,
  selectedVisibilityType,
}: {
  chatId: string;
  selectedVisibilityType: VisibilityType;
} & React.ComponentProps<typeof Button>) {
  const [open, setOpen] = useState(false);

  const { visibilityType, setVisibilityType } = useChatVisibility({
    chatId,
    initialVisibility: selectedVisibilityType,
  });

  const selectedVisibility = useMemo(
    () => visibilities.find((visibility) => visibility.id === visibilityType),
    [visibilityType],
  );

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger
        asChild
        className={cn(
          'w-fit data-[state=open]:bg-accent/20 data-[state=open]:border-primary/50 data-[state=open]:text-foreground',
          className,
        )}
      >
        <Button
          variant="outline"
          className="hidden md:flex md:px-2 md:h-[34px] mx-auto bg-transparent hover:bg-accent/40 hover:border-primary/50 border-border/70 dark:border-border/40 dark:hover:bg-accent/30 hover:text-foreground"
        >
          <div className="flex items-center">
            {selectedVisibility?.icon}
            <span className="mx-1 text-foreground">{selectedVisibility?.label}</span>
            <ChevronDownIcon className="text-foreground/70" />
          </div>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="start" className="min-w-[300px] p-1 border-border/70 dark:border-border/40 shadow-md">
        {visibilities.map((visibility) => (
          <DropdownMenuItem
            key={visibility.id}
            onSelect={() => {
              setVisibilityType(visibility.id);
              setOpen(false);
            }}
            className={cn(
              "gap-4 group/item flex flex-row justify-between items-center rounded-md px-3 py-2 my-1",
              // Base styles for non-selected items
              visibility.id !== visibilityType && "text-foreground",

              // Selected item styles
              visibility.id === visibilityType && visibility.id === "private" && "bg-primary text-white dark:bg-primary",
              visibility.id === visibilityType && visibility.id === "public" && "bg-blue-600 text-white dark:bg-blue-700",

              // Hover styles for non-selected items
              visibility.id !== visibilityType && visibility.id === "private" && "hover:bg-primary hover:text-white dark:hover:bg-primary",
              visibility.id !== visibilityType && visibility.id === "public" && "hover:bg-blue-600 hover:text-white dark:hover:bg-blue-700"
            )}
            data-active={visibility.id === visibilityType}
          >
            <div className="flex flex-row gap-2 items-center">
              <span className={cn(
                // Base icon styles
                visibility.id === "private" && visibility.id !== visibilityType && "text-primary/80 dark:text-primary/90",
                visibility.id === "public" && visibility.id !== visibilityType && "text-blue-600/80 dark:text-blue-500/90",

                // Selected item icon styles
                visibility.id === visibilityType && "text-white",

                // Hover icon styles for non-selected items
                visibility.id !== visibilityType && "group-hover/item:text-white"
              )}>
                {visibility.icon}
              </span>
              <div className="flex flex-col gap-1 items-start">
                <span className={cn(
                  "font-medium",
                  // Selected item text is white
                  visibility.id === visibilityType && "text-white",
                  // Non-selected items turn white on hover
                  visibility.id !== visibilityType && "group-hover/item:text-white"
                )}>{visibility.label}</span>
                {visibility.description && (
                  <div className={cn(
                    "text-xs",
                    // Selected item description is white with slight opacity
                    visibility.id === visibilityType && "text-white/80",
                    // Non-selected items have muted text that turns white on hover
                    visibility.id !== visibilityType && "text-muted-foreground group-hover/item:text-white/80"
                  )}>
                    {visibility.description}
                  </div>
                )}
              </div>
            </div>
            <div className={cn(
              "opacity-0 group-data-[active=true]/item:opacity-100",
              // Always white for selected items
              visibility.id === visibilityType && "text-white",
              // Non-selected items have colored checkmark that turns white on hover
              visibility.id !== visibilityType && visibility.id === "private" && "text-primary dark:text-primary group-hover/item:text-white",
              visibility.id !== visibilityType && visibility.id === "public" && "text-blue-600 dark:text-blue-500 group-hover/item:text-white"
            )}>
              <CheckCircleFillIcon />
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
