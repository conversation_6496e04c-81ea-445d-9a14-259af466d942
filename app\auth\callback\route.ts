'use server';

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';
import { syncUserWithDatabase, markUserAsConfirmed } from '@/lib/supabase/user-sync';
import { confirmUserDirectly } from '@/lib/db/confirm-user';

/**
 * This route handles Supabase Auth callbacks
 * It's used when users click on the verification link in their email
 */
export async function GET(request: NextRequest) {
  try {
    const requestUrl = new URL(request.url);
    const code = requestUrl.searchParams.get('code');

    console.log('Auth callback received with code:', code ? 'present' : 'missing');

    if (code) {
      const supabase = await createServerClient();

      // Exchange the code for a session
      console.log('Exchanging code for session...');
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);

      if (error) {
        console.error('Error exchanging code for session:', error);
        return NextResponse.redirect(new URL('/confirm?error=auth_callback_error', requestUrl.origin));
      }

      console.log('Session exchange successful, user data:', data.user ? { id: data.user.id, email: data.user.email } : 'no user data');

      // If we have a user, sync them with our database and mark as confirmed
      if (data.user?.email) {
        console.log('Syncing user with database:', data.user.email);
        await syncUserWithDatabase(data.user.email);

        console.log('Marking user as confirmed:', data.user.email);
        const confirmResult = await markUserAsConfirmed(data.user.email);
        console.log('Confirmation result:', confirmResult);

        if (!confirmResult.success) {
          console.error('Failed to mark user as confirmed, trying direct approach');
          // If the regular approach fails, try the direct approach
          const directResult = await confirmUserDirectly(data.user.email);
          console.log('Direct confirmation result:', directResult);
        }
      }

      // Redirect to the home page
      console.log('Redirecting to home page');
      return NextResponse.redirect(new URL('/', requestUrl.origin));
    }

    // If no code is provided, redirect to the login page
    return NextResponse.redirect(new URL('/login', requestUrl.origin));
  } catch (error) {
    console.error('Auth callback error:', error);
    return NextResponse.redirect(new URL('/confirm?error=auth_callback_error', request.nextUrl.origin));
  }
}
