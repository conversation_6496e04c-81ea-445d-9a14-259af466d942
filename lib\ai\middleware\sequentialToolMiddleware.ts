import { LanguageModelV1Middleware } from 'ai';

export const sequentialToolMiddleware = (): LanguageModelV1Middleware => {
  // Keep track of which tools have been used in the current conversation
  const toolUsageState = new Map<string, {
    toolsInProgress: Set<string>,
    lastToolName: string | null,
    pendingAnalysis: boolean
  }>();

  return async (params: any, runModel: any) => {
    // Extract conversation ID or create one if not present
    const conversationId = params.id || 'default';

    // Initialize state for this conversation if not exists
    if (!toolUsageState.has(conversationId)) {
      toolUsageState.set(conversationId, {
        toolsInProgress: new Set(),
        lastToolName: null,
        pendingAnalysis: false
      });
    }

    const state = toolUsageState.get(conversationId)!;

    // Check if this is a tool call message
    if (params.messages.length > 0) {
      const lastMessage = params.messages[params.messages.length - 1];

      // If the last message is from the assistant and has tool calls
      if (lastMessage.role === 'assistant' && lastMessage.toolCalls && lastMessage.toolCalls.length > 0) {
        // If we're in analysis mode, let it proceed (no tool calls will be made)
        if (state.pendingAnalysis) {
          state.pendingAnalysis = false;
          return runModel(params);
        }

        // If there are multiple tool calls of the same type, only allow one
        const toolCalls = lastMessage.toolCalls;

        // Handle search tool calls
        const searchToolCalls = toolCalls.filter((call: any) =>
          call.name === 'searchInternet'
        );

        // Handle extract page tool calls
        const extractToolCalls = toolCalls.filter((call: any) =>
          call.name === 'extractPage'
        );

        // If we have multiple search tool calls, only keep the first one
        if (searchToolCalls.length > 1) {
          // Modify the message to only include the first search tool call
          const firstSearchToolCall = searchToolCalls[0];
          const otherToolCalls = toolCalls.filter((call: any) =>
            call.name !== 'searchInternet'
          );

          lastMessage.toolCalls = [...otherToolCalls, firstSearchToolCall];

          // Mark that we need to analyze results before proceeding
          state.pendingAnalysis = true;
        }

        // If we have multiple extract page tool calls, only keep the first one
        if (extractToolCalls.length > 1) {
          // Modify the message to only include the first extract tool call
          const firstExtractToolCall = extractToolCalls[0];
          const otherToolCalls = toolCalls.filter((call: any) =>
            call.name !== 'extractPage'
          );

          lastMessage.toolCalls = [...otherToolCalls, firstExtractToolCall];

          // Mark that we need to analyze results before proceeding
          state.pendingAnalysis = true;
        }
      }

      // If the last message is a tool result, mark that we need to analyze it
      if (lastMessage.role === 'tool') {
        state.pendingAnalysis = true;
      }
    }

    return runModel(params);
  };
};
