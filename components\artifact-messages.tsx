import { PreviewMessage, ThinkingMessage } from './message';
import { useScrollToBottom } from './use-scroll-to-bottom';
import { Vote } from '@/lib/db/schema';
import { UIMessage } from 'ai';
import { memo, useEffect, useRef } from 'react';
import equal from 'fast-deep-equal';
import { UIArtifact } from './artifact';
import { UseChatHelpers } from '@ai-sdk/react';
import { ScrollToBottomButton } from './scroll-to-bottom-button';
import { WorkingIndicator } from './working-indicator';

interface ArtifactMessagesProps {
  chatId: string;
  status: UseChatHelpers['status'];
  votes: Array<Vote> | undefined;
  messages: Array<UIMessage>;
  setMessages: UseChatHelpers['setMessages'];
  reload: UseChatHelpers['reload'];
  isReadonly: boolean;
  artifactStatus: UIArtifact['status'];
}

function PureArtifactMessages({
  chatId,
  status,
  votes,
  messages,
  setMessages,
  reload,
  isReadonly,
}: ArtifactMessagesProps) {
  const [messagesContainerRef, messagesEndRef, scrollToBottom] =
    useScrollToBottom<HTMLDivElement>();

  // Auto-scroll only when status changes to submitted (new user message)
  // This is less aggressive than scrolling on every status change
  useEffect(() => {
    if (status === 'submitted') {
      scrollToBottom();
    }
  }, [status, scrollToBottom]);

  // Auto-scroll when a new user message is added, but not for every AI response chunk
  // This prevents aggressive scrolling during streaming
  const prevMessagesLengthRef = useRef(messages.length);
  useEffect(() => {
    // Only scroll when a completely new message is added (not just streaming chunks)
    if (messages.length > prevMessagesLengthRef.current) {
      // Check if the newest message is from the user
      const newestMessage = messages[messages.length - 1];
      if (newestMessage && newestMessage.role === 'user') {
        scrollToBottom();
      }
    }
    prevMessagesLengthRef.current = messages.length;
  }, [messages, scrollToBottom]);

  return (
    <>
      <div
        ref={messagesContainerRef}
        className="flex flex-col gap-4 h-full items-center overflow-y-scroll px-4 pt-20 relative"
      >
        {messages.map((message, index) => (
          <PreviewMessage
            chatId={chatId}
            key={message.id}
            message={message}
            isLoading={status === 'streaming' && index === messages.length - 1}
            vote={
              votes
                ? votes.find((vote) => vote.messageId === message.id)
                : undefined
            }
            setMessages={setMessages}
            reload={reload}
            isReadonly={isReadonly}
          />
        ))}

        {status === 'submitted' &&
          messages.length > 0 &&
          messages[messages.length - 1].role === 'user' && <ThinkingMessage />}

        {status === 'streaming' && <WorkingIndicator />}

        <div
          ref={messagesEndRef}
          className="shrink-0 min-w-[24px] min-h-[24px]"
        />
      </div>

      <div className="absolute bottom-36 left-1/2 transform -translate-x-1/2 z-20">
        <ScrollToBottomButton
          containerRef={messagesContainerRef}
          endRef={messagesEndRef}
          scrollToBottom={scrollToBottom}
        />
      </div>
    </>
  );
}

function areEqual(
  prevProps: ArtifactMessagesProps,
  nextProps: ArtifactMessagesProps,
) {
  if (
    prevProps.artifactStatus === 'streaming' &&
    nextProps.artifactStatus === 'streaming'
  )
    return true;

  if (prevProps.status !== nextProps.status) return false;
  if (prevProps.status && nextProps.status) return false;
  if (prevProps.messages.length !== nextProps.messages.length) return false;
  if (!equal(prevProps.votes, nextProps.votes)) return false;

  return true;
}

export const ArtifactMessages = memo(PureArtifactMessages, areEqual);
