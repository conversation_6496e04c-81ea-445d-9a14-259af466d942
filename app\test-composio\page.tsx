'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

// Helper to extract action name from any tool object structure
function getActionName(tool: any): string | undefined {
  if (!tool) return undefined;
  
  // Handle function-style tool format
  if (tool.type === 'function' && tool.function?.name) {
    return tool.function.name;
  }

  // Try all possible locations of the action name
  return (
    tool.action ||
    tool.action_enum ||
    tool.actionEnum ||
    tool.name ||
    tool.actionName ||
    (typeof tool === 'string' ? tool : undefined) ||
    (tool.id && typeof tool.id === 'string' && tool.id.startsWith('ACTION_') ? tool.id : undefined)
  );
}

// Helper to get tool description
function getToolDescription(tool: any): string {
  if (!tool) return 'No description available';
  
  // Handle function-style tool format
  if (tool.type === 'function' && tool.function?.description) {
    return tool.function.description;
  }

  return tool.description || tool.title || 'No description available';
}

// Helper to get tool parameters schema
function getToolParameters(tool: any): any {
  if (!tool) return null;
  if (tool.type === 'function' && tool.function?.parameters) {
    return tool.function.parameters;
  }
  return tool.parameters || null;
}

// Helper to validate required parameters
function validateParameters(params: any, schema: any): { isValid: boolean; missing: string[] } {
  if (!schema || !schema.required) return { isValid: true, missing: [] };
  
  const missing = schema.required.filter((param: string) => {
    return !params[param] && params[param] !== 0 && params[param] !== false;
  });
  
  return {
    isValid: missing.length === 0,
    missing
  };
}

// Add helper to format parameter input based on schema type
function formatInputValue(value: string, paramSchema: any): any {
  if (!paramSchema || !paramSchema.type) return value;
  
  switch (paramSchema.type) {
    case 'integer':
      return value === '' ? undefined : parseInt(value, 10);
    case 'number':
      return value === '' ? undefined : parseFloat(value);
    case 'boolean':
      return value === 'true';
    case 'array':
      return value.split(',').map(v => v.trim());
    default:
      return value;
  }
}

export default function TestComposio() {
  const [result, setResult] = useState<any>(null);
  const [testLoading, setTestLoading] = useState(false);
  const [connectLoading, setConnectLoading] = useState(false);
  const [statusLoading, setStatusLoading] = useState(false);
  const [executeLoading, setExecuteLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [githubStatus, setGithubStatus] = useState<{connected: boolean, status: string, connectionId: string | null} | null>(null);
  const [actionResult, setActionResult] = useState<any>(null);
  const [fetchType, setFetchType] = useState<'app' | 'action' | 'tag' | 'usecase'>('app');
  const [fetchInput, setFetchInput] = useState<string>('');
  const [fetchingTools, setFetchingTools] = useState(false);
  const [fetchedTools, setFetchedTools] = useState<any[]>([]);
  const [selectedTool, setSelectedTool] = useState<any>(null);
  const [toolSchema, setToolSchema] = useState<any>(null);
  const [toolParams, setToolParams] = useState<any>({});
  const [execLoading, setExecLoading] = useState(false);
  const [execResult, setExecResult] = useState<any>(null);
  const [entityId, setEntityId] = useState<string>('default');
  const [advancedSearch, setAdvancedSearch] = useState(false);
  const [scopedToApps, setScopedToApps] = useState<string[]>([]);
  const [searchMetadata, setSearchMetadata] = useState<any>(null);
  const [isSearching, setIsSearching] = useState(false);

  // Add state for app selection in use case search
  const [availableApps] = useState(['github', 'gmail', 'jira', 'notion', 'googlecalendar']);

  // Helper to handle parameter input changes
  function handleParamChange(key: string, value: string, paramSchema: any) {
    setToolParams(prev => ({
      ...prev,
      [key]: formatInputValue(value, paramSchema)
    }));
  }

  // Function to inspect tool schema
  async function inspectToolSchema({ action }: { action: string }) {
    setError(null);
    try {
      const res = await fetch('/api/inspect-tool-schema', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action }),
      });
      const data = await res.json();
      if (data.success) {
        setToolSchema(data.schema);
      } else {
        setError(data.error || 'Failed to fetch tool schema');
      }
    } catch (err) {
      setError('Error fetching schema: ' + (err as Error).message);
    }
  }

  // Fetch tools by various filters
  async function fetchTools() {
    setFetchingTools(true);
    setFetchedTools([]);
    setSelectedTool(null);
    setToolSchema(null);
    setExecResult(null);
    setError(null);
    setSearchMetadata(null);
    setIsSearching(true);

    try {
      let body: any = {};
      
      if (fetchType === 'usecase') {
        if (!fetchInput.trim()) {
          setError('Please enter a description of what you want to do');
          setFetchingTools(false);
          setIsSearching(false);
          return;
        }

        body = {
          useCase: fetchInput,
          apps: scopedToApps.length > 0 ? scopedToApps : availableApps,
          advanced: advancedSearch
        };
      } else {
        // Handle other fetch types as before
        if (fetchType === 'app') body = { apps: [fetchInput] };
        if (fetchType === 'action') body = { actions: fetchInput.split(',').map(s => s.trim()) };
        if (fetchType === 'tag') body = { tags: [fetchInput] };
      }
      
      const res = await fetch('/api/fetch-tools', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      });
      
      const data = await res.json();
      setSearchMetadata(data.metadata);

      if (data.success) {
        setFetchedTools(data.tools || []);
      } else {
        setError(data.error || 'Failed to fetch tools');
      }
    } catch (err) {
      setError('Error fetching tools: ' + (err as Error).message);
    } finally {
      setFetchingTools(false);
      setIsSearching(false);
    }
  }

  // Execute the selected tool with proper authentication
  async function executeSelectedTool() {
    if (!selectedTool) return;
    
    setExecLoading(true);
    setExecResult(null);
    setError(null);

    try {
      const actionName = getActionName(selectedTool);
      if (!actionName) {
        throw new Error('Could not determine action name from selected tool');
      }

      // Get GitHub connection ID if available
      const useGitHubConnection = actionName.startsWith('GITHUB_') && githubStatus?.connectionId;

      const res = await fetch('/api/execute-tool', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: actionName,
          params: toolParams,
          // For GitHub actions, use the GitHub connection if available
          ...(useGitHubConnection ? {
            connectedAccountId: githubStatus.connectionId
          } : {
            entityId: entityId !== 'default' ? entityId : undefined
          })
        }),
      });

      const data = await res.json();
      
      if (data.error || !data.successful) {
        throw new Error(data.error || 'Execution failed');
      }

      setExecResult({
        successful: true,
        data: data.data,
        raw: data
      });
    } catch (err) {
      setExecResult({
        successful: false,
        error: (err as Error).message
      });
      setError('Error executing tool: ' + (err as Error).message);
    } finally {
      setExecLoading(false);
    }
  }

  async function testConnection() {
    setTestLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/test-composio');
      const data = await response.json();
      setResult(data);
    } catch (err) {
      setError('Error testing Composio connection: ' + (err as Error).message);
    } finally {
      setTestLoading(false);
    }
  }

  async function connectGitHub() {
    setConnectLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/connect-github');
      const data = await response.json();

      if (data.success && data.redirectUrl) {
        console.log("Redirecting to:", data.redirectUrl);
        window.location.href = data.redirectUrl;
      } else {
        setError('Failed to initiate GitHub connection: ' + (data.error || 'Unknown error'));
        setConnectLoading(false);
      }
    } catch (err) {
      setError('Error connecting to GitHub: ' + (err as Error).message);
      setConnectLoading(false);
    }
  }

  async function checkGitHubStatus() {
    setStatusLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/github-status');
      const data = await response.json();

      if (data.success) {
        setGithubStatus({
          connected: data.connected,
          status: data.status,
          connectionId: data.connectionId
        });
      } else {
        setError('Failed to check GitHub connection status: ' + (data.error || 'Unknown error'));
      }
    } catch (err) {
      setError('Error checking GitHub status: ' + (err as Error).message);
    } finally {
      setStatusLoading(false);
    }
  }

  async function executeGitHubAction() {
    setExecuteLoading(true);
    setError(null);
    setActionResult(null);

    try {
      const response = await fetch('/api/execute-github-action');
      const data = await response.json();

      if (data.success) {
        setActionResult(data);
      } else {
        setError('Failed to execute GitHub action: ' + (data.error || data.message || 'Unknown error'));
      }
    } catch (err) {
      setError('Error executing GitHub action: ' + (err as Error).message);
    } finally {
      setExecuteLoading(false);
    }
  }

  // Check GitHub status on component mount
  useEffect(() => {
    checkGitHubStatus();
  }, []);

  // Update the use case search section JSX to show search status and metadata
  const renderUseCaseSection = () => {
    if (fetchType !== 'usecase') return null;

    return (
      <div className="flex flex-col gap-2">
        <div className="flex flex-col gap-2">
          <textarea
            className="border rounded px-3 py-2 min-h-[80px] text-sm"
            value={fetchInput}
            onChange={e => setFetchInput(e.target.value)}
            placeholder="Describe what you want to do in natural language...&#10;Examples:&#10;- create a new page in notion&#10;- list my upcoming google calendar events&#10;- find issues assigned to me in github"
          />
          <div className="flex gap-2 items-center text-sm">
            <label className="flex items-center gap-1">
              <input
                type="checkbox"
                checked={advancedSearch}
                onChange={e => setAdvancedSearch(e.target.checked)}
              />
              Advanced (Multi-tool chains)
            </label>
          </div>
          <div className="flex gap-2 items-center text-sm">
            <label className="w-24">Scope apps:</label>
            <select
              multiple
              className="border rounded px-2 py-1 flex-1"
              value={scopedToApps}
              onChange={e => setScopedToApps(Array.from(e.target.selectedOptions, option => option.value))}
              size={5}
            >
              {availableApps.map(app => (
                <option key={app} value={app}>{app}</option>
              ))}
            </select>
            <div className="text-xs text-gray-500">
              {scopedToApps.length === 0 ? '(All apps)' : `(${scopedToApps.length} selected)`}
            </div>
          </div>
        </div>

        {/* Search Status and Metadata */}
        {isSearching && (
          <div className="text-sm text-blue-600">
            Searching for relevant tools...
          </div>
        )}
        
        {searchMetadata && !isSearching && (
          <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
            <div>Search completed:</div>
            {searchMetadata.relevantActionsFound > 0 ? (
              <ul className="list-disc list-inside">
                <li>Found {searchMetadata.relevantActionsFound} relevant actions</li>
                <li>Fetched {searchMetadata.toolsFetched} tool definitions</li>
                <li>Scoped to: {Array.isArray(searchMetadata.searchScope) ? 
                    searchMetadata.searchScope.join(', ') : 
                    searchMetadata.searchScope}</li>
              </ul>
            ) : (
              <div className="text-yellow-600">
                No relevant tools found. Try:
                <ul className="list-disc list-inside mt-1">
                  <li>Using different keywords</li>
                  <li>Selecting different apps</li>
                  <li>Being more specific about the task</li>
                </ul>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  // Add renderParameterInputs function
  const renderParameterInputs = () => {
    if (!selectedTool) return null;
    
    const parameters = getToolParameters(selectedTool);
    if (!parameters || !parameters.properties) {
      return (
        <div className="text-xs italic text-gray-400">
          No parameter schema available
        </div>
      );
    }

    return Object.entries(parameters.properties).map(([key, schema]: [string, any]) => (
      <div key={key} className="flex gap-2 items-start">
        <div className="w-32 text-xs">
          <div className="font-mono">{key}</div>
          {parameters.required?.includes(key) && (
            <div className="text-red-500">Required</div>
          )}
          <div className="text-gray-500">({schema.type})</div>
        </div>
        <div className="flex-1">
          {schema.type === 'boolean' ? (
            <select
              className="border rounded px-2 py-1 w-full"
              value={toolParams[key]?.toString() || ''}
              onChange={e => handleParamChange(key, e.target.value, schema)}
            >
              <option value="">Select...</option>
              <option value="true">True</option>
              <option value="false">False</option>
            </select>
          ) : (
            <input
              className="border rounded px-2 py-1 w-full"
              value={toolParams[key] || ''}
              onChange={e => handleParamChange(key, e.target.value, schema)}
              placeholder={schema.description || ''}
              type={schema.type === 'integer' || schema.type === 'number' ? 'number' : 'text'}
              required={parameters.required?.includes(key)}
            />
          )}
          {schema.description && (
            <div className="text-xs text-gray-500 mt-1">{schema.description}</div>
          )}
          {schema.default !== undefined && (
            <div className="text-xs text-blue-500 mt-1">
              Default: {JSON.stringify(schema.default)}
            </div>
          )}
        </div>
      </div>
    ));
  };

  return (
    <div className="p-8 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Composio Connection Test</h1>

      <div className="flex flex-wrap gap-4 mb-6">
        <button
          onClick={testConnection}
          disabled={testLoading}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-blue-300"
        >
          {testLoading ? 'Testing...' : 'Test Composio Connection'}
        </button>

        <button
          onClick={connectGitHub}
          disabled={connectLoading || (githubStatus?.connected && githubStatus?.status === 'ACTIVE')}
          className="px-4 py-2 bg-green-500 text-white rounded disabled:bg-green-300"
        >
          {connectLoading ? 'Connecting...' : githubStatus?.connected ? 'GitHub Connected' : 'Connect GitHub'}
        </button>

        <button
          onClick={checkGitHubStatus}
          disabled={statusLoading}
          className="px-4 py-2 bg-gray-500 text-white rounded disabled:bg-gray-300"
        >
          {statusLoading ? 'Checking...' : 'Refresh Status'}
        </button>

        <button
          onClick={executeGitHubAction}
          disabled={executeLoading || !githubStatus?.connected || githubStatus?.status !== 'ACTIVE'}
          className="px-4 py-2 bg-purple-500 text-white rounded disabled:bg-purple-300"
        >
          {executeLoading ? 'Executing...' : 'Execute GitHub Action'}
        </button>
      </div>

      {githubStatus && (
        <div className="mb-4 p-4 bg-gray-100 rounded">
          <h2 className="text-lg font-semibold">GitHub Connection Status</h2>
          <p><strong>Connected:</strong> {githubStatus.connected ? 'Yes' : 'No'}</p>
          {githubStatus.connected && (
            <>
              <p><strong>Status:</strong> {githubStatus.status}</p>
              <p><strong>Connection ID:</strong> {githubStatus.connectionId}</p>
            </>
          )}
        </div>
      )}

      {actionResult && (
        <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded">
          <h2 className="text-lg font-semibold text-green-800">GitHub Action Result</h2>
          <div className="mt-2 flex items-center">
            {actionResult.avatarUrl && (
              <Image
                src={actionResult.avatarUrl}
                alt="GitHub Avatar"
                width={64}
                height={64}
                className="rounded-full mr-4"
              />
            )}
            <div>
              <p><strong>Username:</strong> {actionResult.username}</p>
              <p><strong>Name:</strong> {actionResult.name}</p>
              {actionResult.profileUrl && (
                <a
                  href={actionResult.profileUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline"
                >
                  View GitHub Profile
                </a>
              )}
            </div>
          </div>
          <div className="mt-4">
            <details>
              <summary className="cursor-pointer text-blue-500 hover:text-blue-700">
                View Raw Result Data
              </summary>
              <div className="mt-2 p-2 bg-gray-800 text-gray-200 rounded overflow-x-auto text-xs">
                <pre>{JSON.stringify(actionResult.result, null, 2)}</pre>
              </div>
            </details>
          </div>
        </div>
      )}

      {error && (
        <div className="mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {result && (
        <div className="mt-4">
          <h2 className="text-xl font-semibold mb-2">Test Result:</h2>
          <div className="p-4 bg-gray-100 rounded">
            <p><strong>Success:</strong> {result.success ? 'Yes' : 'No'}</p>
            <p><strong>Message:</strong> {result.message}</p>
            {result.toolCount && (
              <>
                <p><strong>Tool Count:</strong> {result.toolCount}</p>
                <p><strong>Available Tools:</strong></p>
                <div className="max-h-60 overflow-y-auto border border-gray-300 rounded p-2 bg-white">
                  {result.toolNames && result.toolNames.length > 0 ? (
                    <ul className="list-disc pl-5">
                      {result.toolNames.map((tool: string, index: number) => (
                        <li key={index} className="text-sm py-1 border-b border-gray-100 last:border-0">
                          {tool}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-gray-500 italic">No tool names available</p>
                  )}
                </div>

                {result.toolsSample && (
                  <div className="mt-4">
                    <details>
                      <summary className="cursor-pointer text-blue-500 hover:text-blue-700">
                        View Raw Tools Data (Debug)
                      </summary>
                      <div className="mt-2 p-2 bg-gray-800 text-gray-200 rounded overflow-x-auto text-xs">
                        <pre>{result.toolsSample}</pre>
                      </div>
                    </details>
                  </div>
                )}
              </>
            )}
            {result.error && (
              <p><strong>Error:</strong> {result.error}</p>
            )}
          </div>
        </div>
      )}

      {/* --- Tool Fetch Playground --- */}
      <div className="my-8 p-4 border rounded bg-gray-50">
        <h2 className="text-lg font-semibold mb-2">Fetch & Run Any Tool</h2>
        
        {/* Tool Fetch Controls */}
        <div className="flex flex-col gap-2 mb-4">
          <div className="flex gap-2">
            <select 
              value={fetchType} 
              onChange={e => setFetchType(e.target.value as any)} 
              className="border rounded px-2 py-1"
            >
              <option value="app">By App</option>
              <option value="action">By Action(s)</option>
              <option value="tag">By Tag</option>
              <option value="usecase">By Use Case (Experimental)</option>
            </select>
            {fetchType !== 'usecase' && (
              <input
                className="border rounded px-2 py-1 flex-1"
                value={fetchInput}
                onChange={e => setFetchInput(e.target.value)}
                placeholder={
                  fetchType === 'app' ? 'e.g. github' :
                  fetchType === 'action' ? 'e.g. GITHUB_GET_THE_AUTHENTICATED_USER' :
                  fetchType === 'tag' ? 'e.g. Issues' :
                  ''
                }
              />
            )}
          </div>

          {renderUseCaseSection()}

          {/* User Context */}
          <div className="flex gap-2 items-center text-sm">
            <label className="w-24">Entity ID:</label>
            <input
              className="border rounded px-2 py-1 flex-1"
              value={entityId}
              onChange={e => setEntityId(e.target.value)}
              placeholder="default"
            />
            <button 
              onClick={fetchTools} 
              disabled={fetchingTools} 
              className="px-3 py-1 bg-blue-600 text-white rounded disabled:bg-blue-300"
            >
              {fetchingTools ? 'Fetching...' : 'Fetch Tools'}
            </button>
          </div>
        </div>

        {/* Tool List */}
        {fetchedTools.length > 0 && (
          <div className="mt-4">
            <h3 className="font-semibold mb-2">
              Fetched Tools ({fetchedTools.length})
            </h3>
            <ul className="max-h-60 overflow-y-auto border rounded bg-white p-2">
              {fetchedTools.map((tool, idx) => {
                const actionName = getActionName(tool);
                return (
                  <li key={idx} className="mb-2 border-b last:border-0 pb-2">
                    <div className="flex items-center gap-2">
                      <span className="font-mono text-xs bg-gray-200 px-2 py-0.5 rounded">
                        {actionName || '[unknown action]'}
                      </span>
                      <button 
                        className="text-blue-600 underline text-xs" 
                        onClick={() => { 
                          if (actionName) {
                            setSelectedTool(tool); 
                            inspectToolSchema({ action: actionName }); 
                          }
                        }}
                        disabled={!actionName}
                      >
                        Inspect Schema
                      </button>
                      <button 
                        className="text-green-700 underline text-xs" 
                        onClick={() => { 
                          setSelectedTool(tool); 
                          setToolParams({}); 
                          setExecResult(null); 
                        }}
                      >
                        Run
                      </button>
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      {getToolDescription(tool)}
                    </div>
                  </li>
                );
              })}
            </ul>
          </div>
        )}

        {/* Schema Display */}
        {toolSchema && (
          <div className="mt-4">
            <h4 className="font-semibold">Tool Schema:</h4>
            <div className="mt-2 bg-gray-800 text-gray-200 rounded p-4 overflow-x-auto">
              <div className="text-xs font-mono whitespace-pre">
                {JSON.stringify(toolSchema, null, 2)}
              </div>
            </div>
          </div>
        )}

        {/* Tool Execution */}
        {selectedTool && (
          <div className="mt-4">
            <h4 className="font-semibold">
              Run Tool: {getActionName(selectedTool)}
            </h4>
            <div className="mb-2 text-xs text-gray-600">Enter parameters:</div>
            <div className="flex flex-col gap-2 mb-2">
              {renderParameterInputs()}
            </div>
            <button 
              onClick={executeSelectedTool} 
              disabled={execLoading} 
              className="px-3 py-1 bg-purple-700 text-white rounded disabled:bg-purple-300"
            >
              {execLoading ? 'Executing...' : 'Execute Tool'}
            </button>

            {/* Execution Result */}
            {execResult && (
              <div className="mt-4">
                <h5 className="font-semibold">
                  Execution {execResult.successful ? 'Success' : 'Failed'}:
                </h5>
                <div className="mt-2 bg-gray-800 text-gray-200 rounded p-4 overflow-x-auto">
                  <div className="text-xs font-mono whitespace-pre">
                    {JSON.stringify(execResult.successful ? execResult.data : execResult.error, null, 2)}
                  </div>
                </div>
                {execResult.raw && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-blue-500 hover:text-blue-700 text-sm">
                      View Raw Response
                    </summary>
                    <div className="mt-2 bg-gray-800 text-gray-200 rounded p-4 overflow-x-auto">
                      <div className="text-xs font-mono whitespace-pre">
                        {JSON.stringify(execResult.raw, null, 2)}
                      </div>
                    </div>
                  </details>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
