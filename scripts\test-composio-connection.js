/**
 * Test script for Composio connections
 * 
 * This script helps you test the connection flow for different providers
 * Run it with: node scripts/test-composio-connection.js [provider]
 * 
 * Example: node scripts/test-composio-connection.js github
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { OpenAIToolSet } = require('composio-core');

// Get the provider from command line arguments
const provider = process.argv[2];
if (!provider) {
  console.error('Please specify a provider (e.g., github, gmail, googlecalendar, notion, jira)');
  process.exit(1);
}

// Normalize provider name
const normalizedProvider = provider.toLowerCase();

// Check if we have an integration ID for this provider
const envVarName = `COMPOSIO_${normalizedProvider.toUpperCase()}_INTEGRATION_ID`;
const integrationId = process.env[envVarName];

if (!integrationId) {
  console.error(`No integration ID found for ${normalizedProvider} in .env.local`);
  console.error(`Please add ${envVarName} to your .env.local file`);
  process.exit(1);
}

console.log(`Testing connection for ${normalizedProvider} with integration ID: ${integrationId}`);

// Initialize the Composio toolset
const toolset = new OpenAIToolSet();

// Test entity ID
const entityId = 'test-user-123';

async function testConnection() {
  try {
    console.log(`Getting entity for ${entityId}...`);
    const entity = await toolset.getEntity(entityId);
    
    console.log(`Initiating connection with integration ID...`);
    try {
      // Try the entity approach first
      const connectionRequest = await entity.initiateConnection({
        integrationId: integrationId,
        redirectUri: 'http://localhost:3000/oauth-callback'
      });
      
      console.log('Connection request successful!');
      console.log('Redirect URL:', connectionRequest.redirectUrl);
      console.log('You can open this URL in your browser to test the OAuth flow');
    } catch (entityError) {
      console.error('Error using entity.initiateConnection:', entityError);
      
      console.log('Trying alternative approach...');
      // Try the direct client approach
      const connectionRequest = await toolset.client.connectedAccounts.initiate({
        integrationId: integrationId,
        entityId: entityId,
        redirectUri: 'http://localhost:3000/oauth-callback'
      });
      
      console.log('Alternative approach successful!');
      console.log('Redirect URL:', connectionRequest.redirectUrl);
      console.log('You can open this URL in your browser to test the OAuth flow');
    }
  } catch (error) {
    console.error('Error testing connection:', error);
  }
}

// Run the test
testConnection();
