import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { db, withRetry } from '@/lib/db/client';
import { toolExperiences } from '@/lib/db/schema';
import { generateEmbedding } from '@/lib/memory/embeddings';
import { sql } from 'drizzle-orm';

/**
 * POST /api/admin/generate-embeddings
 * Generates embeddings for existing tool experiences that don't have them
 */
export async function POST() {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('🚀 Starting tool experience embedding generation...');

    // Get all tool experiences without embeddings
    const experiences = await withRetry(async () => {
      return await db.execute(sql`
        SELECT id, experience_type, app_name, action_name, context_tags, experience_content
        FROM "ToolExperiences"
        WHERE embedding IS NULL OR embedding = '' OR embedding = '""'
      `);
    });

    console.log(`Found ${experiences.length} tool experiences to process`);

    const results = {
      total: experiences.length,
      processed: 0,
      errors: 0,
      details: [] as any[]
    };

    for (let i = 0; i < experiences.length; i++) {
      const experience = experiences[i] as any;
      console.log(`Processing ${i + 1}/${experiences.length}: ${experience.experience_type} for ${experience.app_name}`);

      try {
        // Create embedding text from experience content and context
        const contextTags = experience.context_tags ? experience.context_tags.join(' ') : '';
        const embeddingText = `${experience.experience_type} ${experience.app_name} ${experience.action_name || 'general'} ${contextTags} ${experience.experience_content}`;

        // Generate embedding
        const embedding = await generateEmbedding(embeddingText);
        console.log(`Generated embedding with ${embedding.length} dimensions`);

        // Update the record with the embedding
        await withRetry(async () => {
          return await db.execute(sql`
            UPDATE "ToolExperiences"
            SET embedding = ${JSON.stringify(embedding)}::vector
            WHERE id = ${experience.id}
          `);
        });

        console.log(`✅ Updated experience: ${experience.id}`);
        results.processed++;
        results.details.push({
          id: experience.id,
          type: experience.experience_type,
          app: experience.app_name,
          status: 'success',
          embeddingDimensions: embedding.length
        });

        // Add a small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error(`❌ Failed to process experience ${experience.id}:`, error);
        results.errors++;
        results.details.push({
          id: experience.id,
          type: experience.experience_type,
          app: experience.app_name,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    console.log('🎉 Tool experience embedding generation completed!');

    return NextResponse.json({
      success: true,
      message: `Embedding generation completed. Processed: ${results.processed}, Errors: ${results.errors}`,
      results
    });
  } catch (error) {
    console.error('💥 Error generating tool experience embeddings:', error);
    return NextResponse.json(
      {
        error: 'Failed to generate embeddings',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/generate-embeddings
 * Check status of tool experiences embeddings
 */
export async function GET() {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get counts of experiences with and without embeddings
    const stats = await withRetry(async () => {
      return await db.execute(sql`
        SELECT
          COUNT(*) as total,
          COUNT(CASE WHEN embedding IS NOT NULL AND embedding != '' AND embedding != '""' THEN 1 END) as with_embeddings,
          COUNT(CASE WHEN embedding IS NULL OR embedding = '' OR embedding = '""' THEN 1 END) as without_embeddings
        FROM "ToolExperiences"
        WHERE "isActive" = true
      `);
    });

    const result = stats[0] as any;

    return NextResponse.json({
      success: true,
      stats: {
        total: parseInt(result.total),
        withEmbeddings: parseInt(result.with_embeddings),
        withoutEmbeddings: parseInt(result.without_embeddings),
        completionPercentage: result.total > 0 ? Math.round((result.with_embeddings / result.total) * 100) : 0
      }
    });
  } catch (error) {
    console.error('Error getting embedding stats:', error);
    return NextResponse.json(
      {
        error: 'Failed to get embedding stats',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
