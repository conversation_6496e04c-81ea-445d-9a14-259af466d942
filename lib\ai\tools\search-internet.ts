import { tool } from 'ai';
import { z } from 'zod';
import { tavily } from '@tavily/core';

export const searchInternet = tool({
  description: 'Search the internet for up-to-date information on any topic',
  parameters: z.object({
    query: z.string().describe('The search query to find information on the internet'),
  }),
  execute: async ({ query }) => {
    try {
      // Initialize Tavily client with API key from environment variable
      const client = tavily({ apiKey: process.env.TAVILY_API_KEY });
      
      // Call Tavily search API
      const results = await client.search(query);
      
      return {
        results: results.results,
        query: results.query,
        message: `Found ${results.results.length} results for "${query}"`,
      };
    } catch (error) {
      console.error('Error searching the internet:', error);
      return {
        error: 'Failed to search the internet',
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});
