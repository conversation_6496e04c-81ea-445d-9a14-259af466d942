import { NextResponse } from 'next/server';
import { auth, signOut } from '@/app/(auth)/auth';
import { eq } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { getUser } from '@/lib/db/queries';
import { user } from '@/lib/db/schema';
import { createServerClient } from '@/lib/supabase/server';

// biome-ignore lint: Forbidden non-null assertion.
const client = postgres(process.env.POSTGRES_URL!);
const db = drizzle(client);

export async function GET() {
  try {
    const session = await auth();

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const users = await getUser(session.user.email as string);

    if (users.length === 0) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Return user data without sensitive information
    return NextResponse.json({
      id: users[0].id,
      email: users[0].email,
      firstName: users[0].firstName || '',
      lastName: users[0].lastName || '',
    });
  } catch (error) {
    console.error('Error fetching user data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE() {
  try {
    const session = await auth();

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const users = await getUser(session.user.email as string);

    if (users.length === 0) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Delete user from database
    await db
      .delete(user)
      .where(eq(user.id, session.user.id));

    // Delete user from Supabase Auth
    try {
      const supabase = await createServerClient();
      await supabase.auth.admin.deleteUser(session.user.id);
    } catch (supabaseError) {
      console.error('Error deleting user from Supabase:', supabaseError);
      // Continue anyway since we've deleted from our database
    }

    // Sign out the user
    await signOut({ redirectTo: '/login' });

    return NextResponse.json(
      { success: true, message: 'Account deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
