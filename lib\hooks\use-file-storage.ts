'use client';

import { useState } from 'react';
import { toast } from 'sonner';

export type FileUploadProgress = {
  filename: string;
  progress: number;
  status: 'uploading' | 'success' | 'error';
};

export type UploadedFile = {
  url: string;
  pathname: string;
  contentType: string;
  size?: number;
  bucket?: string;
};

export function useFileStorage() {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<FileUploadProgress[]>([]);

  /**
   * Upload a file to Supabase Storage
   */
  const uploadFile = async (file: File): Promise<UploadedFile | undefined> => {
    const formData = new FormData();
    formData.append('file', file);

    // Add file to upload progress
    setUploadProgress((prev) => [
      ...prev,
      {
        filename: file.name,
        progress: 0,
        status: 'uploading',
      },
    ]);

    setIsUploading(true);

    try {
      const response = await fetch('/api/files/upload', {
        method: 'POST',
        body: formData,
      });

      // Update progress to 100%
      setUploadProgress((prev) =>
        prev.map((item) =>
          item.filename === file.name
            ? { ...item, progress: 100, status: response.ok ? 'success' : 'error' }
            : item
        )
      );

      if (!response.ok) {
        const { error } = await response.json();
        toast.error(error || 'Failed to upload file');
        return undefined;
      }

      const data = await response.json();
      return data as UploadedFile;
    } catch (error) {
      console.error('Error uploading file:', error);
      toast.error('Failed to upload file, please try again');
      
      // Update progress to error
      setUploadProgress((prev) =>
        prev.map((item) =>
          item.filename === file.name
            ? { ...item, status: 'error' }
            : item
        )
      );
      
      return undefined;
    } finally {
      setIsUploading(false);
      
      // Remove progress after a delay
      setTimeout(() => {
        setUploadProgress((prev) => 
          prev.filter((item) => item.filename !== file.name)
        );
      }, 3000);
    }
  };

  /**
   * Upload multiple files to Supabase Storage
   */
  const uploadFiles = async (files: File[]): Promise<UploadedFile[]> => {
    const uploadPromises = files.map((file) => uploadFile(file));
    const results = await Promise.all(uploadPromises);
    return results.filter((result): result is UploadedFile => result !== undefined);
  };

  /**
   * Delete a file from Supabase Storage
   */
  const deleteFile = async (path: string, bucket: string = 'files'): Promise<boolean> => {
    try {
      const response = await fetch(`/api/files/delete?path=${encodeURIComponent(path)}&bucket=${bucket}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const { error } = await response.json();
        toast.error(error || 'Failed to delete file');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error deleting file:', error);
      toast.error('Failed to delete file');
      return false;
    }
  };

  /**
   * List files from Supabase Storage
   */
  const listFiles = async (
    bucket: string = 'files',
    folder: string = '',
    limit: number = 100,
    offset: number = 0
  ) => {
    try {
      const response = await fetch(
        `/api/files/list?bucket=${bucket}&folder=${folder}&limit=${limit}&offset=${offset}`
      );

      if (!response.ok) {
        const { error } = await response.json();
        toast.error(error || 'Failed to list files');
        return [];
      }

      const { files } = await response.json();
      return files;
    } catch (error) {
      console.error('Error listing files:', error);
      toast.error('Failed to list files');
      return [];
    }
  };

  return {
    uploadFile,
    uploadFiles,
    deleteFile,
    listFiles,
    isUploading,
    uploadProgress,
  };
}
