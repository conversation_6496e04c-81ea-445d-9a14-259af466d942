'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';

export default function TestGmailPage() {
  const [connectLoading, setConnectLoading] = useState(false);
  const [statusLoading, setStatusLoading] = useState(false);
  const [executeLoading, setExecuteLoading] = useState(false);
  const [status, setStatus] = useState<any>(null);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  async function connectGmail() {
    setConnectLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/connections/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ provider: 'gmail' }),
      });
      const data = await response.json();

      if (data.success && data.redirectUrl) {
        console.log("Redirecting to:", data.redirectUrl);
        window.open(data.redirectUrl, '_blank', 'width=600,height=700');
      } else {
        setError('Failed to initiate Gmail connection: ' + (data.error || 'Unknown error'));
      }
    } catch (err) {
      setError('Error connecting to Gmail: ' + (err as Error).message);
    } finally {
      setConnectLoading(false);
    }
  }

  async function checkGmailStatus() {
    setStatusLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/connections/status?provider=gmail');
      const data = await response.json();
      setStatus(data);
    } catch (err) {
      setError('Error checking Gmail status: ' + (err as Error).message);
    } finally {
      setStatusLoading(false);
    }
  }

  async function executeGmailAction() {
    setExecuteLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/tools/execute-action', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'GMAIL_GET_PROFILE',
          params: {
            user_id: 'me'
          }
        }),
      });
      const data = await response.json();
      setResult(data);
    } catch (err) {
      setError('Error executing Gmail action: ' + (err as Error).message);
    } finally {
      setExecuteLoading(false);
    }
  }

  return (
    <div className="flex min-h-screen w-full flex-col items-center justify-center py-12 px-4 bg-background">
      <div className="w-full max-w-md space-y-8 bg-card shadow-sm rounded-lg border p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold">Test Gmail Integration</h1>
          <p className="text-sm text-gray-500 mt-2">
            Use this page to test the Gmail integration with Composio
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
            {error}
          </div>
        )}

        <div className="space-y-4">
          <div>
            <Button
              onClick={connectGmail}
              disabled={connectLoading}
              className="w-full"
            >
              {connectLoading ? 'Connecting...' : 'Connect Gmail'}
            </Button>
          </div>

          <div>
            <Button
              onClick={checkGmailStatus}
              disabled={statusLoading}
              variant="outline"
              className="w-full"
            >
              {statusLoading ? 'Checking...' : 'Check Gmail Status'}
            </Button>
          </div>

          {status && (
            <div className="bg-gray-50 border border-gray-200 p-4 rounded-md">
              <h3 className="font-medium mb-2">Connection Status:</h3>
              <pre className="text-xs overflow-auto p-2 bg-gray-100 rounded">
                {JSON.stringify(status, null, 2)}
              </pre>
            </div>
          )}

          <div>
            <Button
              onClick={executeGmailAction}
              disabled={executeLoading || !status?.connected}
              variant="default"
              className="w-full"
            >
              {executeLoading ? 'Executing...' : 'Execute Gmail Action (Get Profile)'}
            </Button>
          </div>

          {result && (
            <div className="bg-gray-50 border border-gray-200 p-4 rounded-md">
              <h3 className="font-medium mb-2">Action Result:</h3>
              <pre className="text-xs overflow-auto p-2 bg-gray-100 rounded">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
