import { NextResponse } from 'next/server';
import { z } from 'zod';
import { compare, genSaltSync, hashSync } from 'bcrypt-ts';
import { eq } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { auth } from '@/app/(auth)/auth';
import { user } from '@/lib/db/schema';
import { getUser } from '@/lib/db/queries';

const profileFormSchema = z.object({
  email: z.string().email().optional(),
  currentPassword: z.string().min(6).optional(),
  newPassword: z.string().min(6).optional(),
  confirmPassword: z.string().min(6).optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
});

// biome-ignore lint: Forbidden non-null assertion.
const client = postgres(process.env.POSTGRES_URL!);
const db = drizzle(client);

export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if the request is JSON or FormData
    const contentType = request.headers.get('content-type') || '';
    let validatedData;
    let formData;

    if (contentType.includes('application/json')) {
      const jsonData = await request.json();
      validatedData = profileFormSchema.parse(jsonData);
    } else {
      formData = await request.formData();
    }

    try {
      if (!validatedData && formData) {
        validatedData = profileFormSchema.parse({
          email: formData.get('email'),
          currentPassword: formData.get('currentPassword') || undefined,
          newPassword: formData.get('newPassword') || undefined,
          confirmPassword: formData.get('confirmPassword') || undefined,
          firstName: formData.get('firstName') || undefined,
          lastName: formData.get('lastName') || undefined,
        });
      }

      // Update firstName and lastName if provided
      if (validatedData.firstName !== undefined || validatedData.lastName !== undefined) {
        const updateData: Record<string, any> = {};

        if (validatedData.firstName !== undefined) {
          updateData.firstName = validatedData.firstName;
        }

        if (validatedData.lastName !== undefined) {
          updateData.lastName = validatedData.lastName;
        }

        if (Object.keys(updateData).length > 0) {
          await db
            .update(user)
            .set(updateData)
            .where(eq(user.id, session.user.id));
        }
      }

      // Check if user wants to update password
      if (validatedData.newPassword) {
        // Ensure current password is provided
        if (!validatedData.currentPassword) {
          return NextResponse.json(
            { error: 'invalid_data', message: 'Current password is required' },
            { status: 400 }
          );
        }

        // Ensure new password and confirm password match
        if (validatedData.newPassword !== validatedData.confirmPassword) {
          return NextResponse.json(
            { error: 'password_mismatch', message: 'New passwords do not match' },
            { status: 400 }
          );
        }

        // Verify current password
        const users = await getUser(validatedData.email);
        if (users.length === 0) {
          return NextResponse.json(
            { error: 'user_not_found', message: 'User not found' },
            { status: 404 }
          );
        }

        // biome-ignore lint: Forbidden non-null assertion.
        const passwordsMatch = await compare(validatedData.currentPassword, users[0].password!);
        if (!passwordsMatch) {
          return NextResponse.json(
            { error: 'invalid_credentials', message: 'Current password is incorrect' },
            { status: 400 }
          );
        }

        // Update password
        const salt = genSaltSync(10);
        const hash = hashSync(validatedData.newPassword, salt);

        await db
          .update(user)
          .set({ password: hash })
          .where(eq(user.id, session.user.id));
      }

      return NextResponse.json(
        { success: true, message: 'Profile updated successfully' },
        { status: 200 }
      );
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'invalid_data', message: 'Invalid form data' },
          { status: 400 }
        );
      }
      throw error;
    }
  } catch (error) {
    console.error('Error updating profile:', error);
    return NextResponse.json(
      { error: 'server_error', message: 'Internal server error' },
      { status: 500 }
    );
  }
}
