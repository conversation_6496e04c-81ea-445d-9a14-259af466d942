import { describe, it, expect } from 'vitest';
import { getCoreSystemPrompt } from '../lib/ai/core-prompt';

describe('Core System Prompt', () => {
  it('should include core identity and principles', () => {
    const prompt = getCoreSystemPrompt({
      selectedChatModel: 'chat-model-reasoning',
    });

    expect(prompt).toContain('exceptional executive assistant');
    expect(prompt).toContain('OUTCOME-ORIENTED EXECUTION');
    expect(prompt).toContain('AUTONOMOUS CONFIDENCE');
    expect(prompt).toContain('EXECUTIVE ASSISTANT MINDSET');
  });

  it('should include mental model framework', () => {
    const prompt = getCoreSystemPrompt({
      selectedChatModel: 'chat-model-reasoning',
    });

    expect(prompt).toContain('MENTAL MODEL FRAMEWORK');
    expect(prompt).toContain('TASK DECOMPOSITION');
    expect(prompt).toContain('HIERARCHICAL THINKING');
    expect(prompt).toContain('PARAMETER INFERENCE');
  });

  it('should include artifacts prompt for non-reasoning model', () => {
    const prompt = getCoreSystemPrompt({
      selectedChatModel: 'chat-model',
    });

    expect(prompt).toContain('Artifacts is a special user interface mode');
    expect(prompt).toContain('createDocument tool');
  });

  it('should not include artifacts prompt for reasoning model', () => {
    const prompt = getCoreSystemPrompt({
      selectedChatModel: 'chat-model-reasoning',
    });

    expect(prompt).not.toContain('Artifacts is a special user interface mode');
  });

  it('should include conversation awareness when context is provided', () => {
    const conversationContext = {
      recentMessages: ['user: Hello', 'assistant: Hi there!'],
      toolsUsed: ['searchInternet', 'fetchActions'],
      userPatterns: ['Polite communication style'],
      conversationFlow: 'Early conversation - building understanding',
      taskProgression: 'Information gathering and planning phase',
      personalContext: 'User prefers detailed explanations'
    };

    const prompt = getCoreSystemPrompt({
      selectedChatModel: 'chat-model-reasoning',
      conversationContext,
    });

    expect(prompt).toContain('CONVERSATION AWARENESS');
    expect(prompt).toContain('RECENT CONVERSATION FLOW');
    expect(prompt).toContain('TOOLS USED IN THIS CONVERSATION');
    expect(prompt).toContain('USER PATTERNS OBSERVED');
    expect(prompt).toContain('CONVERSATION INTELLIGENCE DIRECTIVES');
  });

  it('should not include conversation awareness when no context is provided', () => {
    const prompt = getCoreSystemPrompt({
      selectedChatModel: 'chat-model-reasoning',
    });

    expect(prompt).not.toContain('CONVERSATION AWARENESS');
  });
});
