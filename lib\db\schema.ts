import type { InferSelectModel } from 'drizzle-orm';
import {
  pgTable,
  varchar,
  timestamp,
  json,
  uuid,
  text,
  primaryKey,
  foreignKey,
  boolean,
  real,
  integer,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';

export const user = pgTable('User', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  email: varchar('email', { length: 64 }).notNull(),
  password: varchar('password', { length: 64 }),
  firstName: varchar('firstName', { length: 64 }),
  lastName: varchar('lastName', { length: 64 }),
  confirmed: boolean('confirmed').notNull().default(false),
});

export type User = InferSelectModel<typeof user>;

export const chat = pgTable('Chat', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  createdAt: timestamp('createdAt').notNull(),
  title: text('title').notNull(),
  userId: uuid('userId')
    .notNull()
    .references(() => user.id),
  visibility: varchar('visibility', { enum: ['public', 'private'] })
    .notNull()
    .default('private'),
});

export type Chat = InferSelectModel<typeof chat>;

// DEPRECATED: The following schema is deprecated and will be removed in the future.
// Read the migration guide at https://github.com/vercel/ai-chatbot/blob/main/docs/04-migrate-to-parts.md
export const messageDeprecated = pgTable('Message', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  chatId: uuid('chatId')
    .notNull()
    .references(() => chat.id),
  role: varchar('role').notNull(),
  content: json('content').notNull(),
  createdAt: timestamp('createdAt').notNull(),
});

export type MessageDeprecated = InferSelectModel<typeof messageDeprecated>;

export const message = pgTable('Message_v2', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  chatId: uuid('chatId')
    .notNull()
    .references(() => chat.id),
  role: varchar('role').notNull(),
  parts: json('parts').notNull(),
  attachments: json('attachments').notNull(),
  createdAt: timestamp('createdAt').notNull(),
});

export type DBMessage = InferSelectModel<typeof message>;

// DEPRECATED: The following schema is deprecated and will be removed in the future.
// Read the migration guide at https://github.com/vercel/ai-chatbot/blob/main/docs/04-migrate-to-parts.md
export const voteDeprecated = pgTable(
  'Vote',
  {
    chatId: uuid('chatId')
      .notNull()
      .references(() => chat.id),
    messageId: uuid('messageId')
      .notNull()
      .references(() => messageDeprecated.id),
    isUpvoted: boolean('isUpvoted').notNull(),
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.chatId, table.messageId] }),
    };
  },
);

export type VoteDeprecated = InferSelectModel<typeof voteDeprecated>;

export const vote = pgTable(
  'Vote_v2',
  {
    chatId: uuid('chatId')
      .notNull()
      .references(() => chat.id),
    messageId: uuid('messageId')
      .notNull()
      .references(() => message.id),
    isUpvoted: boolean('isUpvoted').notNull(),
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.chatId, table.messageId] }),
    };
  },
);

export type Vote = InferSelectModel<typeof vote>;

export const document = pgTable(
  'Document',
  {
    id: uuid('id').notNull().defaultRandom(),
    createdAt: timestamp('createdAt').notNull(),
    title: text('title').notNull(),
    content: text('content'),
    kind: varchar('text', { enum: ['text', 'code', 'image', 'sheet'] })
      .notNull()
      .default('text'),
    userId: uuid('userId')
      .notNull()
      .references(() => user.id),
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.id, table.createdAt] }),
    };
  },
);

export type Document = InferSelectModel<typeof document>;

export const suggestion = pgTable(
  'Suggestion',
  {
    id: uuid('id').notNull().defaultRandom(),
    documentId: uuid('documentId').notNull(),
    documentCreatedAt: timestamp('documentCreatedAt').notNull(),
    originalText: text('originalText').notNull(),
    suggestedText: text('suggestedText').notNull(),
    description: text('description'),
    isResolved: boolean('isResolved').notNull().default(false),
    userId: uuid('userId')
      .notNull()
      .references(() => user.id),
    createdAt: timestamp('createdAt').notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.id] }),
    documentRef: foreignKey({
      columns: [table.documentId, table.documentCreatedAt],
      foreignColumns: [document.id, document.createdAt],
    }),
  }),
);

export type Suggestion = InferSelectModel<typeof suggestion>;

export const connection = pgTable('Connection', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  userId: uuid('userId').notNull().references(() => user.id),
  provider: varchar('provider', { length: 64 }).notNull(), // e.g., 'github', 'gmail'
  connectionId: varchar('connectionId', { length: 255 }).notNull(),
  status: varchar('status', { enum: ['ACTIVE', 'EXPIRED', 'REVOKED'] })
    .notNull()
    .default('ACTIVE'),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  updatedAt: timestamp('updatedAt').notNull().defaultNow(),
  metadata: json('metadata') // Optional extra info as JSON
});

export type Connection = InferSelectModel<typeof connection>;

export const passwordReset = pgTable('PasswordReset', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  email: varchar('email', { length: 64 }).notNull(),
  token: varchar('token', { length: 255 }).notNull(),
  expires: timestamp('expires').notNull(),
  createdAt: timestamp('createdAt').notNull(),
  used: boolean('used').notNull().default(false),
});

export type PasswordReset = InferSelectModel<typeof passwordReset>;

// Memory table for storing user context as vector embeddings
export const memory = pgTable('Memory', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  userId: uuid('userId')
    .notNull()
    .references(() => user.id),
  content: text('content').notNull(),
  // Keep vector type for proper embedding storage
  embedding: text('embedding').notNull(), // This should be vector(768) but drizzle doesn't support it directly
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  isActive: boolean('isActive').notNull().default(true),
  category: text('category'),
});

export type Memory = InferSelectModel<typeof memory>;

// Tool Experiences table for storing AI agent learning patterns and insights
export const toolExperiences = pgTable('ToolExperiences', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  experienceType: text('experience_type').notNull(), // 'pattern', 'insight', 'advice', 'workflow', 'parameter_combo'
  appName: text('app_name').notNull(), // 'github', 'notion', 'gmail', 'multi'
  actionName: text('action_name'), // specific action or 'general'
  contextTags: text('context_tags').array(), // ['repo_management', 'documentation', 'search']
  experienceContent: text('experience_content').notNull(), // the actual wisdom
  // Add vector embedding for semantic similarity (stored as text, converted to vector in queries)
  embedding: text('embedding').notNull(),
  successRate: real('success_rate').default(1.0), // how often this works (0.0 to 1.0)
  usageCount: integer('usage_count').default(0), // how many times applied
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  lastUsed: timestamp('lastUsed'),
  priority: text('priority').notNull().default('medium'), // 'critical', 'high', 'medium', 'low'
  isActive: boolean('isActive').notNull().default(true),
});

export type ToolExperience = InferSelectModel<typeof toolExperiences>;
