'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

interface Connection {
  id: string;
  provider: string;
  connectionId: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  metadata?: any;
}

export default function ConnectionsPage() {
  const router = useRouter();
  const [connections, setConnections] = useState<Connection[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [connectingProvider, setConnectingProvider] = useState<string | null>(null);
  const [disconnectingProvider, setDisconnectingProvider] = useState<string | null>(null);
  const [checkingStatus, setCheckingStatus] = useState<Record<string, boolean>>({});

  // Available providers
  const availableProviders = [
    { id: 'github', name: 'GitHub', description: 'Connect to GitHub to access repositories, issues, and more.' },
    { id: 'gmail', name: 'Gmail', description: 'Connect to Gmail to access emails and send messages.' },
    { id: 'googlecalendar', name: 'Google Calendar', description: 'Connect to Google Calendar to manage events and schedules.' },
    { id: 'notion', name: 'Notion', description: 'Connect to Notion to access and manage your workspaces.' },
    { id: 'jira', name: 'Jira', description: 'Connect to Jira to manage issues and projects.' }
  ];

  // Fetch user connections
  useEffect(() => {
    fetchConnections();
  }, []);

  const fetchConnections = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/connections');
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.connections) {
          setConnections(data.connections);
        }
      } else {
        toast.error('Failed to fetch connections');
      }
    } catch (error) {
      console.error('Error fetching connections:', error);
      toast.error('An error occurred while fetching connections');
    } finally {
      setIsLoading(false);
    }
  };

  // Connect to a provider
  const connectProvider = async (provider: string) => {
    setConnectingProvider(provider);
    try {
      const response = await fetch('/api/connections/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ provider }),
      });

      const data = await response.json();

      if (data.success && data.redirectUrl) {
        // Set up message listener for OAuth completion or errors
        const messageHandler = (event: MessageEvent) => {
          if (event.origin === window.location.origin) {
            // Handle OAuth completion
            if (event.data?.type === 'OAUTH_COMPLETE') {
              // Remove the event listener once we've received the message
              window.removeEventListener('message', messageHandler);

              // Start checking the connection status
              checkConnectionStatus(provider);
            }

            // Handle OAuth errors
            else if (event.data?.type === 'OAUTH_ERROR') {
              // Remove the event listener
              window.removeEventListener('message', messageHandler);

              // Show error message
              const appName = event.data?.appName || provider;
              const errorMsg = event.data?.error || `Failed to connect to ${appName}`;
              toast.error(errorMsg);

              // Reset connecting state
              setConnectingProvider(null);
            }
          }
        };

        // Add the message listener
        window.addEventListener('message', messageHandler);

        // Open the redirect URL in a new window
        window.open(data.redirectUrl, '_blank', 'width=600,height=700');
      } else if (data.comingSoon) {
        // Handle "coming soon" message
        toast.info(`${provider} integration coming soon`);
        setConnectingProvider(null);
      } else {
        // Handle other errors
        toast.error(`Couldn't connect to ${provider}. Please try again.`);
        setConnectingProvider(null);
      }
    } catch (error) {
      console.error(`Error connecting to ${provider}:`, error);
      toast.error(`Couldn't connect to ${provider}. Please try again.`);
      setConnectingProvider(null);
    }
  };

  // Check connection status
  const checkConnectionStatus = async (provider: string, attempts = 0, maxAttempts = 15) => {
    setCheckingStatus(prev => ({ ...prev, [provider]: true }));

    // If we've exceeded the maximum number of attempts (30 seconds total)
    if (attempts >= maxAttempts) {
      toast.error(`Couldn't connect to ${provider}. Please try again.`);
      setCheckingStatus(prev => ({ ...prev, [provider]: false }));
      setConnectingProvider(null);
      return;
    }

    try {
      const response = await fetch(`/api/connections/status?provider=${provider}`);
      const data = await response.json();

      if (data.success) {
        if (data.connected && data.status === 'ACTIVE') {
          toast.success(`${provider} connected`);
          // Refresh connections list
          fetchConnections();
          setCheckingStatus(prev => ({ ...prev, [provider]: false }));
          setConnectingProvider(null);
        } else {
          // If not connected yet or not active, check again in 2 seconds
          setTimeout(() => checkConnectionStatus(provider, attempts + 1, maxAttempts), 2000);
        }
      } else {
        toast.error(`Couldn't connect to ${provider}. Please try again.`);
        setCheckingStatus(prev => ({ ...prev, [provider]: false }));
        setConnectingProvider(null);
      }
    } catch (error) {
      console.error(`Error checking ${provider} connection status:`, error);
      toast.error(`Couldn't connect to ${provider}. Please try again.`);
      setCheckingStatus(prev => ({ ...prev, [provider]: false }));
      setConnectingProvider(null);
    }
  };

  // Disconnect a service
  const disconnectService = async (connectionId: string, provider: string) => {
    // Set the disconnecting state for this provider
    setDisconnectingProvider(provider);

    try {
      const response = await fetch('/api/connections', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ connectionId }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success(`${provider} disconnected`);
        // Refresh connections list
        fetchConnections();
      } else {
        toast.error(`Couldn't disconnect ${provider}. Please try again.`);
      }
    } catch (error) {
      console.error(`Error disconnecting ${provider}:`, error);
      toast.error(`Couldn't disconnect ${provider}. Please try again.`);
    } finally {
      // Clear the disconnecting state
      setDisconnectingProvider(null);
    }
  };

  // Get connection status for a provider
  const getConnectionForProvider = (provider: string) => {
    return connections.find(conn => conn.provider.toLowerCase() === provider.toLowerCase());
  };

  return (
    <div className="flex min-h-screen w-full items-center justify-center py-8 px-4 bg-background">
      <div className="w-full max-w-2xl bg-card shadow-sm rounded-lg border flex flex-col gap-6 p-6">
        <div className="flex flex-col items-center justify-center gap-2 text-center">
          <h3 className="text-xl font-semibold dark:text-zinc-50">Connect Your Services</h3>
          <p className="text-sm text-gray-500 dark:text-zinc-400">
            Connect your accounts to enable AI tools and automation
          </p>
        </div>

        <div className="mt-6">
            <div className="space-y-4">
              {isLoading ? (
                <div className="flex justify-center items-center py-8">
                  <div className="size-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                </div>
              ) : (
                availableProviders.map((provider) => {
                  const connection = getConnectionForProvider(provider.id);
                  const isConnected = !!connection && connection.status === 'ACTIVE';
                  const isChecking = checkingStatus[provider.id];

                  return (
                    <div key={provider.id} className="flex flex-col sm:flex-row sm:items-center justify-between p-4 border rounded-md gap-4">
                      <div>
                        <h4 className="font-medium">{provider.name}</h4>
                        <p className="text-sm text-gray-500">{provider.description}</p>
                        {isConnected && (
                          <p className="text-xs text-green-500 mt-1">
                            Connected
                          </p>
                        )}
                      </div>
                      <Button
                        variant={isConnected ? "outline" : "default"}
                        size="sm"
                        disabled={connectingProvider === provider.id || disconnectingProvider === provider.id || isChecking}
                        onClick={() => isConnected
                          ? disconnectService(connection.id, provider.id)
                          : connectProvider(provider.id)
                        }
                        className="whitespace-nowrap"
                      >
                        {connectingProvider === provider.id || isChecking ? (
                          <>
                            <div className="size-4 animate-spin rounded-full border-2 border-background border-t-transparent mr-2" />
                            {isChecking ? 'Checking...' : 'Connecting...'}
                          </>
                        ) : disconnectingProvider === provider.id ? (
                          <>
                            <div className="size-4 animate-spin rounded-full border-2 border-background border-t-transparent mr-2" />
                            Disconnecting...
                          </>
                        ) : isConnected ? (
                          'Disconnect'
                        ) : (
                          `Connect ${provider.name}`
                        )}
                      </Button>
                    </div>
                  );
                })
              )}
            </div>
        </div>

        <div className="flex justify-center mt-4">
          <Button
            variant="ghost"
            onClick={() => router.push('/profile')}
            className="text-sm text-gray-600 dark:text-zinc-400"
          >
            Back to Profile
          </Button>
        </div>
      </div>
    </div>
  );
}