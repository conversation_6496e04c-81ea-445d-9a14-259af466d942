import { OpenAIToolSet } from "composio-core";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    console.log("Checking GitHub connection status...");

    // Initialize the Composio toolset using OpenAIToolSet
    const toolset = new OpenAIToolSet();
    console.log("Toolset initialized");

    // For simplicity, we're using a default entity ID
    // In a real app, you'd use the authenticated user's ID
    const userIdentifier = "default_user";

    // Get the entity for the user
    console.log("Getting entity for user:", userIdentifier);
    const entity = await toolset.getEntity(userIdentifier);
    console.log("Entity retrieved");

    try {
      // Check if the entity has a GitHub connection
      console.log("Checking GitHub connection");
      const connections = await entity.getConnections();
      console.log("Connections retrieved:", connections);

      // Find GitHub connection
      const githubConnection = connections.find(conn => conn.appName === "github");

      if (githubConnection) {
        return NextResponse.json({
          success: true,
          connected: true,
          status: githubConnection.status,
          connectionId: githubConnection.id,
        });
      } else {
        return NextResponse.json({
          success: true,
          connected: false,
          status: "NOT_CONNECTED",
          connectionId: null,
        });
      }
    } catch (connError) {
      console.log("Error getting connections, assuming not connected:", connError);
      return NextResponse.json({
        success: true,
        connected: false,
        status: "ERROR",
        connectionId: null,
        errorDetails: connError instanceof Error ? connError.message : String(connError)
      });
    }
  } catch (error) {
    console.error("Error checking GitHub connection status:", error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}
