import 'server-only';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

// Connection pool configuration
const CONNECTION_POOL_CONFIG = {
  max: 10,                 // Maximum number of connections in the pool
  idle_timeout: 20,        // Close idle connections after 20 seconds
  connect_timeout: 10,     // Connection timeout in seconds
  max_lifetime: 60 * 30,   // Maximum connection lifetime in seconds (30 minutes)
  max_acquire_time: 30,    // Maximum time to acquire a connection (30 seconds)
};

// Retry configuration
const RETRY_CONFIG = {
  maxRetries: 3,           // Maximum number of retries
  initialDelay: 100,       // Initial delay in ms
  maxDelay: 3000,          // Maximum delay in ms
};

/**
 * Creates a database client with connection pooling
 * @returns A postgres client with connection pooling
 */
export function createDbClient() {
  if (!process.env.POSTGRES_URL) {
    throw new Error('POSTGRES_URL environment variable is not defined');
  }

  return postgres(process.env.POSTGRES_URL, CONNECTION_POOL_CONFIG);
}

/**
 * Creates a Drizzle ORM instance with the database client
 * @returns A Drizzle ORM instance
 */
export function createDbOrm() {
  const client = createDbClient();
  return drizzle(client);
}

// Create a singleton instance of the database client
const dbClient = createDbClient();
const db = drizzle(dbClient);

/**
 * Executes a database operation with retry logic
 * @param operation The database operation to execute
 * @param options Optional retry configuration
 * @returns The result of the database operation
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  options: {
    maxRetries?: number;
    initialDelay?: number;
    maxDelay?: number;
    onRetry?: (error: Error, attempt: number) => void;
  } = {}
): Promise<T> {
  const maxRetries = options.maxRetries ?? RETRY_CONFIG.maxRetries;
  const initialDelay = options.initialDelay ?? RETRY_CONFIG.initialDelay;
  const maxDelay = options.maxDelay ?? RETRY_CONFIG.maxDelay;
  const onRetry = options.onRetry;

  let lastError: Error | null = null;
  let attempt = 0;

  while (attempt <= maxRetries) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      attempt++;

      // If this was the last attempt, throw the error
      if (attempt > maxRetries) {
        throw lastError;
      }

      // Calculate exponential backoff delay (with jitter)
      const delay = Math.min(
        initialDelay * Math.pow(2, attempt - 1) * (0.5 + Math.random() * 0.5),
        maxDelay
      );

      // Log retry attempt
      if (onRetry) {
        onRetry(lastError, attempt);
      } else {
        console.warn(
          `Database operation failed (attempt ${attempt}/${maxRetries}), retrying in ${Math.round(
            delay
          )}ms:`,
          lastError.message
        );
      }

      // Wait before retrying
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  // This should never happen due to the throw in the loop
  throw lastError;
}

export { db };
