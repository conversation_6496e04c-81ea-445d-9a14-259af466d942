/**
 * Test script for generating embeddings
 * Run with: npx tsx lib/memory/test-embedding.ts
 */
import { config } from 'dotenv';
import { generateEmbedding } from './embeddings';

// Load environment variables
config({
  path: '.env.local',
});

async function testEmbedding() {
  try {
    console.log('🧠 Testing Embedding Generation...');

    // Test generating an embedding
    const testText = 'The user prefers dark mode in all applications';
    console.log(`Generating embedding for: "${testText}"`);
    
    const embedding = await generateEmbedding(testText);
    
    console.log(`✅ Successfully generated embedding with ${embedding.length} dimensions`);
    console.log(`First 5 values: ${embedding.slice(0, 5).join(', ')}`);
    
    // Test similarity between related concepts
    const text1 = 'The user likes dark mode';
    const text2 = 'The user prefers dark themes in applications';
    const text3 = 'The weather is sunny today';
    
    console.log('\nTesting semantic similarity between:');
    console.log(`1. "${text1}"`);
    console.log(`2. "${text2}"`);
    console.log(`3. "${text3}"`);
    
    const embedding1 = await generateEmbedding(text1);
    const embedding2 = await generateEmbedding(text2);
    const embedding3 = await generateEmbedding(text3);
    
    // Calculate cosine similarity
    const similarity12 = cosineSimilarity(embedding1, embedding2);
    const similarity13 = cosineSimilarity(embedding1, embedding3);
    const similarity23 = cosineSimilarity(embedding2, embedding3);
    
    console.log('\nSimilarity scores:');
    console.log(`Between 1 and 2: ${similarity12.toFixed(4)} (should be high)`);
    console.log(`Between 1 and 3: ${similarity13.toFixed(4)} (should be low)`);
    console.log(`Between 2 and 3: ${similarity23.toFixed(4)} (should be low)`);
    
    console.log('\n✅ Embedding tests completed successfully!');
  } catch (error) {
    console.error('❌ Error testing embeddings:', error);
  }
}

// Helper function to calculate cosine similarity
function cosineSimilarity(a: number[], b: number[]): number {
  if (a.length !== b.length) {
    throw new Error('Vectors must have the same length');
  }

  let dotProduct = 0;
  let normA = 0;
  let normB = 0;

  for (let i = 0; i < a.length; i++) {
    dotProduct += a[i] * b[i];
    normA += a[i] * a[i];
    normB += b[i] * b[i];
  }

  normA = Math.sqrt(normA);
  normB = Math.sqrt(normB);

  if (normA === 0 || normB === 0) {
    return 0;
  }

  return dotProduct / (normA * normB);
}

// Run the test
testEmbedding();
