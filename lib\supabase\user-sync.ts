'use server';

import { createServerClient } from './server';
import { createUser, getUser, updateUserConfirmedStatus } from '@/lib/db/queries';
import { confirmUserDirectly } from '@/lib/db/confirm-user';
import { genSaltSync, hashSync } from 'bcrypt-ts';

/**
 * Syncs a Supabase Auth user with our application's database
 * This ensures that when a user signs up with Supabase Auth,
 * they also exist in our application's database
 */
export async function syncUserWithDatabase(email: string, password?: string) {
  try {
    // Check if user exists in our database
    const users = await getUser(email);

    if (users.length === 0) {
      // User doesn't exist in our database, create them
      if (password) {
        // If password is provided, use it
        await createUser(email, password);
      } else {
        // If no password is provided (e.g., OAuth login), create with a secure random password
        // This is just a placeholder since auth is handled by Supabase
        const salt = genSaltSync(10);
        const secureRandomPassword = hashSync(Math.random().toString(36).slice(2) + Date.now().toString(), salt);
        await createUser(email, secureRandomPassword);
      }
      return { success: true, message: 'User synced with database' };
    }

    return { success: true, message: 'User already exists in database' };
  } catch (error) {
    console.error('Error syncing user with database:', error);
    return { success: false, message: 'Failed to sync user with database' };
  }
}

/**
 * Marks a user as confirmed in our application's database
 * This is called when a user verifies their email through Supabase Auth
 */
export async function markUserAsConfirmed(email: string) {
  try {
    console.log('Marking user as confirmed:', email);
    // Check if user exists in our database
    const users = await getUser(email);

    if (users.length === 0) {
      console.log('User not found in database, syncing first');
      // User doesn't exist in our database, sync them first
      await syncUserWithDatabase(email);
    } else {
      console.log('User found in database:', users[0]);
    }

    // First try the direct SQL approach which we know works
    console.log('Using direct SQL approach to update confirmed status');
    const directResult = await confirmUserDirectly(email);

    if (directResult) {
      console.log('Direct SQL update successful');
      return { success: true, message: 'User marked as confirmed' };
    }

    // If direct approach fails, try the ORM approach as a fallback
    console.log('Direct approach failed, trying ORM approach');
    const result = await updateUserConfirmedStatus({ email, confirmed: true });
    console.log('ORM update result:', result);

    return { success: true, message: 'User marked as confirmed' };
  } catch (error) {
    console.error('Error marking user as confirmed:', error);
    return { success: false, message: 'Failed to mark user as confirmed' };
  }
}
