# Use Case Standalone API (Find Actions)

## Announcing the New Composio Find Actions API

We're excited to introduce the new **Composio Find Actions API**, a standalone service designed to help you translate natural language descriptions of tasks into specific, actionable steps across various applications integrated with Composio.

**What it does:** This API takes a description of what you want to achieve (e.g., "Fetch linear tickets and create a new task in Notion") and returns a structured sequence of API actions required to accomplish that task, identifying the relevant applications and the specific actions within them.

**Why the change?** Previously, this functionality was available through a function within the Composio SDK, often accessed via a method similar to `composio.aucSearch()` (Advanced Use Case Search), which called an internal service endpoint (like `/search/advanced`). To provide a more robust, flexible, language-agnostic, and independently accessible solution, we have extracted this capability into its own dedicated API service.

**This means the previous SDK function (`composio.aucSearch()` or similar) used for this purpose is being deprecated and will be removed in future SDK versions.** All users should migrate to using this new standalone API endpoint.

---

### Service URL

The Find Actions API is accessible at: `https://find-actions.composio.dev/`

---

### API Endpoints

### 1. Search for Actions (`/api/search`)

This is the primary endpoint for translating a use case into actions.