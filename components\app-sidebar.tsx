'use client';

import type { User } from 'next-auth';
import { useRouter } from 'next/navigation';
import { useTheme } from 'next-themes';

import { NewChatIcon } from '@/components/icons';
import { SidebarHistory } from '@/components/sidebar-history';
import { SidebarMiddleToggle } from '@/components/sidebar-middle-toggle';
import { SidebarUserNav } from '@/components/sidebar-user-nav';
import { Button } from '@/components/ui/button';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  useSidebar,
} from '@/components/ui/sidebar';
import Link from 'next/link';
import Image from 'next/image';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { cn } from '@/lib/utils';

export function AppSidebar({ user }: { user: User | undefined }) {
  const router = useRouter();
  const { setOpenMobile } = useSidebar();
  const { theme } = useTheme();

  return (
    <>
      <Sidebar className="group-data-[side=left]:border-r-0 bg-[#eef2ff] border-r border-indigo-100 shadow-sm dark:bg-[#0f0f19] dark:border-r dark:border-gray-800/30">
        <SidebarHeader>
          <SidebarMenu>
            <div className="flex flex-row justify-between items-center">
              <div className="flex-1 min-w-0">
                <Link
                  href="/"
                  onClick={() => {
                    setOpenMobile(false);
                  }}
                  className="flex flex-row gap-0 items-center py-1"
                >
                  <div
                    className={cn(
                      "px-0 rounded-md cursor-pointer",
                      "hover:bg-muted transition-all duration-200",
                      "whitespace-nowrap overflow-hidden",
                      "group-data-[collapsible=icon]:text-center group-data-[collapsible=icon]:w-full",
                      "group-data-[collapsible=icon]:px-0",
                      "group-data-[collapsible=icon]:rotate-0 group-data-[collapsible=icon]:opacity-100",
                      "group-data-[collapsible=icon]:translate-x-0",
                      "group-data-[collapsible=icon]:block",
                      "group-data-[collapsible=offcanvas]:block",
                      "relative h-8 w-52"
                    )}
                  >
                    <Image
                      src="/Logo.png"
                      alt="Logo"
                      fill
                      className="object-contain object-left"
                      priority
                    />
                  </div>
                </Link>
              </div>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    type="button"
                    className="p-2 h-fit border-0"
                    onClick={() => {
                      setOpenMobile(false);
                      router.push('/');
                      router.refresh();
                    }}
                  >
                    <NewChatIcon size={24} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent align="end">New Chat</TooltipContent>
              </Tooltip>
            </div>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent>
          <SidebarHistory user={user} />
        </SidebarContent>
        <SidebarFooter>{user && <SidebarUserNav user={user} />}</SidebarFooter>
      </Sidebar>
      <SidebarMiddleToggle />
    </>
  );
}
