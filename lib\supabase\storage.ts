'use server';

import { createServerClient } from './server';
import { supabase } from './client';

/**
 * Uploads a file to Supabase Storage
 * @param file The file to upload as a Buffer or ArrayBuffer
 * @param filename The name of the file
 * @param contentType The content type of the file
 * @param bucketName The bucket to upload to (defaults to 'files')
 * @returns Object containing the URL and other metadata of the uploaded file
 */
export async function uploadFileToSupabase({
  file,
  filename,
  contentType,
  bucketName = 'files',
}: {
  file: ArrayBuffer;
  filename: string;
  contentType: string;
  bucketName?: string;
}) {
  try {
    // Create a unique filename to prevent collisions
    const uniqueFilename = `${Date.now()}-${filename}`;
    
    // Upload the file to Supabase Storage
    const { data, error } = await supabase.storage
      .from(bucketName)
      .upload(uniqueFilename, file, {
        contentType,
        upsert: false,
      });

    if (error) {
      console.error('Error uploading file to Supabase:', error);
      throw new Error(`Failed to upload file: ${error.message}`);
    }

    // Get the public URL for the uploaded file
    const { data: publicUrlData } = supabase.storage
      .from(bucketName)
      .getPublicUrl(uniqueFilename);

    return {
      url: publicUrlData.publicUrl,
      pathname: uniqueFilename,
      contentType,
      size: file.byteLength,
      bucket: bucketName,
    };
  } catch (error) {
    console.error('Error in uploadFileToSupabase:', error);
    throw error;
  }
}

/**
 * Uploads a file to Supabase Storage from the server
 * @param file The file to upload as a Buffer or ArrayBuffer
 * @param filename The name of the file
 * @param contentType The content type of the file
 * @param bucketName The bucket to upload to (defaults to 'files')
 * @returns Object containing the URL and other metadata of the uploaded file
 */
export async function serverUploadFileToSupabase({
  file,
  filename,
  contentType,
  bucketName = 'files',
}: {
  file: ArrayBuffer;
  filename: string;
  contentType: string;
  bucketName?: string;
}) {
  try {
    // Create a server-side Supabase client
    const supabaseServer = await createServerClient();
    
    // Create a unique filename to prevent collisions
    const uniqueFilename = `${Date.now()}-${filename}`;
    
    // Upload the file to Supabase Storage
    const { data, error } = await supabaseServer.storage
      .from(bucketName)
      .upload(uniqueFilename, file, {
        contentType,
        upsert: false,
      });

    if (error) {
      console.error('Error uploading file to Supabase:', error);
      throw new Error(`Failed to upload file: ${error.message}`);
    }

    // Get the public URL for the uploaded file
    const { data: publicUrlData } = supabaseServer.storage
      .from(bucketName)
      .getPublicUrl(uniqueFilename);

    return {
      url: publicUrlData.publicUrl,
      pathname: uniqueFilename,
      contentType,
      size: file.byteLength,
      bucket: bucketName,
    };
  } catch (error) {
    console.error('Error in serverUploadFileToSupabase:', error);
    throw error;
  }
}

/**
 * Deletes a file from Supabase Storage
 * @param path The path of the file to delete
 * @param bucketName The bucket the file is in (defaults to 'files')
 * @returns Boolean indicating success or failure
 */
export async function deleteFileFromSupabase({
  path,
  bucketName = 'files',
}: {
  path: string;
  bucketName?: string;
}) {
  try {
    const { error } = await supabase.storage
      .from(bucketName)
      .remove([path]);

    if (error) {
      console.error('Error deleting file from Supabase:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteFileFromSupabase:', error);
    return false;
  }
}
