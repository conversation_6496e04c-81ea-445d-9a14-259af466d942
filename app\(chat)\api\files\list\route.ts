import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { createClient } from '@supabase/supabase-js';

export async function GET(request: Request) {
  const session = await auth();

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const bucket = searchParams.get('bucket') || 'files';

  // Get user ID from session and use it as the folder path to isolate user files
  const userId = session.user?.id;
  if (!userId) {
    return NextResponse.json({ error: 'User ID not found' }, { status: 401 });
  }

  // Use the user ID as the folder to list only user's files
  const folder = searchParams.get('folder') || userId;
  const limit = parseInt(searchParams.get('limit') || '100');
  const offset = parseInt(searchParams.get('offset') || '0');

  try {
    // Check if service role key is available
    if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('SUPABASE_SERVICE_ROLE_KEY is not defined in environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create client with service role key to bypass RLS
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    // List files from Supabase Storage
    const { data, error } = await supabase.storage
      .from(bucket)
      .list(folder, {
        limit,
        offset,
        sortBy: { column: 'created_at', order: 'desc' },
      });

    if (error) {
      console.error('Error listing files from Supabase:', error);
      return NextResponse.json({ error: 'Failed to list files' }, { status: 500 });
    }

    // Get public URLs for each file
    const filesWithUrls = data.map(file => {
      if (file.name && !file.id.endsWith('/')) { // Skip folders
        const { data: urlData } = supabase.storage
          .from(bucket)
          .getPublicUrl(`${folder ? `${folder}/` : ''}${file.name}`);

        return {
          ...file,
          url: urlData.publicUrl,
          path: `${folder ? `${folder}/` : ''}${file.name}`,
        };
      }
      return file;
    });

    return NextResponse.json({ files: filesWithUrls });
  } catch (error) {
    console.error('Error in file list API:', error);
    return NextResponse.json(
      { error: 'Failed to list files' },
      { status: 500 },
    );
  }
}
