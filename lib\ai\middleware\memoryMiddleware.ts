import { LanguageModelV1Middleware } from 'ai';
import { getRelevantMemories } from '@/lib/memory/queries';

/**
 * Hyper-allergic memory middleware - pathologically sensitive to missing ANY potentially useful information
 * Assumes every detail could matter in the future and defaults to capturing rather than ignoring
 */
export const memoryMiddleware = (_userId: string): LanguageModelV1Middleware => {
  // Track memory operations during conversation with enhanced context
  const memoryState = new Map<string, {
    lastMemoryCheck: number,
    importantContext: string[],
    toolResults: any[],
    conversationContext: string,
    toolsUsed: string[],
    userPatterns: string[],
    conversationFlow: string,
    taskProgression: string,
    recentMessages: string[],
    personalContext: string
  }>();

  return async (params: any, runModel: any) => {
    // Extract conversation ID or create one if not present
    const conversationId = params.id || 'default';

    // Initialize state for this conversation if not exists
    if (!memoryState.has(conversationId)) {
      memoryState.set(conversationId, {
        lastMemoryCheck: Date.now(),
        importantContext: [],
        toolResults: [],
        conversationContext: '',
        toolsUsed: [],
        userPatterns: [],
        conversationFlow: '',
        taskProgression: '',
        recentMessages: [],
        personalContext: ''
      });
    }

    const state = memoryState.get(conversationId)!;

    // Update conversation context for awareness
    if (params.messages && params.messages.length > 0) {
      // Memory creation is now handled by the AI using the remember tool
      // No automatic memory extraction from middleware

      // Update conversation context with enhanced tracking
      updateConversationContext(params.messages, state);

      // Extract personal context from conversation
      state.personalContext = extractPersonalContext(params.messages);
    }

    return runModel(params);
  };
};

/**
 * Extract personal context and preferences from conversation
 */
function extractPersonalContext(messages: any[]): string {
  const personalInfo: string[] = [];

  messages.forEach(msg => {
    if (msg.role === 'user') {
      const text = extractTextFromMessage(msg).toLowerCase();

      // Extract personal preferences and context
      if (text.includes('my name is') || text.includes("i'm ")) {
        const nameMatch = text.match(/my name is (\w+)|i'm (\w+)/);
        if (nameMatch) {
          personalInfo.push(`User's name: ${nameMatch[1] || nameMatch[2]}`);
        }
      }

      if (text.includes('i prefer') || text.includes('i like')) {
        personalInfo.push(`Preference mentioned: ${text.substring(0, 100)}`);
      }

      if (text.includes('i work') || text.includes('my job') || text.includes('my company')) {
        personalInfo.push(`Work context: ${text.substring(0, 100)}`);
      }

      if (text.includes('i use') || text.includes('i usually')) {
        personalInfo.push(`Usage pattern: ${text.substring(0, 100)}`);
      }
    }
  });

  return personalInfo.slice(-5).join('; '); // Keep last 5 personal context items
}





/**
 * Optimized conversation context tracking for better performance
 */
function updateConversationContext(messages: any[], state: any) {
  // Optimized: Only process last 6 messages for better performance
  const recentMessages = messages.slice(-6);

  // Extract text more efficiently
  const messageTexts = recentMessages
    .map(msg => {
      const text = extractTextFromMessage(msg);
      const role = msg.role;
      return text.length > 0 ? `${role}: ${text.substring(0, 100)}` : null; // Limit text length
    })
    .filter(text => text !== null);

  // Track tools used in this conversation (optimized)
  const toolsUsed = new Set<string>();
  const userPatterns = new Set<string>();

  // Only analyze recent messages for patterns to improve performance
  recentMessages.forEach(msg => {
    // Track tool calls
    if (msg.toolCalls && Array.isArray(msg.toolCalls)) {
      msg.toolCalls.forEach((tool: any) => {
        if (tool.name) {
          toolsUsed.add(tool.name);
        }
      });
    }

    // Track user patterns naturally
    if (msg.role === 'user') {
      const text = extractTextFromMessage(msg);

      // Simple length-based pattern detection only
      if (text.length > 150) {
        userPatterns.add('Detailed communicator');
      }
    }
  });

  // Simplified conversation flow analysis
  let conversationFlow = '';
  if (messages.length <= 2) {
    conversationFlow = 'Initial conversation';
  } else if (messages.length <= 6) {
    conversationFlow = 'Early conversation';
  } else {
    conversationFlow = 'Active conversation';
  }

  // Simplified task progression
  let taskProgression = '';
  const hasToolCalls = recentMessages.some((msg: any) => msg.toolCalls && msg.toolCalls.length > 0);

  if (hasToolCalls) {
    taskProgression = 'Task execution active';
  } else {
    taskProgression = 'Planning phase';
  }

  // Update state with optimized context
  state.conversationContext = messageTexts.join('\n').substring(0, 1000); // Reduced limit for performance
  state.toolsUsed = Array.from(toolsUsed);
  state.userPatterns = Array.from(userPatterns);
  state.conversationFlow = conversationFlow;
  state.taskProgression = taskProgression;
  state.recentMessages = messageTexts.slice(-5); // Keep last 5 formatted messages
}

/**
 * Extracts text content from a message
 */
function extractTextFromMessage(message: any): string {
  if (typeof message.content === 'string') {
    return message.content;
  }

  if (Array.isArray(message.content)) {
    return message.content
      .filter((part: any) => part.type === 'text')
      .map((part: any) => part.text)
      .join(' ');
  }

  return '';
}

/**
 * Enhanced memory retrieval with real-time context
 */
export async function getContextualMemories(
  userId: string,
  query: string,
  conversationContext?: string,
  limit: number = 5
) {
  return await getRelevantMemories({
    userId,
    query,
    context: conversationContext,
    limit,
    similarityThreshold: 0.3
  });
}

/**
 * Get enhanced conversation context for dynamic prompt injection
 */
export function getConversationContext(messages: any[]): {
  recentMessages: string[];
  toolsUsed: string[];
  userPatterns: string[];
  conversationFlow: string;
  taskProgression: string;
  personalContext: string;
} {
  // Create a temporary state to analyze the conversation
  const tempState = {
    conversationContext: '',
    toolsUsed: [],
    userPatterns: [],
    conversationFlow: '',
    taskProgression: '',
    recentMessages: [],
    personalContext: ''
  };

  // Analyze the conversation
  updateConversationContext(messages, tempState);
  tempState.personalContext = extractPersonalContext(messages);

  return {
    recentMessages: tempState.recentMessages,
    toolsUsed: tempState.toolsUsed,
    userPatterns: tempState.userPatterns,
    conversationFlow: tempState.conversationFlow,
    taskProgression: tempState.taskProgression,
    personalContext: tempState.personalContext
  };
}
