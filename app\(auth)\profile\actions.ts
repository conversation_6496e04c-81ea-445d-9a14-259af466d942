'use server';

import { z } from 'zod';
import { compare, genSaltSync, hashSync } from 'bcrypt-ts';
import { eq } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { auth } from '@/app/(auth)/auth';
import { user } from '@/lib/db/schema';
import { getUser } from '@/lib/db/queries';
import { ProfileActionState } from './page';

const profileFormSchema = z.object({
  email: z.string().email(),
  currentPassword: z.string().min(6).optional(),
  newPassword: z.string().min(6).optional(),
  confirmPassword: z.string().min(6).optional(),
});

// biome-ignore lint: Forbidden non-null assertion.
const client = postgres(process.env.POSTGRES_URL!);
const db = drizzle(client);

export async function updateProfile(
  _: ProfileActionState,
  formData: FormData,
): Promise<ProfileActionState> {
  try {
    const session = await auth();
    if (!session || !session.user) {
      return { status: 'failed' };
    }

    const validatedData = profileFormSchema.parse({
      email: formData.get('email'),
      currentPassword: formData.get('currentPassword') || undefined,
      newPassword: formData.get('newPassword') || undefined,
      confirmPassword: formData.get('confirmPassword') || undefined,
    });

    // Check if user wants to update password
    if (validatedData.newPassword) {
      // Ensure current password is provided
      if (!validatedData.currentPassword) {
        return { status: 'invalid_data' };
      }

      // Ensure new password and confirm password match
      if (validatedData.newPassword !== validatedData.confirmPassword) {
        return { status: 'password_mismatch' };
      }

      // Verify current password
      const users = await getUser(validatedData.email);
      if (users.length === 0) {
        return { status: 'failed' };
      }

      // biome-ignore lint: Forbidden non-null assertion.
      const passwordsMatch = await compare(validatedData.currentPassword, users[0].password!);
      if (!passwordsMatch) {
        return { status: 'failed' };
      }

      // Update password
      const salt = genSaltSync(10);
      const hash = hashSync(validatedData.newPassword, salt);

      await db
        .update(user)
        .set({ password: hash })
        .where(eq(user.id, session.user.id));
    }

    return { status: 'success' };
  } catch (error) {
    console.error('Failed to update profile', error);
    if (error instanceof z.ZodError) {
      return { status: 'invalid_data' };
    }
    return { status: 'failed' };
  }
}
