import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { createMemory } from '@/lib/memory/queries';

export async function POST(request: Request) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the request body
    const { content, category } = await request.json();

    // Validate the request
    if (!content) {
      return NextResponse.json(
        { error: 'Content is required' },
        { status: 400 }
      );
    }

    // Get the user ID
    const userId = session.user.id;

    // Create the memory
    const result = await createMemory({
      userId,
      content,
      category,
    });

    return NextResponse.json({
      success: true,
      memory: result[0],
    });
  } catch (error) {
    console.error('Error creating memory:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
