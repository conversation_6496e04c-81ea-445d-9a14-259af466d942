'use client';
import { ChevronUp } from 'lucide-react';
import Image from 'next/image';
import type { User } from 'next-auth';
import { signOut } from 'next-auth/react';
import { useTheme } from 'next-themes';
import { useRouter } from 'next/navigation';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import { ProfileSettingsModal } from './profile-settings-modal';

export function SidebarUserNav({ user }: { user: User }) {
  const { setTheme, theme } = useTheme();
  const router = useRouter();

  return (
    <SidebarMenu className="md:hidden">
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger
            asChild
            className="data-[state=open]:bg-sidebar-accent/20 data-[state=open]:border-sidebar-primary/50 data-[state=open]:text-sidebar-foreground"
          >
            <SidebarMenuButton className="data-[state=open]:bg-sidebar-accent bg-transparent data-[state=open]:text-sidebar-accent-foreground h-10 border border-sidebar-border/70 dark:border-sidebar-border/40 hover:bg-sidebar-accent/40 hover:border-sidebar-primary/50 dark:hover:bg-sidebar-accent/30 hover:text-sidebar-foreground">
              <div className="flex items-center w-full">
                <Image
                  src={`https://avatar.vercel.sh/${user.email}`}
                  alt={user.email ?? 'User Avatar'}
                  width={20}
                  height={20}
                  className="rounded-full"
                />
                <span className="truncate text-sidebar-foreground ml-2">{user?.email}</span>
                <ChevronUp className="ml-auto text-sidebar-foreground/70" size={14} />
              </div>
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            side="top"
            className="w-[--radix-popper-anchor-width] p-1 border-sidebar-border/70 dark:border-sidebar-border/40 shadow-md"
          >
            <DropdownMenuItem
              className="cursor-pointer rounded-md px-3 py-2 my-1 hover:bg-sidebar-accent/40 dark:hover:bg-sidebar-accent/30"
              onSelect={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
            >
              {`Toggle ${theme === 'light' ? 'dark' : 'light'} mode`}
            </DropdownMenuItem>
            <ProfileSettingsModal
              trigger={
                <DropdownMenuItem
                  className="cursor-pointer rounded-md px-3 py-2 my-1 hover:bg-sidebar-accent/40 dark:hover:bg-sidebar-accent/30"
                  onSelect={(e) => e.preventDefault()}
                >
                  Profile Settings
                </DropdownMenuItem>
              }
            />

            <DropdownMenuItem
              className="cursor-pointer rounded-md px-3 py-2 my-1 hover:bg-sidebar-accent/40 dark:hover:bg-sidebar-accent/30"
              onSelect={() => router.push('/files')}
            >
              File Manager
            </DropdownMenuItem>
            <DropdownMenuSeparator className="my-1 bg-sidebar-border/50" />
            <DropdownMenuItem
              className="cursor-pointer rounded-md px-3 py-2 my-1 hover:bg-sidebar-accent/40 dark:hover:bg-sidebar-accent/30"
              asChild
            >
              <button
                type="button"
                className="w-full text-left"
                onClick={() => {
                  signOut({
                    redirectTo: '/',
                  });
                }}
              >
                Sign out
              </button>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
