import NextAuth from 'next-auth';

import { authConfig } from '@/app/(auth)/auth.config';

export default NextAuth(authConfig).auth;

export const config = {
  matcher: [
    '/',
    '/:id',
    '/api/:path*',
    '/login',
    '/register',
    '/register/confirmation',
    '/profile',
    '/files',
    '/api/user',
    '/api/profile',
    '/confirm',
    '/reset-password-request',
    '/reset-password',
    // Add auth callback routes that Supabase might use
    '/auth/callback',
  ],
};
