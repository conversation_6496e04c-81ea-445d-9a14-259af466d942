'use client';

import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import * as z from 'zod';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { SubmitButton } from './submit-button';

const resetPasswordSchema = z.object({
  currentPassword: z.string().min(6, 'Password must be at least 6 characters'),
  newPassword: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string().min(6, 'Password must be at least 6 characters'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export function ResetPasswordForm({ email }: { email: string }) {
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formErrors, setFormErrors] = useState<{
    currentPassword?: string;
    newPassword?: string;
    confirmPassword?: string;
  }>({});

  const showToast = useCallback((type: 'success' | 'error' | 'info', message: string) => {
    // Use setTimeout to avoid React state updates during render
    setTimeout(() => {
      toast[type](message);
    }, 0);
  }, []);

  const handleSubmit = useCallback(async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Reset form errors
    setFormErrors({});

    const formData = new FormData(e.currentTarget);

    // Get form values
    const currentPassword = formData.get('currentPassword') as string;
    const newPassword = formData.get('newPassword') as string;
    const confirmPassword = formData.get('confirmPassword') as string;

    // Client-side validation
    try {
      resetPasswordSchema.parse({
        currentPassword,
        newPassword,
        confirmPassword
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: {
          currentPassword?: string;
          newPassword?: string;
          confirmPassword?: string;
        } = {};

        error.errors.forEach((err) => {
          if (err.path[0]) {
            errors[err.path[0] as keyof typeof errors] = err.message;
          }
        });

        setFormErrors(errors);
        showToast('error', 'Please fix the errors in the form');
        return;
      }
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('/api/profile', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        if (data.error === 'invalid_data') {
          showToast('error', data.message || 'Invalid form data!');
        } else if (data.error === 'password_mismatch') {
          setFormErrors({ confirmPassword: 'Passwords do not match' });
          showToast('error', data.message || 'New passwords do not match!');
        } else if (data.error === 'invalid_credentials') {
          setFormErrors({ currentPassword: 'Current password is incorrect' });
          showToast('error', data.message || 'Current password is incorrect');
        } else {
          showToast('error', data.message || 'Failed to update password!');
        }
        setIsSubmitting(false);
        return;
      }

      // Clear form fields after successful update
      e.currentTarget.reset();

      setIsSuccessful(true);
      showToast('success', 'Password updated successfully!');

      // Reset success state after 3 seconds
      setTimeout(() => {
        setIsSuccessful(false);
      }, 3000);
    } catch (error) {
      console.error('Error updating password:', error);
      showToast('error', 'An error occurred while updating your password.');
    } finally {
      setIsSubmitting(false);
    }
  }, [showToast]);

  return (
    <form id="reset-password-form" onSubmit={handleSubmit} className="flex flex-col gap-4">
      <input type="hidden" name="email" value={email} />

      <div className="flex flex-col gap-2">
        <Label
          htmlFor="currentPassword"
          className="text-zinc-600 font-normal dark:text-zinc-400"
        >
          Current Password
        </Label>
        <Input
          id="currentPassword"
          name="currentPassword"
          className={`bg-muted text-md md:text-sm ${formErrors.currentPassword ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
          type="password"
          placeholder="••••••••"
          autoComplete="current-password"
        />
        {formErrors.currentPassword && (
          <p className="text-xs text-red-500 mt-1">{formErrors.currentPassword}</p>
        )}
      </div>

      <div className="flex flex-col gap-2 mt-2">
        <Label
          htmlFor="newPassword"
          className="text-zinc-600 font-normal dark:text-zinc-400"
        >
          New Password
        </Label>
        <Input
          id="newPassword"
          name="newPassword"
          className={`bg-muted text-md md:text-sm ${formErrors.newPassword ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
          type="password"
          placeholder="••••••••"
          autoComplete="new-password"
        />
        {formErrors.newPassword && (
          <p className="text-xs text-red-500 mt-1">{formErrors.newPassword}</p>
        )}
      </div>

      <div className="flex flex-col gap-2 mt-2">
        <Label
          htmlFor="confirmPassword"
          className="text-zinc-600 font-normal dark:text-zinc-400"
        >
          Confirm New Password
        </Label>
        <Input
          id="confirmPassword"
          name="confirmPassword"
          className={`bg-muted text-md md:text-sm ${formErrors.confirmPassword ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
          type="password"
          placeholder="••••••••"
          autoComplete="new-password"
        />
        {formErrors.confirmPassword && (
          <p className="text-xs text-red-500 mt-1">{formErrors.confirmPassword}</p>
        )}
      </div>

      <SubmitButton isSuccessful={isSuccessful} isSubmitting={isSubmitting}>Update Password</SubmitButton>
    </form>
  );
}
