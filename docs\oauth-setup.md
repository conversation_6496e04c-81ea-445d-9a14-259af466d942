# Setting Up OAuth for White-Labeling

To use your own OAuth credentials instead of Composio's default ones, you need to:

1. Create OAuth credentials in the respective provider's developer console
2. Add these credentials to your `.env.local` file
3. Deploy your application with these environment variables

## Step 1: Create OAuth Credentials

### For Google (Gmail, Google Calendar)

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" > "Credentials"
4. Click "Create Credentials" > "OAuth client ID"
5. Select "Web application" as the application type
6. Add your app's domain to the "Authorized JavaScript origins"
7. Add `https://your-domain.com/oauth-callback` to the "Authorized redirect URIs"
8. Click "Create" and note down the Client ID and Client Secret

### For GitHub

1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Click "New OAuth App"
3. Fill in the application details
4. Set the "Authorization callback URL" to `https://your-domain.com/oauth-callback`
5. Click "Register application" and note down the Client ID
6. Generate a new client secret and note it down

## Step 2: Add Credentials to Environment Variables

Add the following to your `.env.local` file:

```
# Google OAuth Credentials
OAUTH_GMAIL_CLIENT_ID=your_gmail_client_id
OAUTH_GMAIL_CLIENT_SECRET=your_gmail_client_secret
OAUTH_GOOGLECALENDAR_CLIENT_ID=your_google_calendar_client_id
OAUTH_GOOGLECALENDAR_CLIENT_SECRET=your_google_calendar_client_secret

# GitHub OAuth Credentials
OAUTH_GITHUB_CLIENT_ID=your_github_client_id
OAUTH_GITHUB_CLIENT_SECRET=your_github_client_secret

# Other providers as needed
OAUTH_NOTION_CLIENT_ID=your_notion_client_id
OAUTH_NOTION_CLIENT_SECRET=your_notion_client_secret
```

## Step 3: Deploy with Environment Variables

Make sure to add these environment variables to your deployment platform (Vercel, Netlify, etc.).

## Testing Locally

For local testing, you can use localhost URLs:

1. Set the redirect URI in the provider's developer console to `http://localhost:3000/oauth-callback`
2. Use the same credentials in your `.env.local` file

## Troubleshooting

If you encounter issues:

1. Check that the redirect URI exactly matches what's configured in the provider's developer console
2. Ensure the OAuth scopes are properly configured
3. Verify that the environment variables are correctly set
4. Check the server logs for any error messages related to OAuth
