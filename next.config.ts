import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  // Add serverExternalPackages to handle @libsql packages
  serverExternalPackages: ['@libsql/client', 'libsql'],
  experimental: {
    ppr: true,
    // Turbopack configuration
    turbo: {
      rules: {
        // Ignore problematic files in node_modules
        '**/@libsql/*/README.md': {
          loaders: ['ignore-loader'],
        },
        '**/@libsql/*/LICENSE': {
          loaders: ['ignore-loader'],
        },
        '**/node_modules/**/*.md': {
          loaders: ['ignore-loader'],
        },
        '**/node_modules/**/LICENSE': {
          loaders: ['ignore-loader'],
        },
        // Ignore @mastra/core package warnings
        '**/node_modules/@mastra/core/**/@libsql/client': {
          loaders: ['ignore-loader'],
        },
      },
      // Resolve external packages with specific versions
      resolveAlias: {
        '@libsql/client': '@libsql/client',
        'libsql': 'libsql',
      },
    },
  },
  images: {
    remotePatterns: [
      {
        hostname: 'avatar.vercel.sh',
      },
    ],
  },
  webpack: (config, { isServer }) => {
    // Create a rule for handling problematic files in node_modules
    config.module.rules.push({
      test: /\.(md|LICENSE|txt)$/,
      include: /node_modules/,
      use: 'ignore-loader',
      // This ensures these files are completely ignored
      sideEffects: false,
    });

    // Add a specific rule for @mastra/core package
    config.module.rules.push({
      test: /\.js$/,
      include: /node_modules\/@mastra\/core/,
      use: 'ignore-loader',
      // This ensures these files are completely ignored when they try to import @libsql
      resourceQuery: /@libsql\/client/,
    });

    // Handle specific @libsql packages
    if (isServer) {
      // Mark certain packages as external on the server
      const originalExternals = config.externals;
      config.externals = [
        ...(Array.isArray(originalExternals) ? originalExternals : [originalExternals]),
        function({ request }: { request: string }, callback: (err?: Error | null, result?: string) => void) {
          // Handle specific version mismatches
          if (request === '@libsql/client' || request === 'libsql') {
            return callback(null, `commonjs ${request}`);
          }
          callback();
        },
      ].filter(Boolean);
    }

    return config;
  },
  // Disable type checking and ESLint errors during build
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
};

export default nextConfig;
