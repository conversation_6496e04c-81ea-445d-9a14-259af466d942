'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

export default function TestGmailAIPage() {
  const [loading, setLoading] = useState(false);
  const [query, setQuery] = useState('');
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  async function testGmailAI() {
    if (!query.trim()) {
      setError('Please enter a query');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // First, use fetchActions to find Gmail actions
      const fetchResponse = await fetch('/api/tools/fetch-actions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          description: query
        }),
      });
      
      const fetchData = await fetchResponse.json();
      
      if (!fetchData.success) {
        throw new Error(fetchData.error || 'Failed to fetch Gmail actions');
      }

      // Display the results
      setResult({
        fetchActionsResult: fetchData
      });
    } catch (err) {
      setError('Error testing Gmail AI: ' + (err as Error).message);
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="flex min-h-screen w-full flex-col items-center justify-center py-12 px-4 bg-background">
      <div className="w-full max-w-md space-y-8 bg-card shadow-sm rounded-lg border p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold">Test Gmail AI Integration</h1>
          <p className="text-sm text-gray-500 mt-2">
            Test if the AI can find and use Gmail actions
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
            {error}
          </div>
        )}

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              Enter a Gmail-related query:
            </label>
            <Textarea
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="e.g., get my email profile, send an email, create a draft"
              className="w-full h-24"
            />
          </div>

          <div>
            <Button
              onClick={testGmailAI}
              disabled={loading}
              className="w-full"
            >
              {loading ? 'Testing...' : 'Test Gmail AI'}
            </Button>
          </div>

          {result && (
            <div className="bg-gray-50 border border-gray-200 p-4 rounded-md">
              <h3 className="font-medium mb-2">Result:</h3>
              <pre className="text-xs overflow-auto p-2 bg-gray-100 rounded max-h-96">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
