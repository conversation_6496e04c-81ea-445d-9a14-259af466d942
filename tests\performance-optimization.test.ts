import { describe, it, expect } from 'vitest';
import { getCoreSystemPrompt, generateConversationContext } from '../lib/ai/core-prompt';
import { getConversationContext } from '../lib/ai/middleware/memoryMiddleware';

describe('Performance Optimization Tests', () => {
  it('should generate optimized conversation context', () => {
    const conversationContext = {
      recentMessages: ['user: Hello', 'assistant: Hi there!', 'user: What\'s my calendar today?'],
      toolsUsed: ['getCurrentTime', 'fetchActions'],
      userPatterns: ['Polite communication'],
      conversationFlow: 'Early conversation',
      taskProgression: 'Task execution active',
      personalContext: 'User prefers calendar management'
    };

    const contextString = generateConversationContext(conversationContext);
    
    // Should be more concise than before
    expect(contextString).toContain('CONVERSATION AWARENESS');
    expect(contextString).toContain('RECENT FLOW');
    expect(contextString).toContain('TOOLS USED');
    expect(contextString.length).toBeLessThan(1000); // Should be concise
  });

  it('should handle empty conversation context efficiently', () => {
    const emptyContext = {
      recentMessages: [],
      toolsUsed: [],
      userPatterns: [],
      conversationFlow: '',
      taskProgression: '',
      personalContext: ''
    };

    const contextString = generateConversationContext(emptyContext);
    expect(contextString).toBe('');
  });

  it('should process conversation messages efficiently', () => {
    const messages = [
      { role: 'user', content: 'Hello' },
      { role: 'assistant', content: 'Hi there!' },
      { role: 'user', content: 'What\'s on my calendar today?' },
      { 
        role: 'assistant', 
        content: 'I\'ll check your calendar.',
        toolCalls: [{ name: 'getCurrentTime' }, { name: 'fetchActions' }]
      }
    ];

    const context = getConversationContext(messages);
    
    expect(context.toolsUsed).toContain('getCurrentTime');
    expect(context.toolsUsed).toContain('fetchActions');
    expect(context.conversationFlow).toBe('Early conversation');
    expect(context.taskProgression).toBe('Task execution active');
  });

  it('should generate system prompt with conversation context efficiently', () => {
    const conversationContext = {
      recentMessages: ['user: Hello'],
      toolsUsed: ['getCurrentTime'],
      userPatterns: ['Polite communication'],
      conversationFlow: 'Initial conversation',
      taskProgression: 'Planning phase',
      personalContext: ''
    };

    const prompt = getCoreSystemPrompt({
      selectedChatModel: 'chat-model',
      conversationContext,
    });

    expect(prompt).toContain('CONVERSATION AWARENESS');
    expect(prompt).toContain('getCurrentTime');
    expect(prompt).toContain('Artifacts is a special user interface mode'); // Should include artifacts
  });

  it('should limit conversation context size for performance', () => {
    // Test with many messages to ensure we limit processing
    const manyMessages = Array.from({ length: 20 }, (_, i) => ({
      role: i % 2 === 0 ? 'user' : 'assistant',
      content: `Message ${i + 1}`.repeat(50) // Long messages
    }));

    const context = getConversationContext(manyMessages);
    
    // Should only process recent messages
    expect(context.recentMessages.length).toBeLessThanOrEqual(5);
    
    // Each message should be truncated
    context.recentMessages.forEach(msg => {
      expect(msg.length).toBeLessThan(150); // Should be truncated
    });
  });
});
