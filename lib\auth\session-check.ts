'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from '@/components/toast';
import { signOut } from 'next-auth/react';

/**
 * Hook to check if the user's session is still valid
 * If the session becomes invalid (e.g., user removed from DB or confirmation status changed),
 * the user will be logged out
 */
export function useSessionCheck() {
  const router = useRouter();

  useEffect(() => {
    let isLoggingOut = false;
    let consecutiveErrors = 0;
    const MAX_CONSECUTIVE_ERRORS = 3;

    const checkSession = async () => {
      try {
        // Add timeout to the fetch request
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        const res = await fetch('/api/auth/session', {
          credentials: 'same-origin',
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!res.ok) {
          // If we get a server error (5xx), don't log out the user
          if (res.status >= 500) {
            console.warn(`Server error (${res.status}) during session check, continuing session`);
            consecutiveErrors++;

            // Only log out after multiple consecutive errors
            if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
              console.error(`${MAX_CONSECUTIVE_ERRORS} consecutive server errors, assuming session is invalid`);
              handleInvalidSession();
            }
            return;
          }

          throw new Error(`Network response was not ok: ${res.status}`);
        }

        // Reset consecutive errors counter on successful response
        consecutiveErrors = 0;

        const data = await res.json();

        if ((!data || !data.user) && !isLoggingOut) {
          handleInvalidSession();
        }
      } catch (error) {
        console.error('Error checking session:', error);

        // Don't log out on network errors or timeouts
        if (error.name === 'AbortError') {
          console.warn('Session check timed out, continuing session');
        } else if (error.name === 'TypeError' && error.message.includes('NetworkError')) {
          console.warn('Network error during session check, continuing session');
        } else {
          consecutiveErrors++;
          console.warn(`Session check error (${consecutiveErrors}/${MAX_CONSECUTIVE_ERRORS})`);

          // Only log out after multiple consecutive errors
          if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
            console.error(`${MAX_CONSECUTIVE_ERRORS} consecutive errors, assuming session is invalid`);
            handleInvalidSession();
          }
        }
      }
    };

    const handleInvalidSession = async () => {
      if (isLoggingOut) return;

      isLoggingOut = true;
      try {
        await signOut({ redirect: false });
        toast({
          type: 'error',
          description: 'Your session has expired. Please log in again.'
        });
        if (window.location.pathname !== '/login') {
          router.push('/login');
        }
      } catch (error) {
        console.error('Error during sign out:', error);
        // Force reload as a last resort
        window.location.href = '/login';
      }
    };

    checkSession();
    const interval = setInterval(checkSession, 15 * 60 * 1000); // Check every 15 minutes
    return () => clearInterval(interval);
  }, [router]);
}
