'use client';

import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { HomeIcon, ArrowLeftIcon, RefreshIcon } from '@/components/icons';

interface NotFoundProps {
  title?: string;
  message?: string;
  showHomeButton?: boolean;
  showBackButton?: boolean;
  showRefreshButton?: boolean;
}

export function NotFound({
  title = 'Not Found',
  message = 'The resource you are looking for does not exist or has been removed.',
  showHomeButton = true,
  showBackButton = true,
  showRefreshButton = true,
}: NotFoundProps) {
  const router = useRouter();

  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] px-4 text-center">
      <div className="rounded-full bg-muted p-6 mb-6">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="h-10 w-10 text-muted-foreground"
        >
          <circle cx="12" cy="12" r="10" />
          <path d="m15 9-6 6" />
          <path d="m9 9 6 6" />
        </svg>
      </div>
      <h1 className="text-3xl font-bold tracking-tighter mb-2">{title}</h1>
      <p className="text-muted-foreground max-w-[500px] mb-6">{message}</p>
      <div className="flex flex-wrap gap-4 justify-center">
        {showHomeButton && (
          <Button onClick={() => router.push('/')} variant="outline">
            <HomeIcon className="mr-2 h-4 w-4" />
            Go Home
          </Button>
        )}
        {showBackButton && (
          <Button onClick={() => router.back()} variant="outline">
            <ArrowLeftIcon className="mr-2 h-4 w-4" />
            Go Back
          </Button>
        )}
        {showRefreshButton && (
          <Button onClick={() => window.location.reload()} variant="outline">
            <RefreshIcon className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        )}
      </div>
    </div>
  );
}
