'use client';

import cx from 'classnames';
import { format } from 'date-fns';

interface CurrentTimeData {
  datetime: string;
  timezone_name: string;
  timezone_location: string;
  timezone_abbreviation: string;
  gmt_offset: number;
  is_dst: boolean;
  requested_location: string;
  latitude: number;
  longitude: number;
  message?: string;
  error?: string;
}

export function CurrentTime({
  timeData,
}: {
  timeData: CurrentTimeData;
}) {
  if (timeData.error) {
    return (
      <div className="flex flex-col gap-2 rounded-lg border border-red-200 bg-red-50 p-4 text-red-800">
        <div className="font-medium">Error retrieving time data</div>
        <div className="text-sm">{timeData.error}</div>
      </div>
    );
  }

  // Check if datetime exists before trying to parse it
  if (!timeData.datetime) {
    return (
      <div className="flex flex-col gap-2 rounded-lg border border-red-200 bg-red-50 p-4 text-red-800">
        <div className="font-medium">Invalid time data format</div>
        <div className="text-sm">The time data is missing the datetime property</div>
      </div>
    );
  }

  // Parse the datetime string into a Date object
  const dateTime = new Date(timeData.datetime.replace(' ', 'T'));

  // Format the date and time parts
  const datePart = format(dateTime, 'EEEE, MMMM d, yyyy');
  const timePart = format(dateTime, 'h:mm:ss a');

  return (
    <div className="flex flex-col gap-4 rounded-2xl p-4 bg-gradient-to-br from-blue-500 to-indigo-600 text-white max-w-[500px]">
      <div className="flex flex-row justify-between items-center">
        <div className="flex flex-col">
          <div className="text-2xl font-medium">{timePart}</div>
          <div className="text-sm text-blue-100">{datePart}</div>
        </div>
        <div className="flex flex-col items-end">
          <div className="text-lg font-medium">{timeData.timezone_name}</div>
          <div className="text-sm text-blue-100">
            {timeData.timezone_abbreviation} (GMT{timeData.gmt_offset >= 0 ? '+' : ''}{timeData.gmt_offset})
          </div>
        </div>
      </div>

      <div className="text-sm text-blue-100">
        <div>Location: {timeData.requested_location}</div>
        {timeData.is_dst && <div>Daylight Saving Time is in effect</div>}
      </div>
    </div>
  );
}
