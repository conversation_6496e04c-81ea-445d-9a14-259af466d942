import { OpenAIToolSet } from "composio-core";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
  try {
    const { action } = await req.json();
    
    if (!action) {
      return NextResponse.json({ 
        successful: false,
        error: 'Action name is required',
        details: 'Please provide a valid action name'
      }, { status: 400 });
    }

    const toolset = new OpenAIToolSet();
    
    try {
      const response = await toolset.client.actions.get({
        actionName: action
      });

      if (!response) {
        throw new Error('Failed to fetch schema');
      }

      return NextResponse.json({
        successful: true,
        schema: response
      });

    } catch (schemaError) {
      console.error('Schema fetch error:', schemaError);
      
      return NextResponse.json({ 
        successful: false,
        error: 'Failed to fetch schema',
        details: schemaError instanceof Error ? schemaError.message : 'Unknown error',
        errorObject: schemaError
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Request parse error:', error);
    
    return NextResponse.json({ 
      successful: false,
      error: 'Invalid request format',
      details: error instanceof Error ? error.message : 'Failed to parse request'
    }, { status: 400 });
  }
}