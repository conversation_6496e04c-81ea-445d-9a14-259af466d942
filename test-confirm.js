// Test script to verify the database update is working correctly
import { updateUserConfirmedStatus } from './lib/db/queries.ts';

async function testConfirmation() {
  try {
    // Replace with an actual email from your database
    const email = '<EMAIL>';

    console.log(`Testing confirmation for user: ${email}`);

    const result = await updateUserConfirmedStatus({
      email,
      confirmed: true
    });

    console.log('Update result:', result);
    console.log('Test completed successfully');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testConfirmation();
