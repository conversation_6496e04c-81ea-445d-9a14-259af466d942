-- Migration: Add embedding column to ToolExperiences table for vector similarity search
-- This enables semantic similarity matching instead of just tag-based filtering

-- Add embedding column to store vector embeddings
ALTER TABLE "ToolExperiences" 
ADD COLUMN IF NOT EXISTS "embedding" vector(768);

-- Create index for efficient vector similarity search
CREATE INDEX IF NOT EXISTS "idx_tool_experiences_embedding" 
ON "ToolExperiences" USING ivfflat ("embedding" vector_cosine_ops);

-- Update existing records with embeddings (you'll need to run this after the migration)
-- This is a placeholder - actual embeddings will be generated by the application
COMMENT ON COLUMN "ToolExperiences"."embedding" IS 'Vector embedding for semantic similarity search (768 dimensions)';

-- Add comment about the new vector-based approach
COMMENT ON TABLE "ToolExperiences" IS 'Stores AI agent learning patterns with vector embeddings for semantic similarity. Implements 80/20 rule where 20% of patterns solve 80% of user tasks.';
