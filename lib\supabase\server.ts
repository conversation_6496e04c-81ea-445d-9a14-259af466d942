'use server';

import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

// These environment variables are set in .env.local
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// Create a Supabase client for server components and server actions
export async function createServerClient() {
  const cookieStore = cookies();

  return createClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      get(name: string) {
        return cookieStore.get(name)?.value;
      },
      set(name: string, value: string, options: { path: string; maxAge: number; domain?: string }) {
        cookieStore.set({ name, value, ...options });
      },
      remove(name: string, options: { path: string; domain?: string }) {
        cookieStore.set({ name, value: '', ...options, maxAge: 0 });
      },
    },
  });
}
