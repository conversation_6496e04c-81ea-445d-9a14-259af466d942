'use client';

import { useState, useRef } from 'react';
import { useFileStorage } from '@/lib/hooks/use-file-storage';
import { Button } from './ui/button';
import { toast } from 'sonner';
import { Upload, Loader2 } from 'lucide-react';
import { Progress } from './ui/progress';

export function FileUpload({ onUploadComplete }: { onUploadComplete?: () => void }) {
  const { uploadFile, uploadProgress, isUploading } = useFileStorage();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    try {
      const uploadPromises = files.map(file => uploadFile(file));
      await Promise.all(uploadPromises);
      
      if (onUploadComplete) {
        onUploadComplete();
      }
      
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error uploading files:', error);
      toast.error('Failed to upload files');
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-6 transition-colors hover:border-gray-400 dark:hover:border-gray-600">
        <Upload className="h-10 w-10 text-gray-400 dark:text-gray-600 mb-2" />
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
          Drag and drop files here, or click to select files
        </p>
        <p className="text-xs text-gray-500 dark:text-gray-500 mb-4">
          Supported formats: JPEG, PNG, PDF, TXT, DOC, DOCX (Max 5MB)
        </p>
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          onChange={handleFileChange}
          multiple
          accept="image/jpeg,image/png,application/pdf,text/plain,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        />
        <Button
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
        >
          {isUploading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Uploading...
            </>
          ) : (
            'Select Files'
          )}
        </Button>
      </div>

      {uploadProgress.length > 0 && (
        <div className="space-y-2">
          <p className="text-sm font-medium">Upload Progress</p>
          {uploadProgress.map((item) => (
            <div key={item.filename} className="space-y-1">
              <div className="flex justify-between text-xs">
                <span className="truncate max-w-[80%]">{item.filename}</span>
                <span>{item.status === 'uploading' ? `${item.progress}%` : item.status}</span>
              </div>
              <Progress value={item.progress} className="h-1" />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
