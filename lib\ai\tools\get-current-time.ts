import { tool } from 'ai';
import { z } from 'zod';
import { getCurrentTimeForLocation } from '@/lib/utils/date-time';

export const getCurrentTime = tool({
  description: 'Get the current date and time for a specific location',
  parameters: z.object({
    location: z.string().describe('The location to get the current time for (city, country, coordinates, or IP)'),
  }),
  execute: async ({ location }) => {
    try {
      const timeData = await getCurrentTimeForLocation(location);
      
      return {
        ...timeData,
        message: `Current time in ${timeData.requested_location} is ${timeData.datetime} (${timeData.timezone_name}, ${timeData.timezone_abbreviation})`
      };
    } catch (error) {
      console.error('Error in getCurrentTime tool:', error);
      return {
        error: 'Failed to get current time',
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});
