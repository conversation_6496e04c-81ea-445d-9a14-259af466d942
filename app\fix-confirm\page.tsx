'use client';

import { useState, useEffect, Suspense } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

// Loading component for Suspense
function FixConfirmLoading() {
  return (
    <div className="flex h-dvh w-screen items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl p-8 shadow-lg">
        <h1 className="text-2xl font-bold mb-6 text-center">Fix Email Confirmation</h1>
        <div className="space-y-4">
          <div className="h-10 w-full animate-pulse rounded-md bg-muted"></div>
          <div className="h-10 w-full animate-pulse rounded-md bg-muted"></div>
        </div>
      </div>
    </div>
  );
}

// Component that uses useSearchParams
function FixConfirmContent() {
  const searchParams = useSearchParams();
  const [email, setEmail] = useState('');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const emailParam = searchParams.get('email');
    if (emailParam) {
      setEmail(emailParam);
    }
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch(`/api/fix-confirm?email=${encodeURIComponent(email)}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fix confirmation status');
      }

      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex h-dvh w-screen items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl p-8 shadow-lg">
        <h1 className="text-2xl font-bold mb-6 text-center">Fix Email Confirmation</h1>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="email" className="block text-sm font-medium mb-1">
              Email Address
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full p-2 border rounded-md"
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Processing...' : 'Fix Confirmation Status'}
          </button>
        </form>

        {error && (
          <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md">
            {error}
          </div>
        )}

        {result && (
          <div className={`mt-4 p-3 rounded-md ${result.finalStatus ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'}`}>
            <p><strong>Initial Status:</strong> {result.initialStatus ? 'Confirmed' : 'Not Confirmed'}</p>
            <p><strong>Final Status:</strong> {result.finalStatus ? 'Confirmed' : 'Not Confirmed'}</p>
            <p className="mt-2">
              {result.finalStatus
                ? 'Confirmation status successfully updated! You can now log in.'
                : 'Failed to update confirmation status. Please contact support.'}
            </p>
            {result.finalStatus && (
              <div className="mt-2">
                <Link href="/login" className="text-blue-600 hover:underline font-medium">
                  Go to Login Page
                </Link>
              </div>
            )}
          </div>
        )}

        <div className="mt-6 text-center">
          <Link href="/login" className="text-blue-600 hover:underline">
            Back to Login
          </Link>
        </div>
      </div>
    </div>
  );
}

// Main page component with Suspense
export default function FixConfirmPage() {
  return (
    <Suspense fallback={<FixConfirmLoading />}>
      <FixConfirmContent />
    </Suspense>
  );
}
