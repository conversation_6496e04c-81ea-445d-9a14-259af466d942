'use server';

import { z } from 'zod';
import { createServerClient } from '@/lib/supabase/server';
import {
  createUser,
  getUser,
  createPasswordResetToken,
  getPasswordResetToken,
  markPasswordResetTokenAsUsed,
  updateUserPassword
} from '@/lib/db/queries';

import { signIn } from './auth';

const loginFormSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

const registerFormSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
});

export interface LoginActionState {
  status: 'idle' | 'in_progress' | 'success' | 'failed' | 'invalid_data' | 'email_not_verified';
  message?: string;
  email?: string;
}

export const login = async (
  _: LoginActionState,
  formData: FormData,
): Promise<LoginActionState> => {
  // Get the email outside the try block so we can use it in the catch block
  const email = formData.get('email') as string;

  try {
    const validatedData = loginFormSchema.parse({
      email,
      password: formData.get('password'),
    });

    await signIn('credentials', {
      email: validatedData.email,
      password: validatedData.password,
      redirect: false,
    });

    return { status: 'success' };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { status: 'invalid_data' };
    }

    console.log('Login error:', error);

    // Check if the error is about email verification
    if (error instanceof Error &&
        (error.message.includes('verify your email') ||
         error.message.includes('email verification'))) {
      console.log('Email verification error detected');
      return {
        status: 'email_not_verified',
        message: 'Email not verified',
        email
      };
    }

    // Check if we can find the user and if they're not confirmed
    try {
      const users = await getUser(email);
      if (users.length > 0 && users[0].confirmed === false) {
        console.log('Found unconfirmed user during login error handling');
        return {
          status: 'email_not_verified',
          message: 'Email not verified',
          email
        };
      }
    } catch (dbError) {
      console.error('Error checking user confirmation status:', dbError);
    }

    return { status: 'failed' };
  }
};

export interface RegisterActionState {
  status:
    | 'idle'
    | 'in_progress'
    | 'success'
    | 'failed'
    | 'user_exists'
    | 'invalid_data'
    | 'email_confirmation_sent';
}

export const register = async (
  _: RegisterActionState,
  formData: FormData,
): Promise<RegisterActionState> => {
  try {
    const validatedData = registerFormSchema.parse({
      email: formData.get('email'),
      password: formData.get('password'),
      firstName: formData.get('firstName'),
      lastName: formData.get('lastName'),
    });

    // Create a Supabase server client
    const supabase = await createServerClient();

    // Check if user already exists in Supabase
    // First, try to get the user by email
    const { data: existingUserData, error: getUserError } = await supabase.auth.signInWithPassword({
      email: validatedData.email,
      password: validatedData.password,
    });

    // If we can sign in, the user exists
    if (existingUserData?.user) {
      // Sign out immediately since we're just checking existence
      await supabase.auth.signOut();
      return { status: 'user_exists' };
    }

    // If we got an error other than invalid credentials, handle it
    if (getUserError && getUserError.message !== 'Invalid login credentials') {
      console.error('Error checking user existence:', getUserError);
      return { status: 'failed' };
    }

    // Sign up the user with Supabase Auth
    const { data: signUpData, error } = await supabase.auth.signUp({
      email: validatedData.email,
      password: validatedData.password,
      options: {
        emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/confirm`,
        data: {
          email: validatedData.email,
        }
      },
    });

    // If the user was created successfully, store them in our database
    if (signUpData?.user && !error) {
      try {
        // Create the user in our database
        await createUser(
          validatedData.email,
          validatedData.password,
          validatedData.firstName,
          validatedData.lastName
        );
      } catch (dbError) {
        console.error('Error creating user in database:', dbError);
        // Continue anyway since the auth user was created
      }
    }

    if (error) {
      console.error('Supabase signup error:', error);
      return { status: 'failed' };
    }

    // Don't sign in immediately - wait for email confirmation
    return { status: 'email_confirmation_sent' };
  } catch (error) {
    console.error('Registration error:', error);
    if (error instanceof z.ZodError) {
      return { status: 'invalid_data' };
    }

    return { status: 'failed' };
  }
};

// Password Reset Actions
export interface PasswordResetRequestResult {
  status: 'success' | 'user_not_found' | 'failed';
  message?: string;
}

export async function requestPasswordReset(email: string): Promise<PasswordResetRequestResult> {
  try {
    // Check if user exists and is confirmed
    const users = await getUser(email);
    if (users.length === 0) {
      return { status: 'user_not_found', message: 'No account found with this email address' };
    }

    // Create a password reset token
    const resetToken = await createPasswordResetToken(email);

    // Send the reset email using Supabase
    const supabase = await createServerClient();
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/reset-password?token=${resetToken.token}`,
    });

    if (error) {
      console.error('Error sending password reset email:', error);
      return { status: 'failed', message: 'Failed to send reset email' };
    }

    return { status: 'success' };
  } catch (error) {
    console.error('Error requesting password reset:', error);
    return { status: 'failed', message: 'An error occurred while processing your request' };
  }
}

export interface PasswordResetResult {
  status: 'success' | 'token_not_found' | 'token_expired' | 'failed';
  message?: string;
}

export async function resetPassword(token: string, newPassword: string): Promise<PasswordResetResult> {
  try {
    // Validate the token
    const resetToken = await getPasswordResetToken(token);
    if (!resetToken) {
      return { status: 'token_not_found', message: 'Invalid or expired reset token' };
    }

    // Check if token is expired
    if (new Date() > new Date(resetToken.expires) || resetToken.used) {
      return { status: 'token_expired', message: 'Reset token has expired' };
    }

    // Update the user's password
    await updateUserPassword(resetToken.email, newPassword);

    // Mark the token as used
    await markPasswordResetTokenAsUsed(token);

    // Update the password in Supabase as well
    const supabase = await createServerClient();
    const { error } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (error) {
      console.error('Error updating Supabase user password:', error);
      // We still consider this a success since the app's database was updated
    }

    return { status: 'success' };
  } catch (error) {
    console.error('Error resetting password:', error);
    return { status: 'failed', message: 'An error occurred while resetting your password' };
  }
}
