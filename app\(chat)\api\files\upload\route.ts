import { NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@/app/(auth)/auth';
import { serverUploadFileToSupabase } from '@/lib/supabase/storage';

// Use Blob instead of File since File is not available in Node.js environment
const FileSchema = z.object({
  file: z
    .instanceof(Blob)
    .refine((file) => file.size <= 5 * 1024 * 1024, {
      message: 'File size should be less than 5MB',
    })
    // Update the file type based on the kind of files you want to accept
    .refine((file) => ['image/jpeg', 'image/png', 'application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(file.type), {
      message: 'Unsupported file type. Please upload JPEG, PNG, PDF, TXT, DOC, or DOCX files.',
    }),
});

export async function POST(request: Request) {
  const session = await auth();

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  if (request.body === null) {
    return new Response('Request body is empty', { status: 400 });
  }

  try {
    const formData = await request.formData();
    const file = formData.get('file') as Blob;

    if (!file) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
    }

    const validatedFile = FileSchema.safeParse({ file });

    if (!validatedFile.success) {
      const errorMessage = validatedFile.error.errors
        .map((error) => error.message)
        .join(', ');

      return NextResponse.json({ error: errorMessage }, { status: 400 });
    }

    // Get filename from formData since Blob doesn't have name property
    const filename = (formData.get('file') as File).name;
    const fileBuffer = await file.arrayBuffer();

    try {
      // Create a unique filename with user ID to prevent collisions and ensure user isolation
      const userId = session.user?.id;
      if (!userId) {
        return NextResponse.json({ error: 'User ID not found' }, { status: 401 });
      }

      // Create a path with user ID as folder to isolate user files
      const filePath = `${userId}/${Date.now()}-${filename}`;

      // Get the Supabase client directly with service role key for admin access
      const { createClient } = await import('@supabase/supabase-js');

      // Check if service role key is available
      if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
        console.error('SUPABASE_SERVICE_ROLE_KEY is not defined in environment variables');
        return NextResponse.json(
          { error: 'Server configuration error' },
          { status: 500 }
        );
      }

      // Create client with service role key to bypass RLS
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY
      );

      // Upload the file to Supabase Storage
      const { data, error } = await supabase.storage
        .from('files')
        .upload(filePath, fileBuffer, {
          contentType: file.type,
          upsert: false,
        });

      if (error) {
        console.error('Error uploading file to Supabase:', error);
        return NextResponse.json({ error: `Upload failed: ${error.message}` }, { status: 500 });
      }

      // Get the public URL for the uploaded file
      const { data: publicUrlData } = supabase.storage
        .from('files')
        .getPublicUrl(filePath);

      const responseData = {
        url: publicUrlData.publicUrl,
        pathname: filePath,
        contentType: file.type,
        size: fileBuffer.byteLength,
        bucket: 'files',
        userId: userId,
      };

      return NextResponse.json(responseData);
    } catch (error) {
      console.error('Error uploading to Supabase:', error);
      return NextResponse.json({ error: 'Upload failed' }, { status: 500 });
    }
  } catch (error) {
    console.error('Error processing upload request:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 },
    );
  }
}
