import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { db } from '@/lib/db/client';
import { memory } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function DELETE(request: Request) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.id;

    // Soft delete all memories for the user by setting isActive to false
    await db
      .update(memory)
      .set({ isActive: false })
      .where(eq(memory.userId, userId));

    return NextResponse.json({
      success: true,
      message: 'All memories cleared successfully'
    });
  } catch (error) {
    console.error('Error clearing memories:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
