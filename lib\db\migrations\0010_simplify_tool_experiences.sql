-- Simplify ToolExperiences table to match user Memory pattern
-- This migration removes unnecessary columns and keeps only essential fields

-- First, backup existing data if needed
-- CREATE TABLE "ToolExperiences_backup" AS SELECT * FROM "ToolExperiences";

-- Drop the existing table
DROP TABLE IF EXISTS "ToolExperiences";

-- <PERSON><PERSON> simplified ToolExperiences table (matching user Memory pattern)
CREATE TABLE "ToolExperiences" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "app_name" text NOT NULL,
  "experience_content" text NOT NULL,
  "embedding" vector(768),
  "createdAt" timestamp DEFAULT now() NOT NULL,
  "isActive" boolean DEFAULT true NOT NULL
);

-- Create index for vector similarity search
CREATE INDEX IF NOT EXISTS idx_tool_experiences_embedding 
ON "ToolExperiences" USING ivfflat (embedding vector_cosine_ops);

-- Create index for app filtering
CREATE INDEX IF NOT EXISTS idx_tool_experiences_app_name 
ON "ToolExperiences" (app_name);

-- Create index for active filtering
CREATE INDEX IF NOT EXISTS idx_tool_experiences_active 
ON "ToolExperiences" ("isActive");

-- Insert sample data for testing
INSERT INTO "ToolExperiences" ("app_name", "experience_content", "isActive") VALUES
('github', 'For fetching user repositories: Use GITHUB_LIST_REPOSITORIES_FOR_THE_AUTHENTICATED_USER instead of GITHUB_LIST_REPOSITORIES_FOR_A_USER to get personal repos', true),
('github', 'When listing repositories, always check if user wants personal repos (my repos) vs public repos of a specific user', true),
('gmail', 'For sending emails, always include a clear subject line and check if user wants to send immediately or save as draft', true),
('notion', 'When creating Notion pages, ask for the database/parent page first, then create with proper properties', true),
('jira', 'For creating issues, always specify project key, issue type, and summary at minimum', true);

-- Verify the new structure
SELECT 
  table_name,
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns 
WHERE table_name = 'ToolExperiences'
ORDER BY ordinal_position;
