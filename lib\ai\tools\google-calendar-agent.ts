import { tool } from 'ai';
import { z } from 'zod';
import { auth } from '@/app/(auth)/auth';
import { OpenAIToolSet } from 'composio-core';
import { getCurrentTimeForLocation } from '@/lib/utils/date-time';
import { getConnectionsByUserId } from '@/lib/db/queries';

/**
 * Google Calendar Sub-Agent Tool
 *
 * AI-driven calendar assistant that lets the AI choose the appropriate action and parameters
 * based on natural language input. No hardcoded patterns - full AI flexibility.
 */

// Available Google Calendar actions with their parameter schemas
const AVAILABLE_CALENDAR_ACTIONS = {
  'GOOGLECALENDAR_CREATE_EVENT': {
    description: 'Create a new event in a google calendar',
    required_params: ['start_datetime'],
    optional_params: [
      'attendees', 'calendar_id', 'create_meeting_room', 'description', 'eventType',
      'event_duration_hour', 'event_duration_minutes', 'guestsCanInviteOthers',
      'guestsCanSeeOtherGuests', 'guests_can_modify', 'location', 'recurrence',
      'send_updates', 'summary', 'timezone', 'transparency', 'visibility'
    ],
    defaults: { calendar_id: 'primary', event_duration_minutes: 30, eventType: 'default', transparency: 'opaque', visibility: 'default' }
  },
  'GOOGLECALENDAR_DELETE_EVENT': {
    description: 'Delete an event from a google calendar',
    required_params: ['event_id'],
    optional_params: ['calendar_id'],
    defaults: { calendar_id: 'primary' }
  },
  'GOOGLECALENDAR_FIND_EVENT': {
    description: 'Find events in a google calendar based on a search query',
    required_params: [],
    optional_params: [
      'calendar_id', 'event_types', 'max_results', 'order_by', 'page_token',
      'query', 'show_deleted', 'single_events', 'timeMax', 'timeMin', 'updated_min'
    ],
    defaults: { calendar_id: 'primary', max_results: 10, single_events: true, event_types: ['default', 'outOfOffice', 'focusTime', 'workingLocation'] }
  },
  'GOOGLECALENDAR_FIND_FREE_SLOTS': {
    description: 'Find free slots in a google calendar for a specific time period',
    required_params: [],
    optional_params: ['calendar_expansion_max', 'group_expansion_max', 'items', 'time_max', 'time_min', 'timezone'],
    defaults: { calendar_expansion_max: 50, group_expansion_max: 100, items: ['primary'], timezone: 'UTC' }
  },
  'GOOGLECALENDAR_GET_CALENDAR': {
    description: 'Fetch a calendar based on the provided calendar id',
    required_params: [],
    optional_params: ['calendar_id'],
    defaults: { calendar_id: 'primary' }
  },
  'GOOGLECALENDAR_LIST_CALENDARS': {
    description: 'List all google calendars from the user\'s calendar list with pagination',
    required_params: [],
    optional_params: ['max_results', 'min_access_role', 'page_token', 'show_deleted', 'show_hidden', 'sync_token'],
    defaults: { max_results: 10 }
  },
  'GOOGLECALENDAR_PATCH_EVENT': {
    description: 'Patches an event in google calendar. Only specified fields will be updated',
    required_params: ['event_id', 'calendar_id'],
    optional_params: [
      'attendees', 'conference_data_version', 'description', 'end_time', 'location',
      'max_attendees', 'send_updates', 'start_time', 'summary', 'supports_attachments', 'timezone'
    ]
  },
  'GOOGLECALENDAR_UPDATE_EVENT': {
    description: 'Update an existing event in a google calendar',
    required_params: ['event_id', 'start_datetime'],
    optional_params: [
      'attendees', 'calendar_id', 'create_meeting_room', 'description', 'eventType',
      'event_duration_hour', 'event_duration_minutes', 'guestsCanInviteOthers',
      'guestsCanSeeOtherGuests', 'guests_can_modify', 'location', 'recurrence',
      'send_updates', 'summary', 'timezone', 'transparency', 'visibility'
    ],
    defaults: { calendar_id: 'primary', event_duration_minutes: 30, eventType: 'default', transparency: 'opaque', visibility: 'default' }
  },
  'GOOGLECALENDAR_QUICK_ADD': {
    description: 'Create a new event based on a simple text string (not preferred, use CREATE_EVENT when possible)',
    required_params: ['text'],
    optional_params: ['calendar_id', 'send_updates'],
    defaults: { calendar_id: 'primary', send_updates: 'none' }
  },
  'GOOGLECALENDAR_REMOVE_ATTENDEE': {
    description: 'Remove an attendee from an existing event',
    required_params: ['attendee_email', 'event_id'],
    optional_params: ['calendar_id'],
    defaults: { calendar_id: 'primary' }
  },
  'GOOGLECALENDAR_GET_CURRENT_DATE_TIME': {
    description: 'Get the current date and time of a specified timezone',
    required_params: [],
    optional_params: ['timezone']
  }
};

/**
 * Get app name from action
 */
function getAppNameFromAction(action: string): string {
  return action.toLowerCase().split('_')[0];
}



/**
 * Main Google Calendar Sub-Agent Tool
 */
export const googleCalendarAgent = tool({
  description: `Intelligent Google Calendar assistant that handles all calendar operations.

Available actions:
${Object.entries(AVAILABLE_CALENDAR_ACTIONS).map(([action, config]) =>
  `- ${action}: ${config.description}
    Required: ${config.required_params.join(', ') || 'none'}
    Optional: ${config.optional_params.join(', ')}`
).join('\n')}

You have full flexibility to:
1. Choose the most appropriate action based on the user's request
2. Set all required and optional parameters as needed
3. Infer dates, times, titles, and other details from natural language
4. Use smart defaults when appropriate

Current time context will be provided automatically.`,
  parameters: z.object({
    action: z.string().describe('The Google Calendar action to execute (e.g., GOOGLECALENDAR_CREATE_EVENT, GOOGLECALENDAR_FIND_EVENT, etc.)'),
    parameters: z.record(z.any()).describe('All parameters for the chosen action. Include both required and optional parameters as needed.'),
  }),
  execute: async ({ action, parameters = {} }) => {
    try {
      const session = await auth();

      if (!session || !session.user) {
        return {
          success: false,
          error: 'Authentication required',
          message: 'Please sign in to access your Google Calendar.',
        };
      }

      // Validate the action exists
      const actionConfig = AVAILABLE_CALENDAR_ACTIONS[action as keyof typeof AVAILABLE_CALENDAR_ACTIONS];
      if (!actionConfig) {
        return {
          success: false,
          error: 'Invalid calendar action',
          message: `Unknown action: ${action}. Available actions: ${Object.keys(AVAILABLE_CALENDAR_ACTIONS).join(', ')}`,
        };
      }

      console.log(`🗓️ Calendar Agent: Executing ${action}`);
      console.log(`📋 Parameters:`, JSON.stringify(parameters, null, 2));

      // Get user's connected services
      const connections = await getConnectionsByUserId(session.user.id);

      if (!connections || connections.length === 0) {
        return {
          success: false,
          error: 'No connected services',
          message: 'You need to connect your Google Calendar before executing calendar actions. Please connect your Google Calendar in the profile settings.',
          redirectUrl: "/profile/connections",
        };
      }

      // Find Google Calendar connection
      const appName = getAppNameFromAction(action);
      const appConnections = connections.filter(conn =>
        conn.provider.toLowerCase() === appName &&
        conn.status === 'ACTIVE'
      );

      if (appConnections.length === 0) {
        return {
          success: false,
          error: 'Google Calendar not connected',
          message: 'Your Google Calendar account is not connected or the connection is inactive. Please connect your Google Calendar in the profile settings.',
          redirectUrl: "/profile/connections",
        };
      }

      // Use the most recently created connection
      const connection = appConnections.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];
      const connectionId = connection.connectionId;

      console.log(`📅 Using Google Calendar connection: ${connectionId}`);

      // Apply defaults and merge with provided parameters
      const finalParams = { ...actionConfig.defaults, ...parameters };

      console.log(`🧠 Final parameters:`, JSON.stringify(finalParams, null, 2));

      // Validate required parameters
      const missingParams = actionConfig.required_params.filter(param => !finalParams[param]);
      if (missingParams.length > 0) {
        return {
          success: false,
          error: 'Missing required parameters',
          message: `Missing required parameters for ${action}: ${missingParams.join(', ')}`,
          requiredParams: actionConfig.required_params,
          providedParams: Object.keys(finalParams),
          missingParams,
        };
      }

      // Execute the calendar action
      const toolset = new OpenAIToolSet();

      let result;
      try {
        console.log(`🚀 Executing API call to Composio...`);
        console.log(`🚀 Action: ${action}`);
        console.log(`🚀 App: ${appName}`);
        console.log(`🚀 User ID: ${session.user.id}`);
        console.log(`🚀 Connection ID: ${connectionId}`);
        console.log(`🚀 Input Parameters:`, JSON.stringify(finalParams, null, 2));

        result = await toolset.client.actions.execute({
          actionName: action,
          requestBody: {
            appName: appName,
            input: finalParams,
            entityId: session.user.id,
            connectedAccountId: connectionId
          }
        });

        console.log(`✅ API call successful!`);
        console.log(`✅ Raw result:`, JSON.stringify(result, null, 2));
      } catch (error) {
        const execError = error as Error;
        console.error("❌ Calendar Agent execution error:", execError);
        console.error("❌ Error details:", {
          message: execError.message,
          stack: execError.stack,
          name: execError.name
        });

        // Handle specific error types
        if (execError.message && execError.message.includes('ConnectedAccountNotFoundError')) {
          return {
            success: false,
            error: 'Google Calendar connection expired',
            message: 'Your Google Calendar connection appears to have expired or been invalidated. Please reconnect your Google Calendar account and try again.',
            redirectUrl: "/profile/connections",
          };
        }

        if (execError.message && execError.message.includes('NotFoundError')) {
          return {
            success: false,
            error: 'Calendar action not found',
            message: `The calendar operation "${action}" could not be found. This might be a temporary issue.`,
          };
        }

        return {
          success: false,
          error: 'Calendar operation failed',
          message: `Failed to execute ${action}: ${execError.message}`,
          finalParams,
        };
      }

      // Process and format the result
      console.log(`🔍 Processing result...`);
      console.log(`🔍 Result structure:`, {
        hasResult: !!result,
        successful: result?.successful,
        hasData: !!result?.data,
        hasError: !!result?.error,
        keys: result ? Object.keys(result) : []
      });

      const success = result?.successful !== false;
      console.log(`🔍 Success determination: ${success}`);

      if (!success) {
        console.log(`❌ Operation failed!`);
        console.log(`❌ Error:`, result?.error);
        console.log(`❌ Full result:`, result);

        return {
          success: false,
          error: result?.error || 'Unknown error',
          message: `Failed to execute ${action}: ${result?.error || 'Unknown error occurred'}`,
          finalParams,
          rawResult: result,
        };
      }

      // Format success response
      console.log(`✅ Operation successful! Formatting response...`);

      let formattedMessage = '';
      let resultData = result?.data || result;

      console.log(`📊 Result data:`, resultData);
      console.log(`📊 Action for formatting: ${action}`);

      // Simple success message based on action type
      if (action.includes('CREATE')) {
        formattedMessage = `✅ Successfully created calendar event`;
        if (finalParams.summary) {
          formattedMessage += ` "${finalParams.summary}"`;
        }
        if (finalParams.start_datetime) {
          const startTime = new Date(finalParams.start_datetime);
          formattedMessage += ` for ${startTime.toLocaleDateString()} at ${startTime.toLocaleTimeString()}`;
        }
      } else if (action.includes('UPDATE') || action.includes('PATCH')) {
        formattedMessage = `✅ Successfully updated calendar event`;
        if (finalParams.event_id) {
          formattedMessage += ` (ID: ${finalParams.event_id})`;
        }
      } else if (action.includes('DELETE')) {
        formattedMessage = `✅ Successfully deleted calendar event`;
        if (finalParams.event_id) {
          formattedMessage += ` (ID: ${finalParams.event_id})`;
        }
      } else if (action.includes('FIND')) {
        const events = Array.isArray(resultData?.items) ? resultData.items : [];
        if (events.length === 0) {
          formattedMessage = '📅 No events found for the specified criteria.';
        } else {
          formattedMessage = `📅 Found ${events.length} event${events.length === 1 ? '' : 's'}`;
        }
      } else if (action.includes('LIST_CALENDARS')) {
        const calendars = Array.isArray(resultData?.items) ? resultData.items : [];
        formattedMessage = `📋 Found ${calendars.length} calendar${calendars.length === 1 ? '' : 's'} in your account`;
      } else if (action.includes('FREE_SLOTS')) {
        formattedMessage = `🕐 Found availability information for your calendar`;
      } else {
        formattedMessage = `✅ Successfully completed calendar operation: ${action}`;
      }

      const finalResponse = {
        success: true,
        message: formattedMessage,
        action: action,
        finalParams,
        result: resultData,
      };

      console.log(`🎉 Calendar Agent completed successfully!`);
      console.log(`🎉 Final response:`, JSON.stringify(finalResponse, null, 2));

      return finalResponse;

    } catch (error) {
      console.error('Calendar Agent error:', error);
      return {
        success: false,
        error: 'Calendar agent error',
        message: `An unexpected error occurred while processing your calendar request: ${error instanceof Error ? error.message : 'Unknown error'}`,
        action,
        parameters,
      };
    }
  },
});
