import { tool } from 'ai';
import { z } from 'zod';
import { auth } from '@/app/(auth)/auth';
import { OpenAIToolSet } from 'composio-core';
import { getCurrentTimeForLocation } from '@/lib/utils/date-time';
import { getConnectionsByUserId } from '@/lib/db/queries';

/**
 * Google Calendar Sub-Agent Tool
 *
 * AI-driven calendar assistant that intelligently selects actions and parameters
 * based on natural language input. No hardcoded patterns - full AI flexibility.
 */

// Complete Google Calendar action definitions with all available parameters
const GOOGLE_CALENDAR_ACTIONS = {
  'GOOGLECALENDAR_CREATE_EVENT': {
    description: 'Create a new event in a google calendar',
    required: ['start_datetime'],
    optional: ['attendees', 'calendar_id', 'create_meeting_room', 'description', 'eventType', 'event_duration_hour', 'event_duration_minutes', 'guestsCanInviteOthers', 'guestsCanSeeOtherGuests', 'guests_can_modify', 'location', 'recurrence', 'send_updates', 'summary', 'timezone', 'transparency', 'visibility']
  },
  'GOOGLECALENDAR_DELETE_EVENT': {
    description: 'Delete an event from a google calendar',
    required: ['event_id'],
    optional: ['calendar_id']
  },
  'GOOGLECALENDAR_FIND_EVENT': {
    description: 'Find events in a google calendar based on a search query',
    required: [],
    optional: ['calendar_id', 'event_types', 'max_results', 'order_by', 'page_token', 'query', 'show_deleted', 'single_events', 'timeMax', 'timeMin', 'updated_min']
  },
  'GOOGLECALENDAR_FIND_FREE_SLOTS': {
    description: 'Find free slots in a google calendar for a specific time period',
    required: [],
    optional: ['calendar_expansion_max', 'group_expansion_max', 'items', 'time_max', 'time_min', 'timezone']
  },
  'GOOGLECALENDAR_GET_CALENDAR': {
    description: 'Fetch a calendar based on the provided calendar id',
    required: [],
    optional: ['calendar_id']
  },
  'GOOGLECALENDAR_GET_CURRENT_DATE_TIME': {
    description: 'Get the current date and time of a specified timezone',
    required: [],
    optional: ['timezone']
  },
  'GOOGLECALENDAR_LIST_CALENDARS': {
    description: 'List all google calendars from the user\'s calendar list with pagination',
    required: [],
    optional: ['max_results', 'min_access_role', 'page_token', 'show_deleted', 'show_hidden', 'sync_token']
  },
  'GOOGLECALENDAR_PATCH_EVENT': {
    description: 'Patches an event in google calendar. Only specified fields will be updated',
    required: ['event_id', 'calendar_id'],
    optional: ['attendees', 'conference_data_version', 'description', 'end_time', 'location', 'max_attendees', 'send_updates', 'start_time', 'summary', 'supports_attachments', 'timezone']
  },
  'GOOGLECALENDAR_QUICK_ADD': {
    description: 'Create a new event based on a simple text string (not preferred, use CREATE_EVENT when possible)',
    required: ['text'],
    optional: ['calendar_id', 'send_updates']
  },
  'GOOGLECALENDAR_UPDATE_EVENT': {
    description: 'Update an existing event in a google calendar',
    required: ['event_id', 'start_datetime'],
    optional: ['attendees', 'calendar_id', 'create_meeting_room', 'description', 'eventType', 'event_duration_hour', 'event_duration_minutes', 'guestsCanInviteOthers', 'guestsCanSeeOtherGuests', 'guests_can_modify', 'location', 'recurrence', 'send_updates', 'summary', 'timezone', 'transparency', 'visibility']
  },
  'GOOGLECALENDAR_REMOVE_ATTENDEE': {
    description: 'Remove an attendee from an existing event',
    required: ['attendee_email', 'event_id'],
    optional: ['calendar_id']
  },
  'GOOGLECALENDAR_SYNC_EVENTS': {
    description: 'Get events from last sync using sync token',
    required: [],
    optional: ['calendar_id', 'event_types', 'max_results', 'pageToken', 'single_events', 'sync_token']
  },
  'GOOGLECALENDAR_PATCH_CALENDAR': {
    description: 'Update a google calendar based on calendar id',
    required: ['calendar_id', 'summary'],
    optional: ['description', 'location', 'timezone']
  },
  'GOOGLECALENDAR_DUPLICATE_CALENDAR': {
    description: 'Duplicate a google calendar based on the provided summary',
    required: [],
    optional: ['summary']
  }
};

/**
 * Get current time for parameter inference
 */
async function getCurrentTime(): Promise<{ datetime: string; timezone: string }> {
  try {
    const timeData = await getCurrentTimeForLocation('auto');
    return {
      datetime: timeData.datetime,
      timezone: timeData.timezone_name
    };
  } catch (error) {
    console.error('Error getting current time:', error);
    // Fallback to system time
    const now = new Date();
    return {
      datetime: now.toISOString(),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    };
  }
}

/**
 * Main Google Calendar Sub-Agent Tool
 */
export const googleCalendarAgent = tool({
  description: `Intelligent Google Calendar assistant that handles all calendar operations.

Available Actions:
${Object.entries(GOOGLE_CALENDAR_ACTIONS).map(([action, config]) =>
  `- ${action}: ${config.description}
    Required: ${config.required.length > 0 ? config.required.join(', ') : 'none'}
    Optional: ${config.optional.join(', ')}`
).join('\n')}

You have full flexibility to:
1. Choose the most appropriate action based on the user's request
2. Set any required and optional parameters you deem necessary
3. Infer dates, times, titles, and other details from natural language
4. Use smart defaults (calendar_id: 'primary', reasonable durations, etc.)

Current time context will be provided to help with date/time inference.`,

  parameters: z.object({
    action: z.string().describe('The Google Calendar action to execute (e.g., GOOGLECALENDAR_CREATE_EVENT, GOOGLECALENDAR_FIND_EVENT)'),
    parameters: z.record(z.any()).describe('Parameters for the action. Include all required parameters and any optional ones that are relevant. Use smart defaults and infer from natural language.'),
  }),
  execute: async ({ action, parameters }) => {
    try {
      const session = await auth();

      if (!session || !session.user) {
        return {
          success: false,
          error: 'Authentication required',
          message: 'Please sign in to access your Google Calendar.',
        };
      }

      // Validate action exists
      if (!GOOGLE_CALENDAR_ACTIONS[action as keyof typeof GOOGLE_CALENDAR_ACTIONS]) {
        return {
          success: false,
          error: 'Invalid action',
          message: `Unknown Google Calendar action: ${action}. Available actions: ${Object.keys(GOOGLE_CALENDAR_ACTIONS).join(', ')}`,
        };
      }

      const actionConfig = GOOGLE_CALENDAR_ACTIONS[action as keyof typeof GOOGLE_CALENDAR_ACTIONS];
      console.log(`🎯 Calendar Agent: Executing ${action}`);
      console.log(`📋 Parameters:`, JSON.stringify(parameters, null, 2));

      // Get user's connected services
      const connections = await getConnectionsByUserId(session.user.id);

      if (!connections || connections.length === 0) {
        return {
          success: false,
          error: 'No connected services',
          message: 'You need to connect your Google Calendar before executing calendar actions. Please connect your Google Calendar in the profile settings.',
          redirectUrl: "/profile/connections",
        };
      }

      // Find Google Calendar connection
      const appName = 'googlecalendar';
      const appConnections = connections.filter(conn =>
        conn.provider.toLowerCase() === appName &&
        conn.status === 'ACTIVE'
      );

      if (appConnections.length === 0) {
        return {
          success: false,
          error: 'Google Calendar not connected',
          message: 'Your Google Calendar account is not connected or the connection is inactive. Please connect your Google Calendar in the profile settings.',
          redirectUrl: "/profile/connections",
        };
      }

      // Use the most recently created connection
      const connection = appConnections.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];
      const connectionId = connection.connectionId;

      console.log(`📅 Using Google Calendar connection: ${connectionId}`);

      // Add smart defaults to parameters
      const finalParams = {
        calendar_id: 'primary', // Default calendar
        ...parameters
      };

      // Get current time context for AI reference
      const currentTime = await getCurrentTime();
      console.log(`🕐 Current time context: ${currentTime.datetime} (${currentTime.timezone})`);

      // Validate required parameters
      const missingParams = actionConfig.required.filter(param => !finalParams[param]);

      if (missingParams.length > 0) {
        return {
          success: false,
          error: 'Missing required parameters',
          message: `Missing required parameters for ${action}: ${missingParams.join(', ')}. Please provide these parameters.`,
          requiredParams: actionConfig.required,
          optionalParams: actionConfig.optional,
          currentTime: currentTime.datetime,
          timezone: currentTime.timezone,
        };
      }

      console.log(`✅ All required parameters provided!`);

      // Execute the calendar action
      const toolset = new OpenAIToolSet();
      let result;

      try {
        console.log(`🚀 Executing API call to Composio...`);
        console.log(`🚀 Action: ${action}`);
        console.log(`🚀 App: ${appName}`);
        console.log(`🚀 User ID: ${session.user.id}`);
        console.log(`🚀 Connection ID: ${connectionId}`);
        console.log(`🚀 Input Parameters:`, JSON.stringify(finalParams, null, 2));

        result = await toolset.client.actions.execute({
          actionName: action,
          requestBody: {
            appName: appName,
            input: finalParams,
            entityId: session.user.id,
            connectedAccountId: connectionId
          }
        });

        console.log(`✅ API call successful!`);
        console.log(`✅ Raw result:`, JSON.stringify(result, null, 2));
      } catch (error) {
        const execError = error as Error;
        console.error("❌ Calendar Agent execution error:", execError);

        // Handle specific error types
        if (execError.message && execError.message.includes('ConnectedAccountNotFoundError')) {
          return {
            success: false,
            error: 'Google Calendar connection expired',
            message: 'Your Google Calendar connection appears to have expired or been invalidated. Please reconnect your Google Calendar account and try again.',
            redirectUrl: "/profile/connections",
          };
        }

        return {
          success: false,
          error: 'Calendar operation failed',
          message: `Failed to execute ${action}: ${execError.message}`,
          action,
          parameters: finalParams,
        };
      }

      // Process and format the result
      const success = result?.successful !== false;

      if (!success) {
        return {
          success: false,
          error: result?.error || 'Unknown error',
          message: `Failed to execute ${action}: ${result?.error || 'Unknown error occurred'}`,
          action,
          parameters: finalParams,
          rawResult: result,
        };
      }

      // Format success response
      let resultData = result?.data || result;
      let formattedMessage = `✅ Successfully executed ${action}`;

      // Add specific formatting based on action type
      if (action === 'GOOGLECALENDAR_CREATE_EVENT') {
        if (finalParams.summary) {
          formattedMessage = `✅ Successfully created calendar event "${finalParams.summary}"`;
        }
        if (finalParams.start_datetime) {
          const startTime = new Date(finalParams.start_datetime);
          formattedMessage += ` for ${startTime.toLocaleDateString()} at ${startTime.toLocaleTimeString()}`;
        }
      } else if (action === 'GOOGLECALENDAR_FIND_EVENT') {
        const events = Array.isArray(resultData?.items) ? resultData.items : [];
        if (events.length === 0) {
          formattedMessage = '📅 No events found for the specified criteria.';
        } else {
          formattedMessage = `📅 Found ${events.length} event${events.length === 1 ? '' : 's'}`;
        }
      } else if (action === 'GOOGLECALENDAR_LIST_CALENDARS') {
        const calendars = Array.isArray(resultData?.items) ? resultData.items : [];
        formattedMessage = `📋 Found ${calendars.length} calendar${calendars.length === 1 ? '' : 's'} in your account`;
      }

      return {
        success: true,
        message: formattedMessage,
        action,
        parameters: finalParams,
        result: resultData,
        currentTime: currentTime.datetime,
        timezone: currentTime.timezone,
      };

    } catch (error) {
      console.error('Calendar Agent error:', error);
      return {
        success: false,
        error: 'Calendar agent error',
        message: `An unexpected error occurred while processing your calendar request: ${error instanceof Error ? error.message : 'Unknown error'}`,
        action,
        parameters,
      };
    }
  },
});
            }

            baseDate.setHours(hour, minute, 0, 0);
            // Remove milliseconds for Google Calendar API compatibility
            params.start_datetime = baseDate.toISOString().replace(/\.\d{3}Z$/, 'Z');

            console.log(`🕐 Single time: ${hour}:${minute.toString().padStart(2, '0')} -> ${params.start_datetime}`);
          } else {
            // Default to 9 AM on the target date
            baseDate.setHours(9, 0, 0, 0);
            // Remove milliseconds for Google Calendar API compatibility
            params.start_datetime = baseDate.toISOString().replace(/\.\d{3}Z$/, 'Z');

            console.log(`🕐 Default time (9 AM on ${baseDate.toDateString()}): ${params.start_datetime}`);
          }
        }
      }

      // Set default duration if not provided and no end time specified
      if (!params.event_duration_minutes && !params.event_duration_hour && !params.end_datetime) {
        params.event_duration_hour = 1; // 1 hour default
      }

      break;

    case 'list_events':
    case 'find_events':
      // Time range inference for listing events
      if (!params.timeMin || !params.timeMax) {
        const today = new Date(now);
        today.setHours(0, 0, 0, 0);

        if (taskLower.includes('today')) {
          params.timeMin = today.toISOString();
          const endOfDay = new Date(today);
          endOfDay.setHours(23, 59, 59, 999);
          params.timeMax = endOfDay.toISOString();
        } else if (taskLower.includes('tomorrow')) {
          const tomorrow = new Date(today);
          tomorrow.setDate(tomorrow.getDate() + 1);
          params.timeMin = tomorrow.toISOString();
          const endOfTomorrow = new Date(tomorrow);
          endOfTomorrow.setHours(23, 59, 59, 999);
          params.timeMax = endOfTomorrow.toISOString();
        } else if (taskLower.includes('week')) {
          params.timeMin = today.toISOString();
          const endOfWeek = new Date(today);
          endOfWeek.setDate(endOfWeek.getDate() + 7);
          params.timeMax = endOfWeek.toISOString();
        } else {
          // Default to today
          params.timeMin = today.toISOString();
          const endOfDay = new Date(today);
          endOfDay.setHours(23, 59, 59, 999);
          params.timeMax = endOfDay.toISOString();
        }
      }

      // Set reasonable max results
      if (!params.max_results) {
        params.max_results = 10;
      }

      // Extract search query for find_events
      if (intent === 'find_events' && !params.query) {
        const queryMatch = task.match(/\b(?:find|search|look for|locate)\s+(?:event|meeting|appointment)s?\s+(?:called|titled|named|about|for|with)?\s*["']?([^"']+?)["']?(?:\s+(?:in|on|for|today|tomorrow|this week)|\s*$)/i);
        if (queryMatch) {
          params.query = queryMatch[1].trim();
        }
      }

      break;

    case 'find_free_slots':
      // Time range for free slot finding
      if (!params.time_min || !params.time_max) {
        const startTime = new Date(now);
        startTime.setHours(9, 0, 0, 0); // Start at 9 AM
        params.time_min = startTime.toISOString();

        const endTime = new Date(startTime);
        endTime.setHours(17, 0, 0, 0); // End at 5 PM
        params.time_max = endTime.toISOString();
      }

      if (!params.items) {
        params.items = ['primary'];
      }

      break;

    case 'delete_event':
      // Extract event ID from the task
      if (!params.event_id) {
        console.log(`🗑️ Extracting event ID from task: "${task}"`);

        // Pattern 1: "delete event with id [event_id]"
        let match = task.match(/\b(?:delete|remove|cancel)\s+(?:event|meeting|appointment)\s+(?:with\s+)?(?:id|event_id)\s+([a-zA-Z0-9]+)/i);
        if (match) {
          params.event_id = match[1];
          console.log(`🗑️ Pattern 1 - Found event ID: ${params.event_id}`);
        } else {
          // Pattern 2: "delete [event_id]" or just the ID
          match = task.match(/\b([a-zA-Z0-9]{20,})\b/);
          if (match) {
            params.event_id = match[1];
            console.log(`🗑️ Pattern 2 - Found event ID: ${params.event_id}`);
          } else {
            // Pattern 3: Extract from context or previous conversation
            console.log(`🗑️ No event ID found in task, will need to search by title`);

            // Try to extract event title to search for
            const titleMatch = task.match(/\b(?:delete|remove|cancel)\s+(?:the\s+)?(?:event\s+)?(?:called\s+|titled\s+|named\s+)?["']?([^"']+?)["']?\s*(?:event|today|tomorrow|$)/i);
            if (titleMatch) {
              params.search_title = titleMatch[1].trim();
              console.log(`🗑️ Will search for event with title: "${params.search_title}"`);
            }
          }
        }
      }
      break;

    case 'update_event':
      console.log(`✏️ Processing update_event for task: "${task}"`);

      // Enhanced update type detection
      const hasTimeUpdate = /\b(?:to\s+)?(\d{1,2}(?::\d{2})?(?:\s*(?:pm|am))?)\s*(?:-|to)\s*(\d{1,2}(?::\d{2})?(?:\s*(?:pm|am))?)\b/i.test(task);
      const hasSummaryUpdate = /\b(?:name|title|summary|call it|rename|change.*to)\b/i.test(task);
      const hasDateUpdate = /\b(?:tomorrow|today|monday|tuesday|wednesday|thursday|friday|saturday|sunday|next week)\b/i.test(task);

      console.log(`✏️ Enhanced update type analysis:`, {
        hasTimeUpdate,
        hasSummaryUpdate,
        hasDateUpdate,
        task: task
      });

      // Smart event identification for updates
      if (!params.event_id) {
        console.log(`✏️ Smart event identification for update from task: "${task}"`);

        // Pattern 1: "update event with id [event_id]"
        let match = task.match(/\b(?:update|modify|change|edit)\s+(?:event|meeting|appointment)\s+(?:with\s+)?(?:id|event_id)\s+([a-zA-Z0-9]+)/i);
        if (match) {
          params.event_id = match[1];
          console.log(`✏️ Found explicit event ID: ${params.event_id}`);
        } else {
          // Pattern 2: "update [quoted title]"
          const quotedMatch = task.match(/\b(?:update|modify|change|edit)\s+(?:event\s+)?["']([^"']+)["']/i);
          if (quotedMatch) {
            params.search_title = quotedMatch[1].trim();
            console.log(`✏️ Found quoted event title: "${params.search_title}"`);
          } else {
            // Pattern 3: "update [title] to [something]" - AI-powered extraction
            let titleForSearch = task
              // Remove update action words
              .replace(/^\s*(?:update|modify|change|edit)\s+(?:event\s+|meeting\s+|appointment\s+)?/gi, '')
              // Remove "to" and everything after it (the new values)
              .replace(/\s+(?:to|from|at|on)\s+.+$/gi, '')
              // Remove time patterns
              .replace(/\s+(?:today|tomorrow|monday|tuesday|wednesday|thursday|friday|saturday|sunday|next week).*$/gi, '')
              // Clean up
              .trim();

            if (titleForSearch && titleForSearch.length > 2 && !/^(?:event|meeting|appointment|it|this|that)$/i.test(titleForSearch)) {
              params.search_title = titleForSearch;
              console.log(`✏️ AI extracted event title for search: "${params.search_title}"`);
            } else {
              console.log(`✏️ Could not extract meaningful event title from: "${task}"`);
            }
          }
        }
      }

      // Smart summary extraction for updates
      if (!params.summary && hasSummaryUpdate) {
        console.log(`✏️ Smart summary extraction for update...`);

        // Pattern 1: "update [old_title] to [new_title]"
        let summaryMatch = task.match(/\b(?:update|modify|change|edit|rename)\s+(?:event\s+)?(?:called\s+|titled\s+)?["']?([^"']+?)["']?\s+to\s+["']?([^"']+?)["']?$/i);
        if (summaryMatch) {
          params.search_title = summaryMatch[1].trim();
          params.summary = summaryMatch[2].trim();
          console.log(`✏️ Pattern 1 - Old title: "${params.search_title}", New title: "${params.summary}"`);
        } else {
          // Pattern 2: "rename [title] to [new_title]"
          summaryMatch = task.match(/\b(?:rename|call it|name it|title it)\s+(?:event\s+)?["']?([^"']+?)["']?\s+to\s+["']?([^"']+?)["']?$/i);
          if (summaryMatch) {
            params.search_title = summaryMatch[1].trim();
            params.summary = summaryMatch[2].trim();
            console.log(`✏️ Pattern 2 - Old title: "${params.search_title}", New title: "${params.summary}"`);
          } else {
            // Pattern 3: "change [title] to [new_title]"
            summaryMatch = task.match(/\b(?:change)\s+(?:event\s+)?["']?([^"']+?)["']?\s+to\s+["']?([^"']+?)["']?$/i);
            if (summaryMatch) {
              params.search_title = summaryMatch[1].trim();
              params.summary = summaryMatch[2].trim();
              console.log(`✏️ Pattern 3 - Old title: "${params.search_title}", New title: "${params.summary}"`);
            } else {
              // Pattern 4: AI-powered extraction from "to [new_title]"
              summaryMatch = task.match(/\s+to\s+["']?([^"']+?)["']?(?:\s+(?:today|tomorrow|from|at|on)|\s*$)/i);
              if (summaryMatch) {
                params.summary = summaryMatch[1].trim();
                console.log(`✏️ Pattern 4 - AI extracted new summary: "${params.summary}"`);
              }
            }
          }
        }
      }

      // Smart time and date extraction for updates
      if (!params.start_datetime && hasTimeUpdate) {
        console.log(`✏️ Smart time extraction for update...`);

        // First, determine the target date for the update (same logic as create)
        let baseDate = new Date(now);

        console.log(`📅 Update date inference from task: "${task}"`);

        if (taskLower.includes('tomorrow')) {
          baseDate.setDate(baseDate.getDate() + 1);
          console.log(`📅 Tomorrow detected for update - using date: ${baseDate.toDateString()}`);
        } else if (taskLower.includes('today')) {
          console.log(`📅 Today detected for update - using date: ${baseDate.toDateString()}`);
        } else if (taskLower.includes('next week')) {
          baseDate.setDate(baseDate.getDate() + 7);
          console.log(`📅 Next week detected for update - using date: ${baseDate.toDateString()}`);
        } else {
          // Check for specific day names
          const dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
          const currentDay = baseDate.getDay();

          for (let i = 0; i < dayNames.length; i++) {
            if (taskLower.includes(dayNames[i])) {
              const targetDay = i + 1;
              const adjustedTargetDay = targetDay === 7 ? 0 : targetDay;

              let daysUntilTarget = adjustedTargetDay - currentDay;
              if (daysUntilTarget <= 0) {
                daysUntilTarget += 7;
              }

              baseDate.setDate(baseDate.getDate() + daysUntilTarget);
              console.log(`📅 ${dayNames[i]} detected for update - using date: ${baseDate.toDateString()}`);
              break;
            }
          }
        }

        // Enhanced time range matching (same as create)
        const timeRangeMatch = task.match(/\b(?:to\s+)?(\d{1,2}(?::\d{2})?(?:\s*(?:pm|am))?)\s*(?:-|to)\s*(\d{1,2}(?::\d{2})?(?:\s*(?:pm|am))?)\b/i);
        if (timeRangeMatch) {
          console.log(`✏️ Found time range for update: ${timeRangeMatch[1]} to ${timeRangeMatch[2]}`);

          const startTimeStr = timeRangeMatch[1];
          const endTimeStr = timeRangeMatch[2];

          console.log(`📅 Using base date for update: ${baseDate.toDateString()}`);
          console.log(`⏰ Parsing update time range: "${startTimeStr}" to "${endTimeStr}"`);
        } else {
          console.log(`📅 Using base date for update: ${baseDate.toDateString()}`);
        }

        if (timeRangeMatch) {
          const startTimeStr = timeRangeMatch[1];
          const endTimeStr = timeRangeMatch[2];

          console.log(`⏰ Parsing update time range: "${startTimeStr}" to "${endTimeStr}" on date ${baseDate.toDateString()}`);

          // Parse start time (reuse the same logic as create_event)
          const startParts = startTimeStr.match(/(\d{1,2})(?::(\d{2}))?\s*(am|pm)?/i);
          if (startParts) {
            let startHour = parseInt(startParts[1]);
            const startMinute = startParts[2] ? parseInt(startParts[2]) : 0;

            console.log(`🕐 Update start time parsing: hour=${startHour}, minute=${startMinute}, ampm=${startParts[3]}`);

            if (startParts[3]) {
              const ampm = startParts[3].toLowerCase();
              if (startHour >= 1 && startHour <= 12) {
                if (ampm === 'pm' && startHour !== 12) {
                  startHour += 12;
                  console.log(`🕐 Valid PM conversion: ${startHour - 12} -> ${startHour}`);
                } else if (ampm === 'am' && startHour === 12) {
                  startHour = 0;
                  console.log(`🕐 12 AM conversion: 12 -> 0`);
                } else {
                  console.log(`🕐 Valid ${ampm.toUpperCase()} time: ${startHour}`);
                }
              } else {
                console.log(`🕐 Invalid ${ampm.toUpperCase()} format for hour ${startHour}, treating as 24-hour`);
              }
            } else {
              console.log(`🕐 24-hour format assumed for: ${startHour}`);
            }

            if (startHour < 0 || startHour > 23) {
              console.log(`❌ Invalid start hour: ${startHour}, defaulting to 9`);
              startHour = 9;
            }

            const startTime = new Date(baseDate);
            startTime.setHours(startHour, startMinute, 0, 0);
            params.start_datetime = startTime.toISOString().replace(/\.\d{3}Z$/, 'Z');

            console.log(`🕐 Update start time: ${startHour}:${startMinute.toString().padStart(2, '0')} -> ${params.start_datetime}`);

            // Parse end time for duration calculation
            const endParts = endTimeStr.match(/(\d{1,2})(?::(\d{2}))?\s*(am|pm)?/i);
            if (endParts) {
              let endHour = parseInt(endParts[1]);
              const endMinute = endParts[2] ? parseInt(endParts[2]) : 0;

              console.log(`🕐 Update end time parsing: hour=${endHour}, minute=${endMinute}, ampm=${endParts[3]}`);

              if (endParts[3]) {
                const ampm = endParts[3].toLowerCase();
                if (endHour >= 1 && endHour <= 12) {
                  if (ampm === 'pm' && endHour !== 12) {
                    endHour += 12;
                    console.log(`🕐 Valid PM conversion: ${endHour - 12} -> ${endHour}`);
                  } else if (ampm === 'am' && endHour === 12) {
                    endHour = 0;
                    console.log(`🕐 12 AM conversion: 12 -> 0`);
                  } else {
                    console.log(`🕐 Valid ${ampm.toUpperCase()} time: ${endHour}`);
                  }
                } else {
                  console.log(`🕐 Invalid ${ampm.toUpperCase()} format for hour ${endHour}, treating as 24-hour`);
                }
              } else {
                console.log(`🕐 24-hour format assumed for: ${endHour}`);
              }

              if (endHour < 0 || endHour > 23) {
                console.log(`❌ Invalid end hour: ${endHour}, defaulting to 17`);
                endHour = 17;
              }

              let endTime = new Date(baseDate);
              endTime.setHours(endHour, endMinute, 0, 0);

              // Handle cross-day events
              if (endTime.getTime() <= startTime.getTime()) {
                console.log(`🌙 Cross-day event detected, adding 1 day to end time`);
                endTime.setDate(endTime.getDate() + 1);
              }

              // Calculate duration
              const durationMs = endTime.getTime() - startTime.getTime();
              const durationMinutes = Math.floor(durationMs / (1000 * 60));

              console.log(`⏱️ Update duration: ${durationMinutes} minutes`);

              if (durationMinutes > 0) {
                if (durationMinutes >= 60) {
                  const hours = Math.floor(durationMinutes / 60);
                  const remainingMinutes = durationMinutes % 60;
                  params.event_duration_hour = hours;
                  if (remainingMinutes > 0) {
                    params.event_duration_minutes = remainingMinutes;
                  }
                  console.log(`⏱️ Set update duration: ${hours} hours ${remainingMinutes} minutes`);
                } else {
                  params.event_duration_minutes = durationMinutes;
                  console.log(`⏱️ Set update duration: ${durationMinutes} minutes`);
                }
              }
            }
          }
        }
      }
      break;

    case 'quick_add':
      // For quick add, use the entire task as the text
      if (!params.text) {
        params.text = task;
      }
      break;
  }

  console.log(`🧠 Final inferred parameters:`, JSON.stringify(params, null, 2));
  return params;
}

/**
 * Get app name from action
 */
function getAppNameFromAction(action: string): string {
  return action.toLowerCase().split('_')[0];
}

/**
 * Main Google Calendar Sub-Agent Tool
 */
export const googleCalendarAgent = tool({
  description: 'Intelligent Google Calendar assistant that handles all calendar operations including creating events, listing events, finding free time, managing calendars, and more. Understands natural language and automatically infers parameters.',
  parameters: z.object({
    task: z.string().describe('Describe what you want to do with your calendar in natural language (e.g., "schedule a meeting with John tomorrow at 2pm", "show me my events today", "find free time this afternoon")'),
    params: z.record(z.any()).optional().describe('Optional specific parameters to override automatic inference (e.g., {"calendar_id": "<EMAIL>", "duration": 30})'),
  }),
  execute: async ({ task, params = {} }) => {
    try {
      const session = await auth();

      if (!session || !session.user) {
        return {
          success: false,
          error: 'Authentication required',
          message: 'Please sign in to access your Google Calendar.',
        };
      }

      // Step 1: Detect intent from natural language
      let intent = detectCalendarIntent(task);
      let actionConfig = CALENDAR_ACTIONS[intent];

      console.log(`🎯 Calendar Agent: Detected intent "${intent}" for task: "${task}"`);

      if (!actionConfig) {
        return {
          success: false,
          error: 'Unknown calendar operation',
          message: `I couldn't understand what you want to do with your calendar. Please try rephrasing your request.`,
          detectedIntent: intent,
        };
      }

      // Step 2: Get user's connected services
      const connections = await getConnectionsByUserId(session.user.id);

      if (!connections || connections.length === 0) {
        return {
          success: false,
          error: 'No connected services',
          message: 'You need to connect at least one service before executing calendar actions. Please connect your Google Calendar in the profile settings.',
          redirectUrl: "/profile/connections",
          detectedIntent: intent,
        };
      }

      // Step 3: Find Google Calendar connection
      const appName = getAppNameFromAction(actionConfig.action);
      const appConnections = connections.filter(conn =>
        conn.provider.toLowerCase() === appName &&
        conn.status === 'ACTIVE'
      );

      if (appConnections.length === 0) {
        return {
          success: false,
          error: 'Google Calendar not connected',
          message: 'Your Google Calendar account is not connected or the connection is inactive. Please connect your Google Calendar in the profile settings.',
          redirectUrl: "/profile/connections",
          detectedIntent: intent,
        };
      }

      // Use the most recently created connection
      const connection = appConnections.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];
      const connectionId = connection.connectionId;

      console.log(`📅 Using Google Calendar connection: ${connectionId}`);

      // Step 4: Infer parameters from natural language and context
      const inferredParams = await inferCalendarParameters(task, intent, params);

      console.log(`🧠 Calendar Agent: Inferred parameters for "${intent}":`, JSON.stringify(inferredParams, null, 2));

      // Step 4.5: Smart action selection for updates
      if (intent === 'update_event') {
        const hasTimeFields = !!(inferredParams.start_datetime || inferredParams.end_datetime || inferredParams.event_duration_hour || inferredParams.event_duration_minutes);
        const hasOnlyMetadataFields = !!(inferredParams.summary || inferredParams.description || inferredParams.location) && !hasTimeFields;

        console.log(`🔄 Update action selection:`, {
          hasTimeFields,
          hasOnlyMetadataFields,
          availableFields: Object.keys(inferredParams).filter(key => key !== 'calendar_id')
        });

        if (hasOnlyMetadataFields) {
          console.log(`🔄 Switching to PATCH_EVENT for metadata-only update`);
          intent = 'patch_event';
          actionConfig = CALENDAR_ACTIONS[intent];
        }
      }

      // Step 5: Validate required parameters
      console.log(`🔍 Validating required parameters...`);
      console.log(`🔍 Required params for ${intent}:`, actionConfig.requiredParams);
      console.log(`🔍 Current params:`, Object.keys(inferredParams));

      const missingParams = actionConfig.requiredParams.filter(param => {
        const hasParam = !!inferredParams[param];
        console.log(`🔍 Parameter "${param}": ${hasParam ? '✅ PROVIDED' : '❌ MISSING'}`);
        return !hasParam;
      });

      if (missingParams.length > 0) {
        console.log(`❌ Missing required parameters: ${missingParams.join(', ')}`);

        // Special handling for delete_event - try to find event by title if no ID
        if (intent === 'delete_event' && missingParams.includes('event_id') && inferredParams.search_title) {
          console.log(`🔍 Attempting to find event by title: "${inferredParams.search_title}"`);
          return {
            success: false,
            error: 'Event ID required for deletion',
            message: `To delete an event, I need the event ID. Please provide the event ID or use a more specific command like "delete event with id [event_id]".`,
            detectedIntent: intent,
            missingParams,
            inferredParams,
            suggestion: `Try: "delete event with id [event_id]" or first list events to find the ID.`
          };
        }

        // Special handling for update_event and patch_event - try to find event by title if no ID
        if ((intent === 'update_event' || intent === 'patch_event') && missingParams.includes('event_id')) {
          console.log(`🔍 ${intent} missing event_id`);
          console.log(`🔍 Available params:`, Object.keys(inferredParams));
          console.log(`🔍 Search title:`, inferredParams.search_title);
          console.log(`🔍 Summary:`, inferredParams.summary);

          if (inferredParams.search_title) {
            console.log(`🔍 Attempting to find event by title for ${intent}: "${inferredParams.search_title}"`);
            return {
              success: false,
              error: 'Event ID required for update',
              message: `To update an event, I need the event ID. I found you want to update an event with title "${inferredParams.search_title}", but I need the specific event ID.`,
              detectedIntent: intent,
              missingParams,
              inferredParams,
              suggestion: `Try: "update event with id [event_id] to [new_time]" or first list events to find the ID.`
            };
          } else {
            console.log(`🔍 No search title found, generic event ID error`);
            return {
              success: false,
              error: 'Event ID required for update',
              message: `To update an event, I need the event ID. Please specify which event you want to update.`,
              detectedIntent: intent,
              missingParams,
              inferredParams,
              suggestion: `Try: "update event with id [event_id]" or first list events to find the ID.`
            };
          }
        }

        return {
          success: false,
          error: 'Missing required parameters',
          message: `I need more information to ${actionConfig.description.toLowerCase()}. Missing: ${missingParams.join(', ')}`,
          detectedIntent: intent,
          inferredParams,
          missingParams,
        };
      }

      console.log(`✅ All required parameters provided!`);

      // Step 6: Execute the calendar action
      const toolset = new OpenAIToolSet();

      console.log(`🗓️ Calendar Agent: Executing ${actionConfig.action} with intent "${intent}"`);
      console.log(`📋 Inferred parameters:`, inferredParams);
      console.log(`🔗 Using connection ID: ${connectionId}`);

      let result;
      try {
        console.log(`🚀 Executing API call to Composio...`);
        console.log(`🚀 Action: ${actionConfig.action}`);
        console.log(`🚀 App: ${appName}`);
        console.log(`🚀 Entity ID: ${session.user.id}`);
        console.log(`🚀 Connection ID: ${connectionId}`);
        console.log(`🚀 Input Parameters:`, JSON.stringify(inferredParams, null, 2));

        result = await toolset.client.actions.execute({
          actionName: actionConfig.action,
          requestBody: {
            appName: appName,
            input: inferredParams,
            entityId: session.user.id,
            connectedAccountId: connectionId
          }
        });

        console.log(`✅ API call successful!`);
        console.log(`✅ Raw result:`, JSON.stringify(result, null, 2));
      } catch (error) {
        const execError = error as Error;
        console.error("❌ Calendar Agent execution error:", execError);
        console.error("❌ Error details:", {
          message: execError.message,
          stack: execError.stack,
          name: execError.name
        });

        // Handle specific error types
        if (execError.message && execError.message.includes('ConnectedAccountNotFoundError')) {
          return {
            success: false,
            error: 'Google Calendar connection expired',
            message: 'Your Google Calendar connection appears to have expired or been invalidated. Please reconnect your Google Calendar account and try again.',
            redirectUrl: "/profile/connections",
            detectedIntent: intent,
          };
        }

        if (execError.message && execError.message.includes('NotFoundError')) {
          return {
            success: false,
            error: 'Calendar action not found',
            message: `The calendar operation "${actionConfig.action}" could not be found. This might be a temporary issue.`,
            detectedIntent: intent,
          };
        }

        return {
          success: false,
          error: 'Calendar operation failed',
          message: `Failed to ${actionConfig.description.toLowerCase()}: ${execError.message}`,
          detectedIntent: intent,
          inferredParams,
        };
      }

      // Step 7: Process and format the result
      console.log(`🔍 Processing result...`);
      console.log(`🔍 Result structure:`, {
        hasResult: !!result,
        successful: result?.successful,
        hasData: !!result?.data,
        hasError: !!result?.error,
        keys: result ? Object.keys(result) : []
      });

      const success = result?.successful !== false;
      console.log(`🔍 Success determination: ${success}`);

      if (!success) {
        console.log(`❌ Operation failed!`);
        console.log(`❌ Error:`, result?.error);
        console.log(`❌ Full result:`, result);

        return {
          success: false,
          error: result?.error || 'Unknown error',
          message: `Failed to ${actionConfig.description.toLowerCase()}: ${result?.error || 'Unknown error occurred'}`,
          detectedIntent: intent,
          inferredParams,
          rawResult: result,
        };
      }

      // Step 8: Format success response based on intent
      console.log(`✅ Operation successful! Formatting response...`);

      let formattedMessage = '';
      let resultData = result?.data || result;

      console.log(`📊 Result data:`, resultData);
      console.log(`📊 Intent for formatting: ${intent}`);

      switch (intent) {
        case 'create_event':
          console.log(`📅 Formatting create_event response...`);
          console.log(`📅 Event summary: ${inferredParams.summary}`);
          console.log(`📅 Start datetime: ${inferredParams.start_datetime}`);

          formattedMessage = `✅ Successfully created calendar event "${inferredParams.summary}"`;
          if (inferredParams.start_datetime) {
            const startTime = new Date(inferredParams.start_datetime);
            console.log(`📅 Parsed start time:`, {
              original: inferredParams.start_datetime,
              parsed: startTime.toString(),
              localDate: startTime.toLocaleDateString(),
              localTime: startTime.toLocaleTimeString()
            });
            formattedMessage += ` for ${startTime.toLocaleDateString()} at ${startTime.toLocaleTimeString()}`;
          }

          console.log(`📅 Final formatted message: ${formattedMessage}`);
          break;

        case 'list_events':
        case 'find_events':
          const events = resultData?.items || [];
          if (events.length === 0) {
            formattedMessage = '📅 No events found for the specified time period.';
          } else {
            formattedMessage = `📅 Found ${events.length} event${events.length === 1 ? '' : 's'}`;
            if (inferredParams.timeMin && inferredParams.timeMax) {
              const startDate = new Date(inferredParams.timeMin);
              const endDate = new Date(inferredParams.timeMax);
              if (startDate.toDateString() === endDate.toDateString()) {
                formattedMessage += ` for ${startDate.toLocaleDateString()}`;
              } else {
                formattedMessage += ` from ${startDate.toLocaleDateString()} to ${endDate.toLocaleDateString()}`;
              }
            }
          }
          break;

        case 'update_event':
        case 'patch_event':
          console.log(`✏️ Formatting ${intent} response...`);
          console.log(`✏️ Event ID: ${inferredParams.event_id}`);
          console.log(`✏️ New summary: ${inferredParams.summary}`);
          console.log(`✏️ New start time: ${inferredParams.start_datetime}`);
          console.log(`✏️ Duration: ${inferredParams.event_duration_hour}h ${inferredParams.event_duration_minutes}m`);
          console.log(`✏️ Result data:`, resultData);

          formattedMessage = `✅ Successfully updated calendar event`;
          if (inferredParams.event_id) {
            formattedMessage += ` (ID: ${inferredParams.event_id})`;
          }
          if (inferredParams.summary) {
            formattedMessage += ` with title "${inferredParams.summary}"`;
          }
          if (inferredParams.start_datetime) {
            const startTime = new Date(inferredParams.start_datetime);
            formattedMessage += ` to ${startTime.toLocaleDateString()} at ${startTime.toLocaleTimeString()}`;
          }

          console.log(`✏️ Final formatted message: ${formattedMessage}`);
          break;

        case 'delete_event':
          console.log(`🗑️ Formatting delete_event response...`);
          console.log(`🗑️ Event ID: ${inferredParams.event_id}`);
          console.log(`🗑️ Result data:`, resultData);

          formattedMessage = `✅ Successfully deleted calendar event`;
          if (inferredParams.event_id) {
            formattedMessage += ` (ID: ${inferredParams.event_id})`;
          }

          console.log(`🗑️ Final formatted message: ${formattedMessage}`);
          break;

        case 'find_free_slots':
          const freeSlots = resultData?.freeBusy || resultData?.calendars || [];
          formattedMessage = `🕐 Found availability information for your calendar`;
          break;

        case 'list_calendars':
          const calendars = resultData?.items || [];
          formattedMessage = `📋 Found ${calendars.length} calendar${calendars.length === 1 ? '' : 's'} in your account`;
          break;

        default:
          formattedMessage = `✅ Successfully completed calendar operation: ${actionConfig.description}`;
      }

      const finalResponse = {
        success: true,
        message: formattedMessage,
        action: actionConfig.action,
        detectedIntent: intent,
        inferredParams,
        result: resultData,
        // Include original task for context
        originalTask: task,
      };

      console.log(`🎉 Calendar Agent completed successfully!`);
      console.log(`🎉 Final response:`, JSON.stringify(finalResponse, null, 2));

      return finalResponse;

    } catch (error) {
      console.error('Calendar Agent error:', error);
      return {
        success: false,
        error: 'Calendar agent error',
        message: `An unexpected error occurred while processing your calendar request: ${error instanceof Error ? error.message : 'Unknown error'}`,
        originalTask: task,
      };
    }
  },
});
