import { tool, generateObject } from 'ai';
import { z } from 'zod';
import { auth } from '@/app/(auth)/auth';
import { OpenAIToolSet } from 'composio-core';
import { getConnectionsByUserId } from '@/lib/db/queries';
import { myProvider } from '@/lib/ai/providers';

/**
 * Google Calendar Sub-Agent Tool
 *
 * A specialized sub-agent that handles all Google Calendar operations with AI-driven
 * intent detection and parameter inference. Replaces regex patterns with intelligent
 * natural language processing.
 */

interface CalendarActionMapping {
  [key: string]: {
    action: string;
    description: string;
    requiredParams: string[];
    optionalParams: string[];
  };
}

// Complete mapping based on official Google Calendar API documentation
const CALENDAR_ACTIONS: CalendarActionMapping = {
  'create_event': {
    action: 'GOOGLECALENDAR_CREATE_EVENT',
    description: 'Create a new calendar event',
    requiredParams: ['start_datetime'],
    optionalParams: [
      'attendees', 'calendar_id', 'create_meeting_room', 'description', 'eventType',
      'event_duration_hour', 'event_duration_minutes', 'guestsCanInviteOthers',
      'guestsCanSeeOtherGuests', 'guests_can_modify', 'location', 'recurrence',
      'send_updates', 'summary', 'timezone', 'transparency', 'visibility'
    ]
  },
  'list_events': {
    action: 'GOOGLECALENDAR_FIND_EVENT',
    description: 'Find and list calendar events',
    requiredParams: [],
    optionalParams: [
      'calendar_id', 'event_types', 'max_results', 'order_by', 'page_token',
      'query', 'show_deleted', 'single_events', 'timeMax', 'timeMin', 'updated_min'
    ]
  },
  'find_events': {
    action: 'GOOGLECALENDAR_FIND_EVENT',
    description: 'Search for specific calendar events',
    requiredParams: [],
    optionalParams: [
      'calendar_id', 'event_types', 'max_results', 'order_by', 'page_token',
      'query', 'show_deleted', 'single_events', 'timeMax', 'timeMin', 'updated_min'
    ]
  },
  'get_calendar': {
    action: 'GOOGLECALENDAR_GET_CALENDAR',
    description: 'Get calendar information',
    requiredParams: [],
    optionalParams: ['calendar_id']
  },
  'list_calendars': {
    action: 'GOOGLECALENDAR_LIST_CALENDARS',
    description: 'List all available calendars',
    requiredParams: [],
    optionalParams: [
      'max_results', 'min_access_role', 'page_token', 'show_deleted', 'show_hidden', 'sync_token'
    ]
  },
  'update_event': {
    action: 'GOOGLECALENDAR_UPDATE_EVENT',
    description: 'Update an existing calendar event',
    requiredParams: ['event_id', 'start_datetime'],
    optionalParams: [
      'attendees', 'calendar_id', 'create_meeting_room', 'description', 'eventType',
      'event_duration_hour', 'event_duration_minutes', 'guestsCanInviteOthers',
      'guestsCanSeeOtherGuests', 'guests_can_modify', 'location', 'recurrence',
      'send_updates', 'summary', 'timezone', 'transparency', 'visibility'
    ]
  },
  'patch_event': {
    action: 'GOOGLECALENDAR_PATCH_EVENT',
    description: 'Patch specific fields of an existing calendar event',
    requiredParams: ['event_id', 'calendar_id'],
    optionalParams: [
      'attendees', 'conference_data_version', 'description', 'end_time',
      'location', 'max_attendees', 'send_updates', 'start_time', 'summary',
      'supports_attachments', 'timezone'
    ]
  },
  'delete_event': {
    action: 'GOOGLECALENDAR_DELETE_EVENT',
    description: 'Delete a calendar event',
    requiredParams: ['event_id'],
    optionalParams: ['calendar_id']
  },
  'find_free_slots': {
    action: 'GOOGLECALENDAR_FIND_FREE_SLOTS',
    description: 'Find available time slots',
    requiredParams: [],
    optionalParams: [
      'calendar_expansion_max', 'group_expansion_max', 'items', 'time_max', 'time_min', 'timezone'
    ]
  },
  'quick_add': {
    action: 'GOOGLECALENDAR_QUICK_ADD',
    description: 'Quick add event from natural language',
    requiredParams: ['text'],
    optionalParams: ['calendar_id', 'send_updates']
  },
  'get_current_time': {
    action: 'GOOGLECALENDAR_GET_CURRENT_DATE_TIME',
    description: 'Get current date and time for timezone',
    requiredParams: [],
    optionalParams: ['timezone']
  },
  'duplicate_calendar': {
    action: 'GOOGLECALENDAR_DUPLICATE_CALENDAR',
    description: 'Duplicate a calendar',
    requiredParams: [],
    optionalParams: ['summary']
  },
  'patch_calendar': {
    action: 'GOOGLECALENDAR_PATCH_CALENDAR',
    description: 'Update calendar properties',
    requiredParams: ['calendar_id', 'summary'],
    optionalParams: ['description', 'location', 'timezone']
  },
  'remove_attendee': {
    action: 'GOOGLECALENDAR_REMOVE_ATTENDEE',
    description: 'Remove an attendee from an event',
    requiredParams: ['attendee_email', 'event_id'],
    optionalParams: ['calendar_id']
  },
  'sync_events': {
    action: 'GOOGLECALENDAR_SYNC_EVENTS',
    description: 'Sync events from last sync token',
    requiredParams: [],
    optionalParams: [
      'calendar_id', 'event_types', 'max_results', 'pageToken', 'single_events', 'sync_token'
    ]
  }
};

/**
 * AI-driven intent detection from natural language
 * Replaces regex patterns with intelligent AI classification
 */
async function detectCalendarIntent(task: string): Promise<string> {
  console.log(`🤖 AI Intent Detection - Input: "${task}"`);

  try {
    const availableIntents = Object.keys(CALENDAR_ACTIONS);
    const intentDescriptions = availableIntents.map(intent =>
      `${intent}: ${CALENDAR_ACTIONS[intent].description}`
    ).join('\n');

    const { object: intentResult } = await generateObject({
      model: myProvider.languageModel('chat-model'),
      schema: z.object({
        intent: z.enum(availableIntents as [string, ...string[]]).describe('The detected calendar intent'),
        confidence: z.number().min(0).max(1).describe('Confidence level (0-1)'),
        reasoning: z.string().describe('Brief explanation of why this intent was chosen')
      }),
      prompt: `Analyze this calendar-related request and determine the most appropriate intent:

REQUEST: "${task}"

AVAILABLE INTENTS:
${intentDescriptions}

Consider:
- The primary action the user wants to perform
- Keywords and context clues
- Natural language variations and synonyms
- Implicit vs explicit requests

Choose the most appropriate intent and provide your confidence level and reasoning.`,
    });

    console.log(`🤖 AI Intent Detection Result:`, {
      intent: intentResult.intent,
      confidence: intentResult.confidence,
      reasoning: intentResult.reasoning
    });

    // Use AI result if confidence is reasonable, otherwise fallback
    if (intentResult.confidence >= 0.6) {
      return intentResult.intent;
    } else {
      console.log(`⚠️ Low confidence (${intentResult.confidence}), using fallback logic`);
      return 'list_events'; // Safe fallback
    }

  } catch (error) {
    console.error('❌ AI Intent Detection failed:', error);

    // Simple fallback logic for critical cases
    const taskLower = task.toLowerCase();
    if (taskLower.includes('create') || taskLower.includes('schedule') || taskLower.includes('add')) {
      return 'create_event';
    } else if (taskLower.includes('update') || taskLower.includes('modify') || taskLower.includes('change')) {
      return 'update_event';
    } else if (taskLower.includes('delete') || taskLower.includes('remove') || taskLower.includes('cancel')) {
      return 'delete_event';
    } else if (taskLower.includes('free') || taskLower.includes('available')) {
      return 'find_free_slots';
    } else if (taskLower.includes('calendars')) {
      return 'list_calendars';
    } else {
      return 'list_events';
    }
  }
}

/**
 * Smart default parameter generation - no AI guessing, just reliable defaults
 * Gets real calendar data first, then lets AI format the results
 */
function getSmartDefaultParameters(task: string, intent: string, providedParams: any = {}): any {
  console.log(`🎯 Smart Defaults - Intent: ${intent}, Task: "${task}"`);
  console.log(`🎯 Provided params:`, JSON.stringify(providedParams, null, 2));

  const params = { ...providedParams };
  const taskLower = task.toLowerCase();
  const now = new Date();

  // Set universal defaults
  if (!params.calendar_id) {
    params.calendar_id = 'primary';
  }

  // Intent-specific smart defaults
  switch (intent) {
    case 'create_event':
      // Extract basic title if not provided
      if (!params.summary) {
        const titleMatch = task.match(/(?:create|schedule|add|book)\s+(?:a\s+)?(?:meeting|event|appointment)?\s*(?:called|titled|named)?\s*["']?([^"']+?)["']?(?:\s+(?:for|at|on|from|tomorrow|today)|\s*$)/i);
        params.summary = titleMatch ? titleMatch[1].trim() : 'New Event';
      }

      // Smart date defaults
      if (!params.start_datetime) {
        const baseDate = new Date(now);
        if (taskLower.includes('tomorrow')) {
          baseDate.setDate(baseDate.getDate() + 1);
        }
        baseDate.setHours(9, 0, 0, 0); // 9 AM default
        params.start_datetime = baseDate.toISOString();
      }

      // Default duration
      if (!params.event_duration_minutes && !params.event_duration_hour) {
        params.event_duration_minutes = 60; // 1 hour default
      }
      break;

    case 'list_events':
    case 'find_events':
      // Smart time range defaults - get real data first approach
      if (!params.timeMin || !params.timeMax) {
        const today = new Date(now);

        if (taskLower.includes('tomorrow')) {
          today.setDate(today.getDate() + 1);
        } else if (taskLower.includes('yesterday')) {
          today.setDate(today.getDate() - 1);
        } else if (taskLower.includes('week')) {
          // This week - start from Monday
          const dayOfWeek = today.getDay();
          const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
          today.setDate(today.getDate() + daysToMonday);
        }

        today.setHours(0, 0, 0, 0);
        params.timeMin = today.toISOString();

        const endDate = new Date(today);
        if (taskLower.includes('week')) {
          endDate.setDate(endDate.getDate() + 6); // End of week
        }
        endDate.setHours(23, 59, 59, 999);
        params.timeMax = endDate.toISOString();
      }

      // Reasonable result limit
      if (!params.max_results) {
        params.max_results = 20; // Get more data, let AI format it nicely
      }
      break;

    case 'update_event':
    case 'patch_event':
      // Extract event identifier
      if (!params.event_id && !params.search_title) {
        const titleMatch = task.match(/(?:update|modify|change)\s+(?:event\s+)?["']?([^"']+?)["']?(?:\s+to|\s*$)/i);
        if (titleMatch) {
          params.search_title = titleMatch[1].trim();
        }
      }
      break;

    case 'delete_event':
      // Extract event identifier
      if (!params.event_id && !params.search_title) {
        const titleMatch = task.match(/(?:delete|remove|cancel)\s+(?:event\s+)?["']?([^"']+?)["']?(?:\s*$)/i);
        if (titleMatch) {
          params.search_title = titleMatch[1].trim();
        }
      }
      break;

    case 'find_free_slots':
      // Default time range for free/busy
      if (!params.time_min || !params.time_max) {
        const startTime = new Date(now);
        startTime.setHours(9, 0, 0, 0); // 9 AM
        params.time_min = startTime.toISOString();

        const endTime = new Date(startTime);
        endTime.setHours(17, 0, 0, 0); // 5 PM
        params.time_max = endTime.toISOString();
      }

      if (!params.items) {
        params.items = ['primary'];
      }
      break;

    case 'quick_add':
      // For quick add, use the entire task as text
      if (!params.text) {
        params.text = task;
      }
      break;
  }

  console.log(`🎯 Smart defaults generated:`, JSON.stringify(params, null, 2));
  return params;
}

/**
 * AI-powered response formatting using real calendar data
 * This replaces hardcoded response logic with intelligent formatting
 */
async function formatCalendarResponse(task: string, intent: string, calendarData: any, params: any): Promise<string> {
  try {
    console.log(`🤖 AI Response Formatting - Intent: ${intent}, Task: "${task}"`);

    const { object: responseResult } = await generateObject({
      model: myProvider.languageModel('chat-model'),
      schema: z.object({
        message: z.string().describe('User-friendly response message'),
        reasoning: z.string().describe('Brief explanation of how the response was formatted')
      }),
      prompt: `Format this calendar data into a user-friendly response:

USER REQUEST: "${task}"
INTENT: ${intent}
PARAMETERS USED: ${JSON.stringify(params, null, 2)}

REAL CALENDAR DATA:
${JSON.stringify(calendarData, null, 2)}

FORMATTING GUIDELINES:

1. **For list_events/find_events:**
   - If no events: "📅 No events found for [time period]"
   - If events found: "📅 Found X events for [time period]:" followed by a brief summary
   - Include event titles, dates, and times in a readable format
   - Use emojis appropriately (📅 🕐 📍 👥)

2. **For create_event:**
   - "✅ Successfully created '[event title]' for [date] at [time]"
   - Include duration if specified

3. **For update_event/patch_event:**
   - "✅ Successfully updated '[event title]' to [new details]"

4. **For delete_event:**
   - "✅ Successfully deleted '[event title]'"

5. **For other operations:**
   - Provide clear success confirmation with relevant details

6. **General rules:**
   - Be conversational and friendly
   - Include relevant details from the actual data
   - Use appropriate emojis
   - Keep it concise but informative
   - If the data shows events, mention them specifically
   - Format dates and times in a human-readable way

Create a response that accurately reflects the real calendar data provided.`,
    });

    console.log(`🤖 AI Response Formatting Result:`, {
      message: responseResult.message,
      reasoning: responseResult.reasoning
    });

    return responseResult.message;

  } catch (error) {
    console.error('❌ AI Response Formatting failed:', error);

    // Fallback to simple formatting
    console.log('🔄 Using fallback response formatting...');

    switch (intent) {
      case 'list_events':
      case 'find_events':
        const events = Array.isArray(calendarData?.event_data?.event_data) ? calendarData.event_data.event_data :
                      Array.isArray(calendarData?.items) ? calendarData.items : [];

        if (events.length === 0) {
          return '📅 No events found for the specified time period.';
        } else {
          return `📅 Found ${events.length} event${events.length === 1 ? '' : 's'} in your calendar.`;
        }

      case 'create_event':
        return `✅ Successfully created calendar event "${params.summary || 'New Event'}"`;

      case 'update_event':
      case 'patch_event':
        return `✅ Successfully updated calendar event`;

      case 'delete_event':
        return `✅ Successfully deleted calendar event`;

      default:
        return `✅ Calendar operation completed successfully`;
    }
  }
}

/**
 * Get app name from action
 */
function getAppNameFromAction(action: string): string {
  return action.toLowerCase().split('_')[0];
}

/**
 * Main Google Calendar Sub-Agent Tool
 */
export const googleCalendarAgent = tool({
  description: 'Intelligent Google Calendar assistant that handles all calendar operations including creating events, listing events, finding free time, managing calendars, and more. Understands natural language and automatically infers parameters.',
  parameters: z.object({
    task: z.string().describe('Describe what you want to do with your calendar in natural language (e.g., "schedule a meeting with John tomorrow at 2pm", "show me my events today", "find free time this afternoon")'),
    params: z.record(z.any()).optional().describe('Optional specific parameters to override automatic inference (e.g., {"calendar_id": "<EMAIL>", "duration": 30})'),
  }),
  execute: async ({ task, params = {} }) => {
    try {
      const session = await auth();

      if (!session || !session.user) {
        return {
          success: false,
          error: 'Authentication required',
          message: 'Please sign in to access your Google Calendar.',
        };
      }

      // Step 1: Detect intent from natural language
      let intent = await detectCalendarIntent(task);
      let actionConfig = CALENDAR_ACTIONS[intent];

      console.log(`🎯 Calendar Agent: Detected intent "${intent}" for task: "${task}"`);

      if (!actionConfig) {
        return {
          success: false,
          error: 'Unknown calendar operation',
          message: `I couldn't understand what you want to do with your calendar. Please try rephrasing your request.`,
          detectedIntent: intent,
        };
      }

      // Step 2: Get user's connected services
      const connections = await getConnectionsByUserId(session.user?.id || '');

      if (!connections || connections.length === 0) {
        return {
          success: false,
          error: 'No connected services',
          message: 'You need to connect at least one service before executing calendar actions. Please connect your Google Calendar in the profile settings.',
          redirectUrl: "/profile/connections",
          detectedIntent: intent,
        };
      }

      // Step 3: Find Google Calendar connection
      const appName = getAppNameFromAction(actionConfig.action);
      const appConnections = connections.filter(conn =>
        conn.provider.toLowerCase() === appName &&
        conn.status === 'ACTIVE'
      );

      if (appConnections.length === 0) {
        return {
          success: false,
          error: 'Google Calendar not connected',
          message: 'Your Google Calendar account is not connected or the connection is inactive. Please connect your Google Calendar in the profile settings.',
          redirectUrl: "/profile/connections",
          detectedIntent: intent,
        };
      }

      // Use the most recently created connection
      const connection = appConnections.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];
      const connectionId = connection.connectionId;

      console.log(`📅 Using Google Calendar connection: ${connectionId}`);

      // Step 4: Get smart default parameters (no AI guessing needed)
      const inferredParams = getSmartDefaultParameters(task, intent, params);

      console.log(`🧠 Calendar Agent: Using smart defaults for "${intent}":`, JSON.stringify(inferredParams, null, 2));

      // Step 4.5: Smart action selection for updates
      if (intent === 'update_event') {
        const hasTimeFields = !!(inferredParams.start_datetime || inferredParams.end_datetime || inferredParams.event_duration_hour || inferredParams.event_duration_minutes);
        const hasOnlyMetadataFields = !!(inferredParams.summary || inferredParams.description || inferredParams.location) && !hasTimeFields;

        console.log(`🔄 Update action selection:`, {
          hasTimeFields,
          hasOnlyMetadataFields,
          availableFields: Object.keys(inferredParams).filter(key => key !== 'calendar_id')
        });

        if (hasOnlyMetadataFields) {
          console.log(`🔄 Switching to PATCH_EVENT for metadata-only update`);
          intent = 'patch_event';
          actionConfig = CALENDAR_ACTIONS[intent];
        }
      }

      // Step 5: Validate required parameters
      console.log(`🔍 Validating required parameters...`);
      console.log(`🔍 Required params for ${intent}:`, actionConfig.requiredParams);
      console.log(`🔍 Current params:`, Object.keys(inferredParams));

      const missingParams = actionConfig.requiredParams.filter(param => {
        const hasParam = !!inferredParams[param];
        console.log(`🔍 Parameter "${param}": ${hasParam ? '✅ PROVIDED' : '❌ MISSING'}`);
        return !hasParam;
      });

      if (missingParams.length > 0) {
        console.log(`❌ Missing required parameters: ${missingParams.join(', ')}`);

        // Special handling for delete_event - try to find event by title if no ID
        if (intent === 'delete_event' && missingParams.includes('event_id') && inferredParams.search_title) {
          console.log(`🔍 Attempting to find event by title: "${inferredParams.search_title}"`);
          return {
            success: false,
            error: 'Event ID required for deletion',
            message: `To delete an event, I need the event ID. Please provide the event ID or use a more specific command like "delete event with id [event_id]".`,
            detectedIntent: intent,
            missingParams,
            inferredParams,
            suggestion: `Try: "delete event with id [event_id]" or first list events to find the ID.`
          };
        }

        // Special handling for update_event and patch_event - try to find event by title if no ID
        if ((intent === 'update_event' || intent === 'patch_event') && missingParams.includes('event_id')) {
          console.log(`🔍 ${intent} missing event_id`);
          console.log(`🔍 Available params:`, Object.keys(inferredParams));
          console.log(`🔍 Search title:`, inferredParams.search_title);
          console.log(`🔍 Summary:`, inferredParams.summary);

          if (inferredParams.search_title) {
            console.log(`🔍 Attempting to find event by title for ${intent}: "${inferredParams.search_title}"`);
            return {
              success: false,
              error: 'Event ID required for update',
              message: `To update an event, I need the event ID. I found you want to update an event with title "${inferredParams.search_title}", but I need the specific event ID.`,
              detectedIntent: intent,
              missingParams,
              inferredParams,
              suggestion: `Try: "update event with id [event_id] to [new_time]" or first list events to find the ID.`
            };
          } else {
            console.log(`🔍 No search title found, generic event ID error`);
            return {
              success: false,
              error: 'Event ID required for update',
              message: `To update an event, I need the event ID. Please specify which event you want to update.`,
              detectedIntent: intent,
              missingParams,
              inferredParams,
              suggestion: `Try: "update event with id [event_id]" or first list events to find the ID.`
            };
          }
        }

        return {
          success: false,
          error: 'Missing required parameters',
          message: `I need more information to ${actionConfig.description.toLowerCase()}. Missing: ${missingParams.join(', ')}`,
          detectedIntent: intent,
          inferredParams,
          missingParams,
        };
      }

      console.log(`✅ All required parameters provided!`);

      // Step 6: Execute the calendar action
      const toolset = new OpenAIToolSet();

      console.log(`🗓️ Calendar Agent: Executing ${actionConfig.action} with intent "${intent}"`);
      console.log(`📋 Inferred parameters:`, inferredParams);
      console.log(`🔗 Using connection ID: ${connectionId}`);

      let result;
      try {
        console.log(`🚀 Executing API call to Composio...`);
        console.log(`🚀 Action: ${actionConfig.action}`);
        console.log(`🚀 App: ${appName}`);
        console.log(`🚀 Entity ID: ${session.user.id}`);
        console.log(`🚀 Connection ID: ${connectionId}`);
        console.log(`🚀 Input Parameters:`, JSON.stringify(inferredParams, null, 2));

        result = await toolset.client.actions.execute({
          actionName: actionConfig.action,
          requestBody: {
            appName: appName,
            input: inferredParams,
            connectedAccountId: connectionId
          }
        });

        console.log(`✅ API call successful!`);
        console.log(`✅ Raw result:`, JSON.stringify(result, null, 2));
      } catch (error) {
        const execError = error as Error;
        console.error("❌ Calendar Agent execution error:", execError);
        console.error("❌ Error details:", {
          message: execError.message,
          stack: execError.stack,
          name: execError.name
        });

        // Handle specific error types
        if (execError.message && execError.message.includes('ConnectedAccountNotFoundError')) {
          return {
            success: false,
            error: 'Google Calendar connection expired',
            message: 'Your Google Calendar connection appears to have expired or been invalidated. Please reconnect your Google Calendar account and try again.',
            redirectUrl: "/profile/connections",
            detectedIntent: intent,
          };
        }

        if (execError.message && execError.message.includes('NotFoundError')) {
          return {
            success: false,
            error: 'Calendar action not found',
            message: `The calendar operation "${actionConfig.action}" could not be found. This might be a temporary issue.`,
            detectedIntent: intent,
          };
        }

        return {
          success: false,
          error: 'Calendar operation failed',
          message: `Failed to ${actionConfig.description.toLowerCase()}: ${execError.message}`,
          detectedIntent: intent,
          inferredParams,
        };
      }

      // Step 7: Process and format the result
      console.log(`🔍 Processing result...`);
      console.log(`🔍 Result structure:`, {
        hasResult: !!result,
        successful: result?.successful,
        hasData: !!result?.data,
        hasError: !!result?.error,
        keys: result ? Object.keys(result) : []
      });

      const success = result?.successful !== false;
      console.log(`🔍 Success determination: ${success}`);

      if (!success) {
        console.log(`❌ Operation failed!`);
        console.log(`❌ Error:`, result?.error);
        console.log(`❌ Full result:`, result);

        return {
          success: false,
          error: result?.error || 'Unknown error',
          message: `Failed to ${actionConfig.description.toLowerCase()}: ${result?.error || 'Unknown error occurred'}`,
          detectedIntent: intent,
          inferredParams,
          rawResult: result,
        };
      }

      // Step 8: Let AI format the real calendar data into user-friendly response
      console.log(`✅ Operation successful! Using AI to format real calendar data...`);

      let resultData = result?.data || result;
      console.log(`📊 Real calendar data received:`, JSON.stringify(resultData, null, 2));

      // Use AI to format the real data into a user-friendly response
      const formattedMessage = await formatCalendarResponse(task, intent, resultData, inferredParams);
      console.log(`🤖 AI-formatted response: ${formattedMessage}`);

      const finalResponse = {
        success: true,
        message: formattedMessage,
        action: actionConfig.action,
        detectedIntent: intent,
        inferredParams,
        result: resultData,
        // Include original task for context
        originalTask: task,
      };

      console.log(`🎉 Calendar Agent completed successfully!`);
      console.log(`🎉 Final response:`, JSON.stringify(finalResponse, null, 2));

      return finalResponse;

    } catch (error) {
      console.error('Calendar Agent error:', error);
      return {
        success: false,
        error: 'Calendar agent error',
        message: `An unexpected error occurred while processing your calendar request: ${error instanceof Error ? error.message : 'Unknown error'}`,
        originalTask: task,
      };
    }
  },
});
