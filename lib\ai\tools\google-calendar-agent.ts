import { tool, generateObject } from 'ai';
import { z } from 'zod';
import { auth } from '@/app/(auth)/auth';
import { OpenAIToolSet } from 'composio-core';
import { getCurrentTimeForLocation } from '@/lib/utils/date-time';
import { getConnectionsByUserId } from '@/lib/db/queries';
import { myProvider } from '@/lib/ai/providers';

/**
 * Google Calendar Sub-Agent Tool
 *
 * A specialized sub-agent that handles all Google Calendar operations with AI-driven
 * intent detection and parameter inference. Replaces regex patterns with intelligent
 * natural language processing.
 */

interface CalendarActionMapping {
  [key: string]: {
    action: string;
    description: string;
    requiredParams: string[];
    optionalParams: string[];
  };
}

// Complete mapping based on official Google Calendar API documentation
const CALENDAR_ACTIONS: CalendarActionMapping = {
  'create_event': {
    action: 'GOOGLECALENDAR_CREATE_EVENT',
    description: 'Create a new calendar event',
    requiredParams: ['start_datetime'],
    optionalParams: [
      'attendees', 'calendar_id', 'create_meeting_room', 'description', 'eventType',
      'event_duration_hour', 'event_duration_minutes', 'guestsCanInviteOthers',
      'guestsCanSeeOtherGuests', 'guests_can_modify', 'location', 'recurrence',
      'send_updates', 'summary', 'timezone', 'transparency', 'visibility'
    ]
  },
  'list_events': {
    action: 'GOOGLECALENDAR_FIND_EVENT',
    description: 'Find and list calendar events',
    requiredParams: [],
    optionalParams: [
      'calendar_id', 'event_types', 'max_results', 'order_by', 'page_token',
      'query', 'show_deleted', 'single_events', 'timeMax', 'timeMin', 'updated_min'
    ]
  },
  'find_events': {
    action: 'GOOGLECALENDAR_FIND_EVENT',
    description: 'Search for specific calendar events',
    requiredParams: [],
    optionalParams: [
      'calendar_id', 'event_types', 'max_results', 'order_by', 'page_token',
      'query', 'show_deleted', 'single_events', 'timeMax', 'timeMin', 'updated_min'
    ]
  },
  'get_calendar': {
    action: 'GOOGLECALENDAR_GET_CALENDAR',
    description: 'Get calendar information',
    requiredParams: [],
    optionalParams: ['calendar_id']
  },
  'list_calendars': {
    action: 'GOOGLECALENDAR_LIST_CALENDARS',
    description: 'List all available calendars',
    requiredParams: [],
    optionalParams: [
      'max_results', 'min_access_role', 'page_token', 'show_deleted', 'show_hidden', 'sync_token'
    ]
  },
  'update_event': {
    action: 'GOOGLECALENDAR_UPDATE_EVENT',
    description: 'Update an existing calendar event',
    requiredParams: ['event_id', 'start_datetime'],
    optionalParams: [
      'attendees', 'calendar_id', 'create_meeting_room', 'description', 'eventType',
      'event_duration_hour', 'event_duration_minutes', 'guestsCanInviteOthers',
      'guestsCanSeeOtherGuests', 'guests_can_modify', 'location', 'recurrence',
      'send_updates', 'summary', 'timezone', 'transparency', 'visibility'
    ]
  },
  'patch_event': {
    action: 'GOOGLECALENDAR_PATCH_EVENT',
    description: 'Patch specific fields of an existing calendar event',
    requiredParams: ['event_id', 'calendar_id'],
    optionalParams: [
      'attendees', 'conference_data_version', 'description', 'end_time',
      'location', 'max_attendees', 'send_updates', 'start_time', 'summary',
      'supports_attachments', 'timezone'
    ]
  },
  'delete_event': {
    action: 'GOOGLECALENDAR_DELETE_EVENT',
    description: 'Delete a calendar event',
    requiredParams: ['event_id'],
    optionalParams: ['calendar_id']
  },
  'find_free_slots': {
    action: 'GOOGLECALENDAR_FIND_FREE_SLOTS',
    description: 'Find available time slots',
    requiredParams: [],
    optionalParams: [
      'calendar_expansion_max', 'group_expansion_max', 'items', 'time_max', 'time_min', 'timezone'
    ]
  },
  'quick_add': {
    action: 'GOOGLECALENDAR_QUICK_ADD',
    description: 'Quick add event from natural language',
    requiredParams: ['text'],
    optionalParams: ['calendar_id', 'send_updates']
  },
  'get_current_time': {
    action: 'GOOGLECALENDAR_GET_CURRENT_DATE_TIME',
    description: 'Get current date and time for timezone',
    requiredParams: [],
    optionalParams: ['timezone']
  },
  'duplicate_calendar': {
    action: 'GOOGLECALENDAR_DUPLICATE_CALENDAR',
    description: 'Duplicate a calendar',
    requiredParams: [],
    optionalParams: ['summary']
  },
  'patch_calendar': {
    action: 'GOOGLECALENDAR_PATCH_CALENDAR',
    description: 'Update calendar properties',
    requiredParams: ['calendar_id', 'summary'],
    optionalParams: ['description', 'location', 'timezone']
  },
  'remove_attendee': {
    action: 'GOOGLECALENDAR_REMOVE_ATTENDEE',
    description: 'Remove an attendee from an event',
    requiredParams: ['attendee_email', 'event_id'],
    optionalParams: ['calendar_id']
  },
  'sync_events': {
    action: 'GOOGLECALENDAR_SYNC_EVENTS',
    description: 'Sync events from last sync token',
    requiredParams: [],
    optionalParams: [
      'calendar_id', 'event_types', 'max_results', 'pageToken', 'single_events', 'sync_token'
    ]
  }
};

/**
 * AI-driven intent detection from natural language
 * Replaces regex patterns with intelligent AI classification
 */
async function detectCalendarIntent(task: string): Promise<string> {
  console.log(`🤖 AI Intent Detection - Input: "${task}"`);

  try {
    const availableIntents = Object.keys(CALENDAR_ACTIONS);
    const intentDescriptions = availableIntents.map(intent =>
      `${intent}: ${CALENDAR_ACTIONS[intent].description}`
    ).join('\n');

    const { object: intentResult } = await generateObject({
      model: myProvider.languageModel('chat-model'),
      schema: z.object({
        intent: z.enum(availableIntents as [string, ...string[]]).describe('The detected calendar intent'),
        confidence: z.number().min(0).max(1).describe('Confidence level (0-1)'),
        reasoning: z.string().describe('Brief explanation of why this intent was chosen')
      }),
      prompt: `Analyze this calendar-related request and determine the most appropriate intent:

REQUEST: "${task}"

AVAILABLE INTENTS:
${intentDescriptions}

Consider:
- The primary action the user wants to perform
- Keywords and context clues
- Natural language variations and synonyms
- Implicit vs explicit requests

Choose the most appropriate intent and provide your confidence level and reasoning.`,
    });

    console.log(`🤖 AI Intent Detection Result:`, {
      intent: intentResult.intent,
      confidence: intentResult.confidence,
      reasoning: intentResult.reasoning
    });

    // Use AI result if confidence is reasonable, otherwise fallback
    if (intentResult.confidence >= 0.6) {
      return intentResult.intent;
    } else {
      console.log(`⚠️ Low confidence (${intentResult.confidence}), using fallback logic`);
      return 'list_events'; // Safe fallback
    }

  } catch (error) {
    console.error('❌ AI Intent Detection failed:', error);

    // Simple fallback logic for critical cases
    const taskLower = task.toLowerCase();
    if (taskLower.includes('create') || taskLower.includes('schedule') || taskLower.includes('add')) {
      return 'create_event';
    } else if (taskLower.includes('update') || taskLower.includes('modify') || taskLower.includes('change')) {
      return 'update_event';
    } else if (taskLower.includes('delete') || taskLower.includes('remove') || taskLower.includes('cancel')) {
      return 'delete_event';
    } else if (taskLower.includes('free') || taskLower.includes('available')) {
      return 'find_free_slots';
    } else if (taskLower.includes('calendars')) {
      return 'list_calendars';
    } else {
      return 'list_events';
    }
  }
}

/**
 * AI-driven parameter inference from natural language and context
 * Replaces hardcoded regex patterns with intelligent AI parameter extraction
 */
async function inferCalendarParameters(task: string, intent: string, providedParams: any = {}): Promise<any> {
  console.log(`🤖 AI Parameter Inference - Intent: ${intent}, Task: "${task}"`);
  console.log(`🤖 Provided params:`, JSON.stringify(providedParams, null, 2));

  try {
    const actionConfig = CALENDAR_ACTIONS[intent];

    // Get current time for Morocco timezone (user's location)
    let currentTime;
    try {
      currentTime = await getCurrentTimeForLocation('Africa/Casablanca');
      console.log(`🌍 Current time in Morocco:`, currentTime);
    } catch (error) {
      console.warn('⚠️ Failed to get Morocco time, using system time:', error);
      currentTime = new Date().toISOString();
    }

    const { object: paramResult } = await generateObject({
      model: myProvider.languageModel('chat-model'),
      schema: z.object({
        parameters: z.object({
          // Date/Time parameters
          start_datetime: z.string().optional().describe('ISO format start date/time (YYYY-MM-DDTHH:mm:ssZ)'),
          end_datetime: z.string().optional().describe('ISO format end date/time'),
          timeMin: z.string().optional().describe('ISO format minimum time for search'),
          timeMax: z.string().optional().describe('ISO format maximum time for search'),
          timezone: z.string().optional().describe('Timezone (default: Africa/Casablanca)'),

          // Event details
          summary: z.string().optional().describe('Event title/name'),
          description: z.string().optional().describe('Event description'),
          location: z.string().optional().describe('Event location'),

          // Duration parameters
          event_duration_minutes: z.number().optional().describe('Event duration in minutes'),
          event_duration_hour: z.number().optional().describe('Event duration in hours'),

          // Calendar management
          calendar_id: z.string().optional().describe('Calendar ID (default: primary)'),
          event_id: z.string().optional().describe('Event ID for updates/deletes'),
          search_title: z.string().optional().describe('Event title to search for when ID not available'),

          // Search parameters
          query: z.string().optional().describe('Search query for finding events'),
          max_results: z.number().optional().describe('Maximum number of results (default: 10)'),

          // Attendees and permissions
          attendees: z.array(z.string()).optional().describe('List of attendee email addresses'),
          send_updates: z.boolean().optional().describe('Whether to send updates to attendees'),
          guestsCanInviteOthers: z.boolean().optional().describe('Whether guests can invite others'),
          guestsCanSeeOtherGuests: z.boolean().optional().describe('Whether guests can see other guests'),
          guests_can_modify: z.boolean().optional().describe('Whether guests can modify the event'),

          // Event properties
          eventType: z.string().optional().describe('Event type (default: default)'),
          transparency: z.string().optional().describe('Event transparency (default: opaque)'),
          visibility: z.string().optional().describe('Event visibility (default: default)'),

          // Recurrence
          recurrence: z.array(z.string()).optional().describe('Recurrence rules'),

          // Meeting room
          create_meeting_room: z.boolean().optional().describe('Whether to create a meeting room'),

          // Quick add
          text: z.string().optional().describe('Natural language text for quick add'),

          // Free/busy
          items: z.array(z.string()).optional().describe('Calendar items for free/busy query'),
          time_min: z.string().optional().describe('Minimum time for free/busy query'),
          time_max: z.string().optional().describe('Maximum time for free/busy query')
        }).describe('Extracted calendar parameters'),
        reasoning: z.string().describe('Brief explanation of parameter extraction logic'),
        confidence: z.number().min(0).max(1).describe('Confidence level in parameter extraction')
      }),
      prompt: `Extract calendar parameters from this natural language request:

REQUEST: "${task}"
INTENT: ${intent}
ACTION: ${actionConfig.action}

AVAILABLE PARAMETERS:
Required: ${actionConfig.requiredParams.join(', ')}
Optional: ${actionConfig.optionalParams.join(', ')}

CURRENT CONTEXT:
- Current time: ${currentTime}
- User timezone: Africa/Casablanca (Morocco)
- User provided params: ${JSON.stringify(providedParams, null, 2)}

PARAMETER EXTRACTION GUIDELINES:

1. **Date/Time Parameters:**
   - start_datetime: ISO format (YYYY-MM-DDTHH:mm:ssZ)
   - For "today": use current date
   - For "tomorrow": add 1 day
   - For day names (Monday, Tuesday, etc.): find next occurrence
   - Default time: 9:00 AM if not specified
   - Handle time ranges: "2pm to 4pm", "14:00-16:00"
   - Handle AM/PM: "2pm" = 14:00, "2am" = 02:00

2. **Event Details:**
   - summary: Extract event title/name from context
   - description: Additional details if mentioned
   - location: Physical or virtual location if specified
   - event_duration_minutes: Duration in minutes (default: 60)

3. **Search Parameters:**
   - query: Search terms for finding events
   - timeMin/timeMax: Time range for event searches
   - max_results: Number of results (default: 10)

4. **Update Parameters:**
   - event_id: Event identifier if provided
   - search_title: Event title to search for if no ID

5. **Smart Defaults:**
   - calendar_id: "primary" (user's main calendar)
   - timezone: "Africa/Casablanca"
   - send_updates: true for events with attendees

Extract only relevant parameters for the ${intent} action. Use smart inference for missing required parameters.`,
    });

    console.log(`🤖 AI Parameter Extraction Result:`, {
      parameters: paramResult.parameters,
      reasoning: paramResult.reasoning,
      confidence: paramResult.confidence
    });

    // Merge AI-extracted parameters with provided parameters (provided takes precedence)
    const finalParams = {
      ...paramResult.parameters,
      ...providedParams
    };

    console.log(`🤖 Final merged parameters:`, JSON.stringify(finalParams, null, 2));
    return finalParams;

  } catch (error) {
    console.error('❌ AI Parameter Inference failed:', error);

    // Fallback to basic parameter extraction
    console.log('🔄 Using fallback parameter extraction...');

    const params = { ...providedParams };
    const taskLower = task.toLowerCase();
    const now = new Date();

    // Basic fallback logic for critical parameters
    switch (intent) {
      case 'create_event':
        if (!params.summary) {
          // Extract title from task
          const titleMatch = task.match(/(?:create|schedule|add|book)\s+(?:a\s+)?(?:meeting|event|appointment)?\s*(?:called|titled|named)?\s*["']?([^"']+?)["']?(?:\s+(?:for|at|on|from|tomorrow|today)|\s*$)/i);
          params.summary = titleMatch ? titleMatch[1].trim() : 'New Event';
        }

        if (!params.start_datetime) {
          const baseDate = new Date(now);
          if (taskLower.includes('tomorrow')) {
            baseDate.setDate(baseDate.getDate() + 1);
          }
          baseDate.setHours(9, 0, 0, 0);
          params.start_datetime = baseDate.toISOString();
        }

        if (!params.event_duration_minutes && !params.event_duration_hour) {
          params.event_duration_minutes = 60; // 1 hour default
        }
        break;

      case 'list_events':
      case 'find_events':
        if (!params.timeMin || !params.timeMax) {
          const today = new Date(now);
          today.setHours(0, 0, 0, 0);
          params.timeMin = today.toISOString();

          const endOfDay = new Date(today);
          endOfDay.setHours(23, 59, 59, 999);
          params.timeMax = endOfDay.toISOString();
        }

        if (!params.max_results) {
          params.max_results = 10;
        }
        break;

      case 'update_event':
      case 'patch_event':
        if (!params.event_id && !params.search_title) {
          // Try to extract event title for search
          const titleMatch = task.match(/(?:update|modify|change)\s+(?:event\s+)?["']?([^"']+?)["']?(?:\s+to|\s*$)/i);
          if (titleMatch) {
            params.search_title = titleMatch[1].trim();
          }
        }
        break;

      case 'delete_event':
        if (!params.event_id && !params.search_title) {
          // Try to extract event title for search
          const titleMatch = task.match(/(?:delete|remove|cancel)\s+(?:event\s+)?["']?([^"']+?)["']?(?:\s*$)/i);
          if (titleMatch) {
            params.search_title = titleMatch[1].trim();
          }
        }
        break;
    }

    // Set smart defaults
    if (!params.calendar_id) {
      params.calendar_id = 'primary';
    }

    console.log(`🔄 Fallback parameters:`, JSON.stringify(params, null, 2));
    return params;
  }
}

/**
 * Get app name from action
 */
function getAppNameFromAction(action: string): string {
  return action.toLowerCase().split('_')[0];
}

/**
 * Main Google Calendar Sub-Agent Tool
 */
export const googleCalendarAgent = tool({
  description: 'Intelligent Google Calendar assistant that handles all calendar operations including creating events, listing events, finding free time, managing calendars, and more. Understands natural language and automatically infers parameters.',
  parameters: z.object({
    task: z.string().describe('Describe what you want to do with your calendar in natural language (e.g., "schedule a meeting with John tomorrow at 2pm", "show me my events today", "find free time this afternoon")'),
    params: z.record(z.any()).optional().describe('Optional specific parameters to override automatic inference (e.g., {"calendar_id": "<EMAIL>", "duration": 30})'),
  }),
  execute: async ({ task, params = {} }) => {
    try {
      const session = await auth();

      if (!session || !session.user) {
        return {
          success: false,
          error: 'Authentication required',
          message: 'Please sign in to access your Google Calendar.',
        };
      }

      // Step 1: Detect intent from natural language
      let intent = await detectCalendarIntent(task);
      let actionConfig = CALENDAR_ACTIONS[intent];

      console.log(`🎯 Calendar Agent: Detected intent "${intent}" for task: "${task}"`);

      if (!actionConfig) {
        return {
          success: false,
          error: 'Unknown calendar operation',
          message: `I couldn't understand what you want to do with your calendar. Please try rephrasing your request.`,
          detectedIntent: intent,
        };
      }

      // Step 2: Get user's connected services
      const connections = await getConnectionsByUserId(session.user?.id || '');

      if (!connections || connections.length === 0) {
        return {
          success: false,
          error: 'No connected services',
          message: 'You need to connect at least one service before executing calendar actions. Please connect your Google Calendar in the profile settings.',
          redirectUrl: "/profile/connections",
          detectedIntent: intent,
        };
      }

      // Step 3: Find Google Calendar connection
      const appName = getAppNameFromAction(actionConfig.action);
      const appConnections = connections.filter(conn =>
        conn.provider.toLowerCase() === appName &&
        conn.status === 'ACTIVE'
      );

      if (appConnections.length === 0) {
        return {
          success: false,
          error: 'Google Calendar not connected',
          message: 'Your Google Calendar account is not connected or the connection is inactive. Please connect your Google Calendar in the profile settings.',
          redirectUrl: "/profile/connections",
          detectedIntent: intent,
        };
      }

      // Use the most recently created connection
      const connection = appConnections.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];
      const connectionId = connection.connectionId;

      console.log(`📅 Using Google Calendar connection: ${connectionId}`);

      // Step 4: Infer parameters from natural language and context
      const inferredParams = await inferCalendarParameters(task, intent, params);

      console.log(`🧠 Calendar Agent: Inferred parameters for "${intent}":`, JSON.stringify(inferredParams, null, 2));

      // Step 4.5: Smart action selection for updates
      if (intent === 'update_event') {
        const hasTimeFields = !!(inferredParams.start_datetime || inferredParams.end_datetime || inferredParams.event_duration_hour || inferredParams.event_duration_minutes);
        const hasOnlyMetadataFields = !!(inferredParams.summary || inferredParams.description || inferredParams.location) && !hasTimeFields;

        console.log(`🔄 Update action selection:`, {
          hasTimeFields,
          hasOnlyMetadataFields,
          availableFields: Object.keys(inferredParams).filter(key => key !== 'calendar_id')
        });

        if (hasOnlyMetadataFields) {
          console.log(`🔄 Switching to PATCH_EVENT for metadata-only update`);
          intent = 'patch_event';
          actionConfig = CALENDAR_ACTIONS[intent];
        }
      }

      // Step 5: Validate required parameters
      console.log(`🔍 Validating required parameters...`);
      console.log(`🔍 Required params for ${intent}:`, actionConfig.requiredParams);
      console.log(`🔍 Current params:`, Object.keys(inferredParams));

      const missingParams = actionConfig.requiredParams.filter(param => {
        const hasParam = !!inferredParams[param];
        console.log(`🔍 Parameter "${param}": ${hasParam ? '✅ PROVIDED' : '❌ MISSING'}`);
        return !hasParam;
      });

      if (missingParams.length > 0) {
        console.log(`❌ Missing required parameters: ${missingParams.join(', ')}`);

        // Special handling for delete_event - try to find event by title if no ID
        if (intent === 'delete_event' && missingParams.includes('event_id') && inferredParams.search_title) {
          console.log(`🔍 Attempting to find event by title: "${inferredParams.search_title}"`);
          return {
            success: false,
            error: 'Event ID required for deletion',
            message: `To delete an event, I need the event ID. Please provide the event ID or use a more specific command like "delete event with id [event_id]".`,
            detectedIntent: intent,
            missingParams,
            inferredParams,
            suggestion: `Try: "delete event with id [event_id]" or first list events to find the ID.`
          };
        }

        // Special handling for update_event and patch_event - try to find event by title if no ID
        if ((intent === 'update_event' || intent === 'patch_event') && missingParams.includes('event_id')) {
          console.log(`🔍 ${intent} missing event_id`);
          console.log(`🔍 Available params:`, Object.keys(inferredParams));
          console.log(`🔍 Search title:`, inferredParams.search_title);
          console.log(`🔍 Summary:`, inferredParams.summary);

          if (inferredParams.search_title) {
            console.log(`🔍 Attempting to find event by title for ${intent}: "${inferredParams.search_title}"`);
            return {
              success: false,
              error: 'Event ID required for update',
              message: `To update an event, I need the event ID. I found you want to update an event with title "${inferredParams.search_title}", but I need the specific event ID.`,
              detectedIntent: intent,
              missingParams,
              inferredParams,
              suggestion: `Try: "update event with id [event_id] to [new_time]" or first list events to find the ID.`
            };
          } else {
            console.log(`🔍 No search title found, generic event ID error`);
            return {
              success: false,
              error: 'Event ID required for update',
              message: `To update an event, I need the event ID. Please specify which event you want to update.`,
              detectedIntent: intent,
              missingParams,
              inferredParams,
              suggestion: `Try: "update event with id [event_id]" or first list events to find the ID.`
            };
          }
        }

        return {
          success: false,
          error: 'Missing required parameters',
          message: `I need more information to ${actionConfig.description.toLowerCase()}. Missing: ${missingParams.join(', ')}`,
          detectedIntent: intent,
          inferredParams,
          missingParams,
        };
      }

      console.log(`✅ All required parameters provided!`);

      // Step 6: Execute the calendar action
      const toolset = new OpenAIToolSet();

      console.log(`🗓️ Calendar Agent: Executing ${actionConfig.action} with intent "${intent}"`);
      console.log(`📋 Inferred parameters:`, inferredParams);
      console.log(`🔗 Using connection ID: ${connectionId}`);

      let result;
      try {
        console.log(`🚀 Executing API call to Composio...`);
        console.log(`🚀 Action: ${actionConfig.action}`);
        console.log(`🚀 App: ${appName}`);
        console.log(`🚀 Entity ID: ${session.user.id}`);
        console.log(`🚀 Connection ID: ${connectionId}`);
        console.log(`🚀 Input Parameters:`, JSON.stringify(inferredParams, null, 2));

        result = await toolset.client.actions.execute({
          actionName: actionConfig.action,
          requestBody: {
            appName: appName,
            input: inferredParams,
            connectedAccountId: connectionId
          }
        });

        console.log(`✅ API call successful!`);
        console.log(`✅ Raw result:`, JSON.stringify(result, null, 2));
      } catch (error) {
        const execError = error as Error;
        console.error("❌ Calendar Agent execution error:", execError);
        console.error("❌ Error details:", {
          message: execError.message,
          stack: execError.stack,
          name: execError.name
        });

        // Handle specific error types
        if (execError.message && execError.message.includes('ConnectedAccountNotFoundError')) {
          return {
            success: false,
            error: 'Google Calendar connection expired',
            message: 'Your Google Calendar connection appears to have expired or been invalidated. Please reconnect your Google Calendar account and try again.',
            redirectUrl: "/profile/connections",
            detectedIntent: intent,
          };
        }

        if (execError.message && execError.message.includes('NotFoundError')) {
          return {
            success: false,
            error: 'Calendar action not found',
            message: `The calendar operation "${actionConfig.action}" could not be found. This might be a temporary issue.`,
            detectedIntent: intent,
          };
        }

        return {
          success: false,
          error: 'Calendar operation failed',
          message: `Failed to ${actionConfig.description.toLowerCase()}: ${execError.message}`,
          detectedIntent: intent,
          inferredParams,
        };
      }

      // Step 7: Process and format the result
      console.log(`🔍 Processing result...`);
      console.log(`🔍 Result structure:`, {
        hasResult: !!result,
        successful: result?.successful,
        hasData: !!result?.data,
        hasError: !!result?.error,
        keys: result ? Object.keys(result) : []
      });

      const success = result?.successful !== false;
      console.log(`🔍 Success determination: ${success}`);

      if (!success) {
        console.log(`❌ Operation failed!`);
        console.log(`❌ Error:`, result?.error);
        console.log(`❌ Full result:`, result);

        return {
          success: false,
          error: result?.error || 'Unknown error',
          message: `Failed to ${actionConfig.description.toLowerCase()}: ${result?.error || 'Unknown error occurred'}`,
          detectedIntent: intent,
          inferredParams,
          rawResult: result,
        };
      }

      // Step 8: Format success response based on intent
      console.log(`✅ Operation successful! Formatting response...`);

      let formattedMessage = '';
      let resultData = result?.data || result;

      console.log(`📊 Result data:`, resultData);
      console.log(`📊 Intent for formatting: ${intent}`);

      switch (intent) {
        case 'create_event':
          console.log(`📅 Formatting create_event response...`);
          console.log(`📅 Event summary: ${inferredParams.summary}`);
          console.log(`📅 Start datetime: ${inferredParams.start_datetime}`);

          formattedMessage = `✅ Successfully created calendar event "${inferredParams.summary}"`;
          if (inferredParams.start_datetime) {
            const startTime = new Date(inferredParams.start_datetime);
            console.log(`📅 Parsed start time:`, {
              original: inferredParams.start_datetime,
              parsed: startTime.toString(),
              localDate: startTime.toLocaleDateString(),
              localTime: startTime.toLocaleTimeString()
            });
            formattedMessage += ` for ${startTime.toLocaleDateString()} at ${startTime.toLocaleTimeString()}`;
          }

          console.log(`📅 Final formatted message: ${formattedMessage}`);
          break;

        case 'list_events':
        case 'find_events':
          const events = Array.isArray(resultData?.items) ? resultData.items : [];
          if (events.length === 0) {
            formattedMessage = '📅 No events found for the specified time period.';
          } else {
            formattedMessage = `📅 Found ${events.length} event${events.length === 1 ? '' : 's'}`;
            if (inferredParams.timeMin && inferredParams.timeMax) {
              const startDate = new Date(inferredParams.timeMin);
              const endDate = new Date(inferredParams.timeMax);
              if (startDate.toDateString() === endDate.toDateString()) {
                formattedMessage += ` for ${startDate.toLocaleDateString()}`;
              } else {
                formattedMessage += ` from ${startDate.toLocaleDateString()} to ${endDate.toLocaleDateString()}`;
              }
            }
          }
          break;

        case 'update_event':
        case 'patch_event':
          console.log(`✏️ Formatting ${intent} response...`);
          console.log(`✏️ Event ID: ${inferredParams.event_id}`);
          console.log(`✏️ New summary: ${inferredParams.summary}`);
          console.log(`✏️ New start time: ${inferredParams.start_datetime}`);
          console.log(`✏️ Duration: ${inferredParams.event_duration_hour}h ${inferredParams.event_duration_minutes}m`);
          console.log(`✏️ Result data:`, resultData);

          formattedMessage = `✅ Successfully updated calendar event`;
          if (inferredParams.event_id) {
            formattedMessage += ` (ID: ${inferredParams.event_id})`;
          }
          if (inferredParams.summary) {
            formattedMessage += ` with title "${inferredParams.summary}"`;
          }
          if (inferredParams.start_datetime) {
            const startTime = new Date(inferredParams.start_datetime);
            formattedMessage += ` to ${startTime.toLocaleDateString()} at ${startTime.toLocaleTimeString()}`;
          }

          console.log(`✏️ Final formatted message: ${formattedMessage}`);
          break;

        case 'delete_event':
          console.log(`🗑️ Formatting delete_event response...`);
          console.log(`🗑️ Event ID: ${inferredParams.event_id}`);
          console.log(`🗑️ Result data:`, resultData);

          formattedMessage = `✅ Successfully deleted calendar event`;
          if (inferredParams.event_id) {
            formattedMessage += ` (ID: ${inferredParams.event_id})`;
          }

          console.log(`🗑️ Final formatted message: ${formattedMessage}`);
          break;

        case 'find_free_slots':
          formattedMessage = `🕐 Found availability information for your calendar`;
          break;

        case 'list_calendars':
          const calendars = Array.isArray(resultData?.items) ? resultData.items : [];
          formattedMessage = `📋 Found ${calendars.length} calendar${calendars.length === 1 ? '' : 's'} in your account`;
          break;

        default:
          formattedMessage = `✅ Successfully completed calendar operation: ${actionConfig.description}`;
      }

      const finalResponse = {
        success: true,
        message: formattedMessage,
        action: actionConfig.action,
        detectedIntent: intent,
        inferredParams,
        result: resultData,
        // Include original task for context
        originalTask: task,
      };

      console.log(`🎉 Calendar Agent completed successfully!`);
      console.log(`🎉 Final response:`, JSON.stringify(finalResponse, null, 2));

      return finalResponse;

    } catch (error) {
      console.error('Calendar Agent error:', error);
      return {
        success: false,
        error: 'Calendar agent error',
        message: `An unexpected error occurred while processing your calendar request: ${error instanceof Error ? error.message : 'Unknown error'}`,
        originalTask: task,
      };
    }
  },
});
