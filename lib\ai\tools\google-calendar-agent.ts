import { tool } from 'ai';
import { z } from 'zod';
import { auth } from '@/app/(auth)/auth';
import { OpenAIToolSet } from 'composio-core';
import { getConnectionsByUserId } from '@/lib/db/queries';

/**
 * AI-Driven Google Calendar Sub-Agent Tool
 * 
 * The AI will analyze the task and choose the appropriate Google Calendar action with parameters.
 * No regex patterns - pure AI intelligence for intent detection and parameter inference.
 */

// Complete Google Calendar Actions Schema - AI will choose from these
const GOOGLE_CALENDAR_ACTIONS = {
  GOOGLECALENDAR_CREATE_EVENT: {
    description: 'Create a new event in a google calendar',
    required_params: ['start_datetime'],
    optional_params: {
      attendees: { type: 'array', description: 'List of attendees for the event' },
      calendar_id: { type: 'string', default: 'primary', description: 'Calendar ID to create event in' },
      create_meeting_room: { type: 'boolean', description: 'Whether to create a meeting room' },
      description: { type: 'string', description: 'Event description' },
      eventType: { type: 'string', default: 'default', description: 'Type of event' },
      event_duration_hour: { type: 'integer', description: 'Duration in hours' },
      event_duration_minutes: { type: 'integer', default: 30, description: 'Duration in minutes' },
      guestsCanInviteOthers: { type: 'boolean', description: 'Whether guests can invite others' },
      guestsCanSeeOtherGuests: { type: 'boolean', description: 'Whether guests can see other guests' },
      guests_can_modify: { type: 'boolean', description: 'Whether guests can modify the event' },
      location: { type: 'string', description: 'Event location' },
      recurrence: { type: 'array', description: 'Recurrence rules for repeating events' },
      send_updates: { type: 'boolean', description: 'Whether to send updates to attendees' },
      summary: { type: 'string', description: 'Event title/summary' },
      timezone: { type: 'string', description: 'Timezone for the event' },
      transparency: { type: 'string', default: 'opaque', description: 'Event transparency (opaque/transparent)' },
      visibility: { type: 'string', default: 'default', description: 'Event visibility' }
    }
  },
  GOOGLECALENDAR_FIND_EVENT: {
    description: 'Find events in a google calendar based on search criteria',
    required_params: [],
    optional_params: {
      calendar_id: { type: 'string', default: 'primary', description: 'Calendar ID to search in' },
      event_types: { type: 'array', default: ['default', 'outOfOffice', 'focusTime', 'workingLocation'], description: 'Types of events to include' },
      max_results: { type: 'integer', default: 10, description: 'Maximum number of results' },
      order_by: { type: 'string', description: 'How to order results' },
      page_token: { type: 'string', description: 'Token for pagination' },
      query: { type: 'string', description: 'Search query text' },
      show_deleted: { type: 'boolean', description: 'Whether to show deleted events' },
      single_events: { type: 'boolean', default: true, description: 'Whether to expand recurring events' },
      timeMax: { type: 'string', description: 'Upper bound for event start time (ISO format)' },
      timeMin: { type: 'string', description: 'Lower bound for event start time (ISO format)' },
      updated_min: { type: 'string', description: 'Lower bound for when events were last updated' }
    }
  },
  GOOGLECALENDAR_UPDATE_EVENT: {
    description: 'Update an existing event in a google calendar',
    required_params: ['event_id', 'start_datetime'],
    optional_params: {
      attendees: { type: 'array', description: 'List of attendees for the event' },
      calendar_id: { type: 'string', default: 'primary', description: 'Calendar ID containing the event' },
      create_meeting_room: { type: 'boolean', description: 'Whether to create a meeting room' },
      description: { type: 'string', description: 'Event description' },
      eventType: { type: 'string', default: 'default', description: 'Type of event' },
      event_duration_hour: { type: 'integer', description: 'Duration in hours' },
      event_duration_minutes: { type: 'integer', default: 30, description: 'Duration in minutes' },
      guestsCanInviteOthers: { type: 'boolean', description: 'Whether guests can invite others' },
      guestsCanSeeOtherGuests: { type: 'boolean', description: 'Whether guests can see other guests' },
      guests_can_modify: { type: 'boolean', description: 'Whether guests can modify the event' },
      location: { type: 'string', description: 'Event location' },
      recurrence: { type: 'array', description: 'Recurrence rules for repeating events' },
      send_updates: { type: 'boolean', description: 'Whether to send updates to attendees' },
      summary: { type: 'string', description: 'Event title/summary' },
      timezone: { type: 'string', description: 'Timezone for the event' },
      transparency: { type: 'string', default: 'opaque', description: 'Event transparency' },
      visibility: { type: 'string', default: 'default', description: 'Event visibility' }
    }
  },
  GOOGLECALENDAR_PATCH_EVENT: {
    description: 'Patches an event in google calendar. Only specified fields will be updated',
    required_params: ['event_id', 'calendar_id'],
    optional_params: {
      attendees: { type: 'array', description: 'List of attendees for the event' },
      conference_data_version: { type: 'integer', description: 'Conference data version' },
      description: { type: 'string', description: 'Event description' },
      end_time: { type: 'string', description: 'Event end time (ISO format)' },
      location: { type: 'string', description: 'Event location' },
      max_attendees: { type: 'integer', description: 'Maximum number of attendees' },
      send_updates: { type: 'string', description: 'Whether to send updates' },
      start_time: { type: 'string', description: 'Event start time (ISO format)' },
      summary: { type: 'string', description: 'Event title/summary' },
      supports_attachments: { type: 'boolean', description: 'Whether event supports attachments' },
      timezone: { type: 'string', description: 'Timezone for the event' }
    }
  },
  GOOGLECALENDAR_DELETE_EVENT: {
    description: 'Delete an event from a google calendar',
    required_params: ['event_id'],
    optional_params: {
      calendar_id: { type: 'string', default: 'primary', description: 'Calendar ID containing the event' }
    }
  },
  GOOGLECALENDAR_FIND_FREE_SLOTS: {
    description: 'Find free slots in a google calendar for a specific time period',
    required_params: [],
    optional_params: {
      calendar_expansion_max: { type: 'integer', default: 50, description: 'Maximum number of calendars to expand' },
      group_expansion_max: { type: 'integer', default: 100, description: 'Maximum number of groups to expand' },
      items: { type: 'array', default: ['primary'], description: 'Calendar IDs to check for free time' },
      time_max: { type: 'string', description: 'Upper bound for the query (ISO format)' },
      time_min: { type: 'string', description: 'Lower bound for the query (ISO format)' },
      timezone: { type: 'string', default: 'UTC', description: 'Timezone for the query' }
    }
  },
  GOOGLECALENDAR_LIST_CALENDARS: {
    description: 'List all google calendars from the user\'s calendar list',
    required_params: [],
    optional_params: {
      max_results: { type: 'integer', default: 10, description: 'Maximum number of results' },
      min_access_role: { type: 'string', description: 'Minimum access role required' },
      page_token: { type: 'string', description: 'Token for pagination' },
      show_deleted: { type: 'boolean', description: 'Whether to show deleted calendars' },
      show_hidden: { type: 'boolean', description: 'Whether to show hidden calendars' },
      sync_token: { type: 'string', description: 'Token for synchronization' }
    }
  },
  GOOGLECALENDAR_GET_CALENDAR: {
    description: 'Fetch a calendar based on the provided calendar id',
    required_params: [],
    optional_params: {
      calendar_id: { type: 'string', default: 'primary', description: 'Calendar ID to fetch' }
    }
  },
  GOOGLECALENDAR_QUICK_ADD: {
    description: 'Create a new event based on a simple text string (fallback option)',
    required_params: ['text'],
    optional_params: {
      calendar_id: { type: 'string', default: 'primary', description: 'Calendar ID to create event in' },
      send_updates: { type: 'string', default: 'none', description: 'Whether to send updates' }
    }
  },
  GOOGLECALENDAR_REMOVE_ATTENDEE: {
    description: 'Remove an attendee from an existing event',
    required_params: ['event_id', 'attendee_email'],
    optional_params: {
      calendar_id: { type: 'string', default: 'primary', description: 'Calendar ID containing the event' }
    }
  },
  GOOGLECALENDAR_GET_CURRENT_DATE_TIME: {
    description: 'Get the current date and time of a specified timezone',
    required_params: [],
    optional_params: {
      timezone: { type: 'number', description: 'UTC offset value for timezone' }
    }
  }
};

/**
 * AI-Driven Google Calendar Sub-Agent Tool
 * 
 * The AI will analyze the task and choose the appropriate Google Calendar action with parameters.
 * No regex patterns - pure AI intelligence for intent detection and parameter inference.
 */
export const googleCalendarAgent = tool({
  description: `Intelligent Google Calendar assistant with full AI flexibility. 

AVAILABLE ACTIONS:
${Object.entries(GOOGLE_CALENDAR_ACTIONS).map(([action, config]) => 
  `- ${action}: ${config.description}
    Required: [${config.required_params.join(', ')}]
    Optional: [${Object.keys(config.optional_params).join(', ')}]`
).join('\n')}

CURRENT TIME CONTEXT: Use getCurrentTime tool if you need current date/time for calculations.

AI INSTRUCTIONS:
1. Analyze the user's natural language request
2. Choose the most appropriate action from the available actions above
3. Infer all required and relevant optional parameters
4. For dates/times: parse natural language like "tomorrow at 2pm", "next Monday", "from 2-4pm"
5. For event titles: extract meaningful titles from context
6. For searches: set appropriate timeMin/timeMax ranges
7. Be intelligent about parameter selection - use all available schema information`,
  
  parameters: z.object({
    action: z.string().describe('The Google Calendar action to execute (choose from the available actions listed above)'),
    parameters: z.record(z.any()).describe('All parameters for the chosen action. Include both required and relevant optional parameters based on the user request and action schema.'),
    reasoning: z.string().describe('Brief explanation of why you chose this action and these parameters'),
  }),

  execute: async ({ action, parameters, reasoning }) => {
    try {
      const session = await auth();

      if (!session || !session.user) {
        return {
          success: false,
          error: 'Authentication required',
          message: 'Please sign in to access your Google Calendar.',
        };
      }

      console.log(`🤖 AI Calendar Agent executing:`, {
        action,
        parameters,
        reasoning
      });

      // Validate the chosen action exists
      if (!GOOGLE_CALENDAR_ACTIONS[action as keyof typeof GOOGLE_CALENDAR_ACTIONS]) {
        return {
          success: false,
          error: 'Invalid action',
          message: `The action "${action}" is not a valid Google Calendar action.`,
          availableActions: Object.keys(GOOGLE_CALENDAR_ACTIONS),
        };
      }

      const actionConfig = GOOGLE_CALENDAR_ACTIONS[action as keyof typeof GOOGLE_CALENDAR_ACTIONS];
      console.log(`✅ Using action: ${action} - ${actionConfig.description}`);

      // Get user's connected services
      const connections = await getConnectionsByUserId(session.user.id!);

      if (!connections || connections.length === 0) {
        return {
          success: false,
          error: 'No connected services',
          message: 'You need to connect at least one service before executing calendar actions. Please connect your Google Calendar in the profile settings.',
          redirectUrl: "/profile/connections",
          selectedAction: action,
        };
      }

      // Find Google Calendar connection
      const appName = 'googlecalendar';
      const appConnections = connections.filter(conn =>
        conn.provider.toLowerCase() === appName &&
        conn.status === 'ACTIVE'
      );

      if (appConnections.length === 0) {
        return {
          success: false,
          error: 'Google Calendar not connected',
          message: 'Your Google Calendar account is not connected or the connection is inactive. Please connect your Google Calendar in the profile settings.',
          redirectUrl: "/profile/connections",
          selectedAction: action,
        };
      }

      // Use the most recently created connection
      const connection = appConnections.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];
      const connectionId = connection.connectionId;

      console.log(`📅 Using Google Calendar connection: ${connectionId}`);
      console.log(`🧠 AI provided parameters:`, JSON.stringify(parameters, null, 2));

      // Validate required parameters for the chosen action
      console.log(`🔍 Validating required parameters for ${action}...`);
      console.log(`🔍 Required params:`, actionConfig.required_params);
      console.log(`🔍 Provided params:`, Object.keys(parameters));

      const missingParams = actionConfig.required_params.filter((param: string) => {
        const hasParam = !!parameters[param];
        console.log(`🔍 Parameter "${param}": ${hasParam ? '✅ PROVIDED' : '❌ MISSING'}`);
        return !hasParam;
      });

      if (missingParams.length > 0) {
        console.log(`❌ Missing required parameters: ${missingParams.join(', ')}`);
        return {
          success: false,
          error: 'Missing required parameters',
          message: `I need more information to ${actionConfig.description.toLowerCase()}. Missing: ${missingParams.join(', ')}`,
          selectedAction: action,
          providedParams: parameters,
          missingParams,
          reasoning,
        };
      }

      console.log(`✅ All required parameters provided!`);

      // Execute the calendar action
      const toolset = new OpenAIToolSet();

      console.log(`🗓️ Calendar Agent: Executing ${action}`);
      console.log(`📋 AI provided parameters:`, parameters);
      console.log(`🔗 Using connection ID: ${connectionId}`);

      let result;
      try {
        console.log(`🚀 Executing API call to Composio...`);
        console.log(`🚀 Action: ${action}`);
        console.log(`🚀 App: ${appName}`);
        console.log(`🚀 Entity ID: ${session.user.id}`);
        console.log(`🚀 Connection ID: ${connectionId}`);
        console.log(`🚀 Input Parameters:`, JSON.stringify(parameters, null, 2));

        result = await toolset.client.actions.execute({
          actionName: action,
          requestBody: {
            appName: appName,
            input: parameters,
            connectedAccountId: connectionId
          }
        });

        console.log(`✅ API call successful!`);
        console.log(`✅ Raw result:`, JSON.stringify(result, null, 2));
      } catch (error) {
        const execError = error as Error;
        console.error(`❌ Calendar action execution failed:`, execError);

        if (execError.message.includes('401') || execError.message.includes('unauthorized')) {
          return {
            success: false,
            error: 'Google Calendar connection expired',
            message: 'Your Google Calendar connection appears to have expired or been invalidated. Please reconnect your Google Calendar account and try again.',
            redirectUrl: "/profile/connections",
            selectedAction: action,
          };
        }

        if (execError.message.includes('404') || execError.message.includes('not found')) {
          return {
            success: false,
            error: 'Calendar action not found',
            message: `The calendar operation "${action}" could not be found. This might be a temporary issue.`,
            selectedAction: action,
          };
        }

        return {
          success: false,
          error: 'Calendar operation failed',
          message: `Failed to ${actionConfig.description.toLowerCase()}: ${execError.message}`,
          selectedAction: action,
          providedParams: parameters,
        };
      }

      // Process and format the result
      console.log(`🔍 Processing result...`);
      console.log(`🔍 Result structure:`, {
        hasResult: !!result,
        successful: result?.successful,
        hasData: !!result?.data,
        hasError: !!result?.error,
        keys: result ? Object.keys(result) : []
      });

      const success = result?.successful !== false;
      console.log(`🔍 Success determination: ${success}`);

      if (!success) {
        console.log(`❌ Operation failed!`);
        console.log(`❌ Error:`, result?.error);
        console.log(`❌ Full result:`, result);

        return {
          success: false,
          error: result?.error || 'Unknown error',
          message: `Failed to ${actionConfig.description.toLowerCase()}: ${result?.error || 'Unknown error occurred'}`,
          selectedAction: action,
          providedParams: parameters,
          rawResult: result,
        };
      }

      // Format success response
      console.log(`✅ Operation successful! Formatting response...`);

      let formattedMessage = '';
      let resultData = result?.data || result;

      console.log(`📊 Result data:`, resultData);
      console.log(`📊 Action for formatting: ${action}`);

      // Simple response formatting based on action
      if (action.includes('CREATE_EVENT')) {
        const eventTitle = parameters.summary || 'event';
        formattedMessage = `✅ Successfully created calendar event "${eventTitle}"`;
        if (parameters.start_datetime) {
          const startTime = new Date(parameters.start_datetime);
          formattedMessage += ` for ${startTime.toLocaleDateString()} at ${startTime.toLocaleTimeString()}`;
        }
      } else if (action.includes('FIND_EVENT') || action.includes('LIST')) {
        const events = resultData?.items || [];
        if (Array.isArray(events)) {
          formattedMessage = `📅 Found ${events.length} event${events.length === 1 ? '' : 's'}`;
        } else {
          formattedMessage = `📅 Retrieved calendar information`;
        }
      } else if (action.includes('UPDATE_EVENT') || action.includes('PATCH_EVENT')) {
        formattedMessage = `✅ Successfully updated calendar event`;
        if (parameters.event_id) {
          formattedMessage += ` (ID: ${parameters.event_id})`;
        }
      } else if (action.includes('DELETE_EVENT')) {
        formattedMessage = `✅ Successfully deleted calendar event`;
        if (parameters.event_id) {
          formattedMessage += ` (ID: ${parameters.event_id})`;
        }
      } else {
        formattedMessage = `✅ Successfully completed calendar operation: ${actionConfig.description}`;
      }

      const finalResponse = {
        success: true,
        message: formattedMessage,
        action: action,
        selectedAction: action,
        providedParams: parameters,
        result: resultData,
        reasoning: reasoning,
      };

      console.log(`🎉 Calendar Agent completed successfully!`);
      console.log(`🎉 Final response:`, JSON.stringify(finalResponse, null, 2));

      return finalResponse;

    } catch (error) {
      console.error('Calendar Agent error:', error);
      return {
        success: false,
        error: 'Calendar agent error',
        message: `An unexpected error occurred while processing your calendar request: ${error instanceof Error ? error.message : 'Unknown error'}`,
        selectedAction: action,
        providedParams: parameters,
      };
    }
  },
});
