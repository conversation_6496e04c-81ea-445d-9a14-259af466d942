import { LanguageModelV1Middleware } from 'ai';

/**
 * Enhanced tool middleware that allows for more parallel tool execution
 * while still maintaining some control over the flow
 */
export const enhancedToolMiddleware = (): LanguageModelV1Middleware => {
  // Keep track of which tools have been used in the current conversation
  const toolUsageState = new Map<string, {
    toolsInProgress: Set<string>,
    lastToolName: string | null,
    pendingAnalysis: boolean,
    searchCount: number,
    extractCount: number,
    lastSearchQuery: string | null,
    lastExtractUrl: string | null
  }>();

  return async (params: any, runModel: any) => {
    // Extract conversation ID or create one if not present
    const conversationId = params.id || 'default';

    // Initialize state for this conversation if not exists
    if (!toolUsageState.has(conversationId)) {
      toolUsageState.set(conversationId, {
        toolsInProgress: new Set(),
        lastToolName: null,
        pendingAnalysis: false,
        searchCount: 0,
        extractCount: 0,
        lastSearchQuery: null,
        lastExtractUrl: null
      });
    }

    const state = toolUsageState.get(conversationId)!;

    // Check if this is a tool call message
    if (params.messages.length > 0) {
      const lastMessage = params.messages[params.messages.length - 1];

      // If the last message is from the assistant and has tool calls
      if (lastMessage.role === 'assistant' && lastMessage.toolCalls && lastMessage.toolCalls.length > 0) {
        // If we're in analysis mode, let it proceed (no tool calls will be made)
        if (state.pendingAnalysis) {
          state.pendingAnalysis = false;
          return runModel(params);
        }

        const toolCalls = lastMessage.toolCalls;

        // Handle search tool calls
        const searchToolCalls = toolCalls.filter((call: any) =>
          call.name === 'searchInternet'
        );

        // Handle extract page tool calls
        const extractToolCalls = toolCalls.filter((call: any) =>
          call.name === 'extractPage'
        );

        // Handle fetch actions tool calls
        const fetchActionsToolCalls = toolCalls.filter((call: any) =>
          call.name === 'fetchActions'
        );

        // Handle execute action tool calls
        const executeActionToolCalls = toolCalls.filter((call: any) =>
          call.name === 'executeAction'
        );

        // Handle VIPER execute tool calls
        const viperExecuteToolCalls = toolCalls.filter((call: any) =>
          call.name === 'viperExecute'
        );

        // Handle verify result tool calls
        const verifyResultToolCalls = toolCalls.filter((call: any) =>
          call.name === 'verifyResult'
        );

        // Allow multiple search calls if they have different queries
        // But limit to 3 parallel searches to prevent overwhelming the system
        if (searchToolCalls.length > 3) {
          // Keep only the first 3 search tool calls
          const keptSearchToolCalls = searchToolCalls.slice(0, 3);
          const otherToolCalls = toolCalls.filter((call: any) =>
            call.name !== 'searchInternet'
          );

          lastMessage.toolCalls = [...otherToolCalls, ...keptSearchToolCalls];

          // Mark that we need to analyze results before proceeding
          state.pendingAnalysis = true;
        } else if (searchToolCalls.length > 1) {
          // Check for duplicate queries
          const searchQueries = new Set<string>();
          const uniqueSearchCalls = [];

          for (const call of searchToolCalls) {
            try {
              const query = JSON.parse(call.args).query;
              if (!searchQueries.has(query)) {
                searchQueries.add(query);
                uniqueSearchCalls.push(call);
              }
            } catch (e) {
              // If we can't parse the args, keep the call
              uniqueSearchCalls.push(call);
            }
          }

          if (uniqueSearchCalls.length < searchToolCalls.length) {
            // Replace with only unique search calls
            const otherToolCalls = toolCalls.filter((call: any) =>
              call.name !== 'searchInternet'
            );

            lastMessage.toolCalls = [...otherToolCalls, ...uniqueSearchCalls];
          }
        }

        // Allow multiple extract calls if they have different URLs
        // But limit to 3 parallel extracts to prevent overwhelming the system
        if (extractToolCalls.length > 3) {
          // Keep only the first 3 extract tool calls
          const keptExtractToolCalls = extractToolCalls.slice(0, 3);
          const otherToolCalls = toolCalls.filter((call: any) =>
            call.name !== 'extractPage'
          );

          lastMessage.toolCalls = [...otherToolCalls, ...keptExtractToolCalls];

          // Mark that we need to analyze results before proceeding
          state.pendingAnalysis = true;
        } else if (extractToolCalls.length > 1) {
          // Check for duplicate URLs
          const extractUrls = new Set<string>();
          const uniqueExtractCalls = [];

          for (const call of extractToolCalls) {
            try {
              const url = JSON.parse(call.args).url;
              if (!extractUrls.has(url)) {
                extractUrls.add(url);
                uniqueExtractCalls.push(call);
              }
            } catch (e) {
              // If we can't parse the args, keep the call
              uniqueExtractCalls.push(call);
            }
          }

          if (uniqueExtractCalls.length < extractToolCalls.length) {
            // Replace with only unique extract calls
            const otherToolCalls = toolCalls.filter((call: any) =>
              call.name !== 'extractPage'
            );

            lastMessage.toolCalls = [...otherToolCalls, ...uniqueExtractCalls];
          }
        }

        // For fetchActions, allow only one call at a time
        if (fetchActionsToolCalls.length > 1) {
          // Keep only the first fetchActions call
          const firstFetchActionsCall = fetchActionsToolCalls[0];
          const otherToolCalls = toolCalls.filter((call: any) =>
            call.name !== 'fetchActions'
          );

          lastMessage.toolCalls = [...otherToolCalls, firstFetchActionsCall];

          // Mark that we need to analyze results before proceeding
          state.pendingAnalysis = true;
        }

        // For executeAction, allow multiple calls if they're for different actions
        if (executeActionToolCalls.length > 1) {
          // Check for duplicate actions
          const actionNames = new Set<string>();
          const uniqueExecuteCalls = [];

          for (const call of executeActionToolCalls) {
            try {
              const action = JSON.parse(call.args).action;
              if (!actionNames.has(action)) {
                actionNames.add(action);
                uniqueExecuteCalls.push(call);
              }
            } catch (e) {
              // If we can't parse the args, keep the call
              uniqueExecuteCalls.push(call);
            }
          }

          if (uniqueExecuteCalls.length < executeActionToolCalls.length) {
            // Replace with only unique execute calls
            const otherToolCalls = toolCalls.filter((call: any) =>
              call.name !== 'executeAction'
            );

            lastMessage.toolCalls = [...otherToolCalls, ...uniqueExecuteCalls];
          }
        }

        // For viperExecute, prioritize it over regular executeAction
        if (viperExecuteToolCalls.length > 0 && executeActionToolCalls.length > 0) {
          // Keep VIPER calls and remove regular execute calls
          const nonExecuteToolCalls = toolCalls.filter((call: any) =>
            call.name !== 'executeAction'
          );

          lastMessage.toolCalls = nonExecuteToolCalls;

          // Mark that we need to analyze results before proceeding
          state.pendingAnalysis = true;
        }

        // For viperExecute, allow multiple calls if they're for different intents
        if (viperExecuteToolCalls.length > 1) {
          // Check for duplicate intents
          const intents = new Set<string>();
          const uniqueViperCalls = [];

          for (const call of viperExecuteToolCalls) {
            try {
              const intent = JSON.parse(call.args).intent;
              if (!intents.has(intent)) {
                intents.add(intent);
                uniqueViperCalls.push(call);
              }
            } catch (e) {
              // If we can't parse the args, keep the call
              uniqueViperCalls.push(call);
            }
          }

          if (uniqueViperCalls.length < viperExecuteToolCalls.length) {
            // Replace with only unique VIPER calls
            const otherToolCalls = toolCalls.filter((call: any) =>
              call.name !== 'viperExecute'
            );

            lastMessage.toolCalls = [...otherToolCalls, ...uniqueViperCalls];
          }
        }
      }

      // Optimized: Reduce forced analysis to prevent mid-task interruptions
      // Only force analysis for critical tools that absolutely need it
      if (lastMessage.role === 'tool') {
        // Only set pendingAnalysis for tools that absolutely require careful analysis
        if (lastMessage.name === 'viperExecute' ||
            lastMessage.name === 'verifyResult') {
          state.pendingAnalysis = true;
        }
        // Remove fetchActions and executeAction from forced analysis to improve flow
      }
    }

    return runModel(params);
  };
};
