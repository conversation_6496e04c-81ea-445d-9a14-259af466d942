import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { db } from '@/lib/db/client';
import { memory } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { generateEmbedding } from '@/lib/memory/embeddings';

export async function PATCH(request: Request) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the request body
    const { id, content, category } = await request.json();

    // Validate the request
    if (!id) {
      return NextResponse.json(
        { error: 'Memory ID is required' },
        { status: 400 }
      );
    }

    if (!content) {
      return NextResponse.json(
        { error: 'Content is required' },
        { status: 400 }
      );
    }

    // Generate new embedding for the updated content
    const embedding = await generateEmbedding(content);

    // Update the memory
    const result = await db
      .update(memory)
      .set({
        content,
        category: category || null,
        embedding: JSON.stringify(embedding),
      })
      .where(eq(memory.id, id))
      .returning();

    // Check if the memory was found and updated
    if (!result || result.length === 0) {
      return NextResponse.json({ error: 'Memory not found' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      memory: result[0],
      message: 'Memory updated successfully'
    });
  } catch (error) {
    console.error('Error updating memory:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
