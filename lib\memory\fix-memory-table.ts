/**
 * <PERSON><PERSON><PERSON> to manually fix the Memory table
 * Run with: npx tsx lib/memory/fix-memory-table.ts
 */
import { config } from 'dotenv';
import postgres from 'postgres';
import fs from 'fs';
import path from 'path';

// Load environment variables
config({
  path: '.env.local',
});

async function fixMemoryTable() {
  if (!process.env.POSTGRES_URL) {
    throw new Error('POSTGRES_URL environment variable is not defined');
  }

  // Create a database client
  const sql = postgres(process.env.POSTGRES_URL, {
    max: 1,
    idle_timeout: 20,
    connect_timeout: 10,
  });

  try {
    console.log('🔧 Fixing Memory table...');

    // Execute the SQL statements directly
    console.log('Dropping Memory table...');
    await sql.unsafe(`DROP TABLE IF EXISTS "Memory"`);

    console.log('Creating Memory table...');
    await sql.unsafe(`
      CREATE TABLE IF NOT EXISTS "Memory" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "userId" uuid NOT NULL,
        "content" text NOT NULL,
        "embedding" text NOT NULL,
        "createdAt" timestamp DEFAULT now() NOT NULL,
        "isActive" boolean DEFAULT true NOT NULL,
        "category" text
      )
    `);

    console.log('Adding foreign key constraint...');
    try {
      await sql.unsafe(`
        ALTER TABLE "Memory" ADD CONSTRAINT "Memory_userId_User_id_fk"
        FOREIGN KEY ("userId") REFERENCES "public"."User"("id")
        ON DELETE no action ON UPDATE no action
      `);
    } catch (error) {
      console.log('Foreign key constraint already exists or failed to add');
    }

    console.log('Creating index...');
    await sql.unsafe(`
      CREATE INDEX IF NOT EXISTS "Memory_userId_idx" ON "Memory" ("userId")
    `);

    // Verify the table structure
    const tableInfo = await sql`
      SELECT column_name, data_type
      FROM information_schema.columns
      WHERE table_name = 'Memory'
      ORDER BY ordinal_position;
    `;

    console.log('\n✅ Memory table structure:');
    tableInfo.forEach((column: any) => {
      console.log(`- ${column.column_name}: ${column.data_type}`);
    });

    console.log('\n✅ Memory table fixed successfully!');
  } catch (error) {
    console.error('❌ Error fixing Memory table:', error);
  } finally {
    // Close the database connection
    await sql.end();
  }
}

// Run the script
fixMemoryTable().catch(console.error);
