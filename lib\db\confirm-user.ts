'use server';

import postgres from 'postgres';
import { updateUserConfirmedStatus } from './queries';

/**
 * Directly updates a user's confirmation status in the database using raw SQL
 * This is a more reliable approach than using the ORM
 */
export async function confirmUserDirectly(email: string): Promise<boolean> {
  try {
    console.log(`Directly confirming user: ${email}`);
    
    // Create a direct database connection
    const client = postgres(process.env.POSTGRES_URL!);
    
    try {
      // First, check if the user exists
      const checkUser = await client`
        SELECT "id", "email", "confirmed" 
        FROM "User" 
        WHERE "email" = ${email}
      `;
      
      console.log('User check result:', checkUser);
      
      if (checkUser.length === 0) {
        console.error('No user found with email:', email);
        return false;
      }
      
      // Execute a direct SQL update
      const result = await client`
        UPDATE "User" 
        SET "confirmed" = true 
        WHERE "email" = ${email}
        RETURNING "id", "email", "confirmed"
      `;
      
      console.log('Direct update result:', result);
      
      // Also try the ORM approach as a backup
      try {
        await updateUserConfirmedStatus({ email, confirmed: true });
      } catch (ormError) {
        console.error('ORM update failed, but direct SQL succeeded:', ormError);
      }
      
      return result.length > 0 && result[0].confirmed === true;
    } finally {
      // Always close the connection
      await client.end();
    }
  } catch (error) {
    console.error('Error directly confirming user:', error);
    return false;
  }
}
