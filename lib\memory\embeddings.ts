/**
 * Functions for generating and working with embeddings for the memory system
 */
import { GoogleGenerativeAI } from '@google/generative-ai';
import crypto from 'crypto';

/**
 * Generates an embedding vector for the given text using Google's embedding model
 * @param text The text to generate an embedding for
 * @returns A vector of floating point numbers representing the text embedding
 */
export async function generateEmbedding(text: string): Promise<number[]> {
  try {
    // Initialize the Google AI SDK
    const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

    // Get the embedding model
    const embeddingModel = genAI.getGenerativeModel({ model: "embedding-001" });

    // Generate embedding
    const result = await embeddingModel.embedContent(text);

    // Extract the embedding values
    const embedding = result.embedding.values;

    return embedding;
  } catch (error) {
    console.error('Error in generateEmbedding:', error);

    // Provide more detailed error information
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
    }

    // Use fallback method if Google AI fails
    console.log('Using fallback embedding method');
    return generateFallbackEmbedding(text);
  }
}

/**
 * Generates a fallback embedding using a simple hash-based approach
 * This is not as good as a real embedding model but can work as a temporary fallback
 * @param text The text to generate an embedding for
 * @returns A vector of numbers representing the text
 */
function generateFallbackEmbedding(text: string): number[] {
  // Create a deterministic but simple embedding
  // This is NOT a proper embedding but allows the system to continue functioning
  const hash = crypto.createHash('sha256').update(text).digest('hex');

  // Create a 128-dimensional vector from the hash
  const embedding = new Array(128).fill(0);

  // Fill the embedding with values derived from the hash
  for (let i = 0; i < hash.length - 1; i++) {
    const value = parseInt(hash.substring(i, i + 2), 16);
    const index = i % embedding.length;
    embedding[index] = (value / 255) * 2 - 1; // Scale to range [-1, 1]
  }

  return embedding;
}

/**
 * Calculates the cosine similarity between two vectors
 * @param a First vector
 * @param b Second vector
 * @returns Cosine similarity score between -1 and 1
 */
export function cosineSimilarity(a: number[], b: number[]): number {
  if (a.length !== b.length) {
    throw new Error('Vectors must have the same length');
  }

  let dotProduct = 0;
  let normA = 0;
  let normB = 0;

  for (let i = 0; i < a.length; i++) {
    dotProduct += a[i] * b[i];
    normA += a[i] * a[i];
    normB += b[i] * b[i];
  }

  normA = Math.sqrt(normA);
  normB = Math.sqrt(normB);

  if (normA === 0 || normB === 0) {
    return 0;
  }

  return dotProduct / (normA * normB);
}
