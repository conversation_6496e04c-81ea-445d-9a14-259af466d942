/**
 * Create Composio Integration Script
 * 
 * This script helps you create integrations in Composio for different providers
 * Run it with: node scripts/create-composio-integration.js [provider]
 * 
 * Example: node scripts/create-composio-integration.js googlecalendar
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { OpenAIToolSet } = require('composio-core');

// Get the provider from command line arguments
const provider = process.argv[2];
if (!provider) {
  console.error('Please specify a provider (e.g., github, gmail, googlecalendar, notion, jira)');
  process.exit(1);
}

// Normalize provider name
const normalizedProvider = provider.toLowerCase();

// Check if we have OAuth credentials for this provider
const clientIdEnvVar = `OAUTH_${normalizedProvider.toUpperCase()}_CLIENT_ID`;
const clientSecretEnvVar = `OAUTH_${normalizedProvider.toUpperCase()}_CLIENT_SECRET`;

const clientId = process.env[clientIdEnvVar];
const clientSecret = process.env[clientSecretEnvVar];

// Initialize the Composio toolset
const toolset = new OpenAIToolSet();

async function createIntegration() {
  try {
    console.log(`Creating integration for ${normalizedProvider}...`);
    
    // Get the callback URL
    const callbackUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/oauth-callback`;
    
    let integration;
    
    if (clientId && clientSecret) {
      // Create a custom integration with our own OAuth credentials
      console.log(`Using custom OAuth credentials for ${normalizedProvider}`);
      integration = await toolset.integrations.create({
        appUniqueKey: normalizedProvider,
        name: `Custom ${normalizedProvider}`,
        authScheme: "OAUTH2",
        useComposioAuth: false,
        authConfig: {
          client_id: clientId,
          client_secret: clientSecret,
          redirect_uri: callbackUrl,
        },
      });
    } else {
      // Create an integration using Composio's OAuth credentials
      console.log(`Using Composio's OAuth credentials for ${normalizedProvider}`);
      integration = await toolset.integrations.create({
        appUniqueKey: normalizedProvider,
        name: `${normalizedProvider} Integration`,
        authScheme: "OAUTH2",
        useComposioAuth: true,
      });
    }
    
    console.log('Integration created successfully!');
    console.log('Integration ID:', integration.id);
    console.log(`Add this to your .env.local file as COMPOSIO_${normalizedProvider.toUpperCase()}_INTEGRATION_ID=${integration.id}`);
    
    return integration;
  } catch (error) {
    console.error('Error creating integration:', error);
  }
}

// Run the function
createIntegration();
