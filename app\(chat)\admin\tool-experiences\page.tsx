'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Trash2, Plus, Edit, Save, X } from 'lucide-react';

interface ToolExperience {
  id: string;
  experienceType: string;
  appName: string;
  actionName: string | null;
  contextTags: string[];
  experienceContent: string;
  successRate: number;
  usageCount: number;
  priority: string;
  createdAt: string;
  lastUsed: string | null;
}

export default function ToolExperiencesAdmin() {
  const [experiences, setExperiences] = useState<ToolExperience[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newExperience, setNewExperience] = useState({
    experienceType: 'pattern',
    appName: '',
    actionName: '',
    contextTags: '',
    experienceContent: '',
    successRate: 1.0,
    priority: 'medium'
  });

  useEffect(() => {
    fetchExperiences();
  }, []);

  const fetchExperiences = async () => {
    try {
      const response = await fetch('/api/admin/tool-experiences');
      if (response.ok) {
        const data = await response.json();
        setExperiences(data);
      }
    } catch (error) {
      console.error('Failed to fetch tool experiences:', error);
    } finally {
      setLoading(false);
    }
  };

  const createExperience = async () => {
    try {
      const response = await fetch('/api/admin/tool-experiences', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...newExperience,
          contextTags: newExperience.contextTags.split(',').map(tag => tag.trim()).filter(Boolean),
          actionName: newExperience.actionName || null
        })
      });

      if (response.ok) {
        await fetchExperiences();
        setShowCreateForm(false);
        setNewExperience({
          experienceType: 'pattern',
          appName: '',
          actionName: '',
          contextTags: '',
          experienceContent: '',
          successRate: 1.0,
          priority: 'medium'
        });
      }
    } catch (error) {
      console.error('Failed to create tool experience:', error);
    }
  };

  const deleteExperience = async (id: string) => {
    if (!confirm('Are you sure you want to delete this experience?')) return;

    try {
      const response = await fetch(`/api/admin/tool-experiences/${id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        await fetchExperiences();
      }
    } catch (error) {
      console.error('Failed to delete tool experience:', error);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-blue-100 text-blue-800';
      case 'low': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'workflow': return 'bg-purple-100 text-purple-800';
      case 'pattern': return 'bg-green-100 text-green-800';
      case 'advice': return 'bg-yellow-100 text-yellow-800';
      case 'insight': return 'bg-indigo-100 text-indigo-800';
      case 'parameter_combo': return 'bg-pink-100 text-pink-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return <div className="p-6">Loading tool experiences...</div>;
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Tool Experiences Management</h1>
          <p className="text-gray-600 mt-2">
            Manage AI agent learning patterns and strategic insights. These experiences help the AI perform better by applying the 80/20 rule.
          </p>
        </div>
        <Button onClick={() => setShowCreateForm(true)} className="flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Add Experience
        </Button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{experiences.length}</div>
            <div className="text-sm text-gray-600">Total Experiences</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {experiences.filter(e => e.priority === 'critical').length}
            </div>
            <div className="text-sm text-gray-600">Critical Priority</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {experiences.reduce((sum, e) => sum + e.usageCount, 0)}
            </div>
            <div className="text-sm text-gray-600">Total Usage</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {(experiences.reduce((sum, e) => sum + e.successRate, 0) / experiences.length * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600">Avg Success Rate</div>
          </CardContent>
        </Card>
      </div>

      {/* Create Form */}
      {showCreateForm && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Create New Tool Experience</CardTitle>
            <CardDescription>
              Add strategic insights that will help the AI agent perform better
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="experienceType">Experience Type</Label>
                <Select value={newExperience.experienceType} onValueChange={(value) => 
                  setNewExperience(prev => ({ ...prev, experienceType: value }))
                }>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pattern">Pattern</SelectItem>
                    <SelectItem value="workflow">Workflow</SelectItem>
                    <SelectItem value="advice">Advice</SelectItem>
                    <SelectItem value="insight">Insight</SelectItem>
                    <SelectItem value="parameter_combo">Parameter Combo</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="appName">App Name</Label>
                <Input
                  value={newExperience.appName}
                  onChange={(e) => setNewExperience(prev => ({ ...prev, appName: e.target.value }))}
                  placeholder="github, gmail, notion, etc."
                />
              </div>
              <div>
                <Label htmlFor="actionName">Action Name (Optional)</Label>
                <Input
                  value={newExperience.actionName}
                  onChange={(e) => setNewExperience(prev => ({ ...prev, actionName: e.target.value }))}
                  placeholder="GITHUB_LIST_REPOSITORIES"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="contextTags">Context Tags (comma-separated)</Label>
              <Input
                value={newExperience.contextTags}
                onChange={(e) => setNewExperience(prev => ({ ...prev, contextTags: e.target.value }))}
                placeholder="repo_management, search, documentation"
              />
            </div>
            <div>
              <Label htmlFor="experienceContent">Experience Content</Label>
              <Textarea
                value={newExperience.experienceContent}
                onChange={(e) => setNewExperience(prev => ({ ...prev, experienceContent: e.target.value }))}
                placeholder="Describe the strategic insight, pattern, or advice..."
                rows={3}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="priority">Priority</Label>
                <Select value={newExperience.priority} onValueChange={(value) => 
                  setNewExperience(prev => ({ ...prev, priority: value }))
                }>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="critical">Critical</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="successRate">Success Rate</Label>
                <Input
                  type="number"
                  min="0"
                  max="1"
                  step="0.01"
                  value={newExperience.successRate}
                  onChange={(e) => setNewExperience(prev => ({ ...prev, successRate: parseFloat(e.target.value) }))}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button onClick={createExperience}>Create Experience</Button>
              <Button variant="outline" onClick={() => setShowCreateForm(false)}>Cancel</Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Experiences List */}
      <div className="space-y-4">
        {experiences.map((experience) => (
          <Card key={experience.id}>
            <CardContent className="p-4">
              <div className="flex justify-between items-start mb-3">
                <div className="flex gap-2 flex-wrap">
                  <Badge className={getTypeColor(experience.experienceType)}>
                    {experience.experienceType}
                  </Badge>
                  <Badge className={getPriorityColor(experience.priority)}>
                    {experience.priority}
                  </Badge>
                  <Badge variant="outline">{experience.appName}</Badge>
                  {experience.actionName && (
                    <Badge variant="secondary">{experience.actionName}</Badge>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setEditingId(experience.id)}
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deleteExperience(experience.id)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              
              <p className="text-sm mb-3">{experience.experienceContent}</p>
              
              <div className="flex gap-4 text-xs text-gray-500">
                <span>Success Rate: {(experience.successRate * 100).toFixed(1)}%</span>
                <span>Used: {experience.usageCount} times</span>
                <span>Created: {new Date(experience.createdAt).toLocaleDateString()}</span>
                {experience.lastUsed && (
                  <span>Last Used: {new Date(experience.lastUsed).toLocaleDateString()}</span>
                )}
              </div>
              
              {experience.contextTags.length > 0 && (
                <div className="mt-2">
                  <span className="text-xs text-gray-500">Tags: </span>
                  {experience.contextTags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs mr-1">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
