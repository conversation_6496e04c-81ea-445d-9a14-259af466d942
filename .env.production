# This file is used by Vercel in production
# The actual values will be set in the Vercel dashboard

# Set this to true to skip database checks during session validation
CHECK_USER_STATUS=false

# Set this to 1 to indicate we're in Vercel environment
VERCEL=1

# Generate a random secret: use openssl rand -base64 32
AUTH_SECRET=LkiqRZQyoTnbZ8+QkJQNzm1qq4Orjb/93yXWGsGWmpA=

# Get from Google AI Studio: https://makersuite.google.com/app/apikey
GOOGLE_GENERATIVE_AI_API_KEY=AIzaSyBB3NN4eJvC9XcsnlCS9jVND7QpaU83Kvo

# Get from Vercel Blob
BLOB_READ_WRITE_TOKEN=vercel_blob_rw_nn8FiEGNZsmkxXVl_PhzmdhWBmt4QEBZWY5E93p24XlEwio

# Supabase connection variables
NEXT_PUBLIC_SUPABASE_URL=https://bxqbhixidzcxkhvelxme.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ4cWJoaXhpZHpjeGtodmVseG1lIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUwMTc0MTUsImV4cCI6MjA2MDU5MzQxNX0.WSgg48CVRGwQN3RJmKGKqCtG3iUEE8AHWiGmU0D2jwo

# Direct database connection (for migrations)
POSTGRES_URL=postgresql://postgres.bxqbhixidzcxkhvelxme:<EMAIL>:5432/postgres

SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ4cWJoaXhpZHpjeGtodmVseG1lIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTAxNzQxNSwiZXhwIjoyMDYwNTkzNDE1fQ.7fprD3EgWw9n9PHPVWQ1ReUQtTUyaNaOQiIXtKn3vSs

# Site URL for redirects (in development, use localhost)
NEXT_PUBLIC_SITE_URL=https://super-assistant-idk5.vercel.app

# Controls whether to check user status in the database during session validation
# Set to 'true' to enable strict validation, 'false' to disable (useful during DB issues)
CHECK_USER_STATUS=false

# Tavily API key for internet search and page extraction
TAVILY_API_KEY=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

# Composio API Key - Get this from https://app.composio.dev/developers
COMPOSIO_API_KEY=9a77z81twxfe61kbnkbu4

# COMPOSIO_LOGGING_LEVEL=debug

# Composio GitHub Integration ID - Get this from your Composio dashboard
COMPOSIO_GITHUB_INTEGRATION_ID=64ae170f-cc0b-4c3b-8ca9-01737b098390

# Composio Gmail Integration ID - Get this from your Composio dashboard
COMPOSIO_GMAIL_INTEGRATION_ID=9270c4a0-201a-42c7-99e2-4609c43056fa

# Composio Google Calendar Integration ID - Get this from your Composio dashboard
COMPOSIO_GOOGLECALENDAR_INTEGRATION_ID=80b951bf-dc23-46c0-add5-c6d7b2c21d36

# Composio Notion Integration ID - Get this from your Composio dashboard
COMPOSIO_NOTION_INTEGRATION_ID=879a0402-ebec-4536-ab2f-0d1c8d15d972

# Composio Jira Integration ID - Get this from your Composio dashboard
COMPOSIO_JIRA_INTEGRATION_ID=1cbd1578-ae03-4ebf-acc6-e50366d921af

# OAuth credentials for white-labeling (using the same GitHub credentials)
# OAUTH_GITHUB_CLIENT_ID=********************
# OAUTH_GITHUB_CLIENT_SECRET=d3c45b2f67148f0a8be4c3eb408de09d373fb7fc

# Gmail OAuth Credentials
# OAUTH_GMAIL_CLIENT_ID=155396585977-6he1ut97k17d1mbdefii3peo5tu50mqv.apps.googleusercontent.com
# OAUTH_GMAIL_CLIENT_SECRET=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

COMPOSIO_CLI=61195a26-6df4-47fc-8ed5-9dc94d64bcb1

ABSTRACT_API_KEY=d6999918147a47f19b9b2f571295bbfe