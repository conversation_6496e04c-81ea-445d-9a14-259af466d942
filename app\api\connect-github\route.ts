import { OpenAIToolSet } from "composio-core";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    console.log("Initiating GitHub connection...");

    // Initialize the Composio toolset using OpenAIToolSet
    const toolset = new OpenAIToolSet();
    console.log("Toolset initialized");

    // For simplicity, we're using a default entity ID
    // In a real app, you'd use the authenticated user's ID
    const userIdentifier = "default_user";

    // Get the entity for the user
    console.log("Getting entity for user:", userIdentifier);
    const entity = await toolset.getEntity(userIdentifier);
    console.log("Entity retrieved");

    // Initiate the GitHub connection using the app name
    console.log("Initiating GitHub connection with app name");
    const connectionRequest = await entity.initiateConnection({
      appName: "github"
    });

    console.log("GitHub connection initiated, redirect URL:", connectionRequest.redirectUrl);

    // Return the redirect URL
    return NextResponse.json({
      success: true,
      redirectUrl: connectionRequest.redirectUrl,
    });
  } catch (error) {
    console.error("Error initiating GitHub connection:", error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
    }, { status: 500 });
  }
}
