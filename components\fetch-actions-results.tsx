'use client';

import { useState } from 'react';
import { SearchIcon } from 'lucide-react';
import { ToolUsageCard } from './ui/tool-usage-card';

interface ActionTool {
  name: string;
  description?: string;
  appName?: string;
  function?: {
    name: string;
    description?: string;
  };
}

interface FetchActionsResultsProps {
  actionsData?: {
    actions: ActionTool[];
    message: string;
    connectedApps: string[];
    searchDescription: string;
    enhancedQuery?: string;
    toolsFetched: number;
    success: boolean;
    isExecuting?: boolean;
  };
}

export function FetchActionsResults({ actionsData }: FetchActionsResultsProps) {
  const [showActions, setShowActions] = useState(false);

  if (!actionsData) {
    // Loading state
    return (
      <ToolUsageCard
        icon={SearchIcon}
        toolName="Fetch Actions"
        status="loading"
        isExecuting={true}
      >
        <div className="mt-1 h-5 w-3/4 animate-pulse rounded-md bg-muted"></div>
        <div className="mt-2 h-4 w-full animate-pulse rounded-md bg-muted"></div>
        <div className="mt-1 h-4 w-1/2 animate-pulse rounded-md bg-muted"></div>
      </ToolUsageCard>
    );
  }

  // Handle error cases
  if (!actionsData.success || !actionsData.actions || actionsData.actions.length === 0) {
    return (
      <ToolUsageCard
        icon={SearchIcon}
        toolName="Fetch Actions"
        status="error"
        query={actionsData.searchDescription}
        errorMessage={actionsData.message || "No relevant actions found"}
        isExecuting={actionsData.isExecuting}
      >
        {actionsData.enhancedQuery && (
          <div className="text-xs text-muted-foreground italic mt-1 border-l-2 border-amber-400 pl-2">
            Enhanced: "{actionsData.enhancedQuery}"
          </div>
        )}

        {actionsData.connectedApps && actionsData.connectedApps.length > 0 && (
          <div className="text-xs text-muted-foreground mt-1">
            Connected apps: {actionsData.connectedApps.join(', ')}
          </div>
        )}
      </ToolUsageCard>
    );
  }

  // Group actions by app
  const actionsByApp: Record<string, ActionTool[]> = {};
  actionsData.actions.forEach(action => {
    const appName = action.appName ||
                   (action.name && action.name.includes('_') ? action.name.split('_')[0].toLowerCase() : 'other') ||
                   (action.function?.name && action.function.name.includes('_') ? action.function.name.split('_')[0].toLowerCase() : 'other');

    if (!actionsByApp[appName]) {
      actionsByApp[appName] = [];
    }
    actionsByApp[appName].push(action);
  });

  return (
    <ToolUsageCard
      icon={SearchIcon}
      toolName="Fetch Actions"
      query={actionsData.searchDescription}
      status="success"
      showContent={showActions}
      onToggleContent={() => setShowActions(!showActions)}
      isExecuting={actionsData.isExecuting}
    >
      <div className="space-y-3">
        <div className="text-sm">
          Found {actionsData.toolsFetched} relevant actions from {Object.keys(actionsByApp).length} connected services
        </div>

        <div className="space-y-3">
          {Object.entries(actionsByApp).map(([appName, actions]) => (
            <div key={appName} className="border rounded-md p-3 bg-background">
              <h3 className="font-medium text-sm mb-2 uppercase">{appName}</h3>
              <ul className="space-y-2">
                {actions.map((action, index) => {
                  const actionName = action.name || action.function?.name || 'Unknown action';
                  const actionDescription = action.description || action.function?.description || '';

                  return (
                    <li key={index} className="text-xs">
                      <div className="font-medium">{actionName.replace(/_/g, ' ').toLowerCase()}</div>
                      {actionDescription && (
                        <div className="text-muted-foreground mt-1">{actionDescription}</div>
                      )}
                    </li>
                  );
                })}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </ToolUsageCard>
  );
}
