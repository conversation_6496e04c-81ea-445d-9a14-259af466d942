'use client';

import React, { useState, useEffect } from 'react';
import { BrainIcon, Trash2Icon, FilterIcon, PlusIcon, AlertTriangleIcon, Edit2Icon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface Memory {
  id: string;
  content: string;
  category?: string;
  createdAt: string;
}

export function MemoryManagement() {
  const [memories, setMemories] = useState<Memory[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('');
  const [newMemory, setNewMemory] = useState({ content: '', category: '' });
  const [activeTab, setActiveTab] = useState('all');
  const [isAddingMemory, setIsAddingMemory] = useState(false);
  const [isDeletingMemory, setIsDeletingMemory] = useState(false);
  const [isClearingMemories, setIsClearingMemories] = useState(false);
  const [showClearConfirm, setShowClearConfirm] = useState(false);
  const [editingMemory, setEditingMemory] = useState<Memory | null>(null);
  const [isUpdatingMemory, setIsUpdatingMemory] = useState(false);

  // Fetch memories from the API
  const fetchMemories = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/memory');

      if (!response.ok) {
        throw new Error('Failed to fetch memories');
      }

      const data = await response.json();
      setMemories(data.memories || []);
    } catch (error) {
      console.error('Error fetching memories:', error);
      toast.error('Failed to load memories');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMemories();
  }, []);

  const filteredMemories = memories.filter(memory => {
    const matchesFilter = memory.content.toLowerCase().includes(filter.toLowerCase()) ||
      (memory.category && memory.category.toLowerCase().includes(filter.toLowerCase()));

    if (activeTab === 'all') return matchesFilter;
    return matchesFilter && memory.category === activeTab;
  });

  const categories = [...new Set(memories.map(m => m.category).filter(Boolean))];

  const handleAddMemory = async () => {
    if (!newMemory.content) return;

    try {
      setIsAddingMemory(true);

      const response = await fetch('/api/memory/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: newMemory.content,
          category: newMemory.category || undefined,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create memory');
      }

      const data = await response.json();

      // Add the new memory to the list
      setMemories([data.memory, ...memories]);
      setNewMemory({ content: '', category: '' });
      toast.success('Memory created successfully');
    } catch (error) {
      console.error('Error creating memory:', error);
      toast.error('Failed to create memory');
    } finally {
      setIsAddingMemory(false);
    }
  };

  const handleDeleteMemory = async (id: string) => {
    try {
      setIsDeletingMemory(true);

      const response = await fetch(`/api/memory/delete?id=${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete memory');
      }

      // Remove the memory from the list
      setMemories(memories.filter(m => m.id !== id));
      toast.success('Memory deleted successfully');
    } catch (error) {
      console.error('Error deleting memory:', error);
      toast.error('Failed to delete memory');
    } finally {
      setIsDeletingMemory(false);
    }
  };

  const handleClearAllMemories = async () => {
    try {
      setIsClearingMemories(true);

      const response = await fetch('/api/memory/clear', {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to clear memories');
      }

      // Clear the memories list
      setMemories([]);
      setShowClearConfirm(false);
      toast.success('All memories cleared successfully');
    } catch (error) {
      console.error('Error clearing memories:', error);
      toast.error('Failed to clear memories');
    } finally {
      setIsClearingMemories(false);
    }
  };

  const handleStartEdit = (memory: Memory) => {
    setEditingMemory(memory);
  };

  const handleCancelEdit = () => {
    setEditingMemory(null);
  };

  const handleUpdateMemory = async () => {
    if (!editingMemory) return;

    try {
      setIsUpdatingMemory(true);

      const response = await fetch('/api/memory/update', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: editingMemory.id,
          content: editingMemory.content,
          category: editingMemory.category || undefined,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update memory');
      }

      const data = await response.json();

      // Update the memory in the list
      setMemories(memories.map(m =>
        m.id === editingMemory.id ? data.memory : m
      ));

      setEditingMemory(null);
      toast.success('Memory updated successfully');
    } catch (error) {
      console.error('Error updating memory:', error);
      toast.error('Failed to update memory');
    } finally {
      setIsUpdatingMemory(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <BrainIcon className="h-5 w-5 text-primary" />
          <h2 className="text-xl font-semibold">Memory Management</h2>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="px-2 py-1">
            {memories.length} Memories
          </Badge>
          {memories.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowClearConfirm(true)}
              className="text-destructive hover:bg-destructive/10"
            >
              Clear All
            </Button>
          )}
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex flex-col space-y-2">
          <Label htmlFor="new-memory">Add New Memory</Label>
          <div className="flex gap-2">
            <Input
              id="new-memory"
              placeholder="Enter new memory..."
              value={newMemory.content}
              onChange={(e) => setNewMemory({ ...newMemory, content: e.target.value })}
              className="flex-1"
              disabled={isAddingMemory}
            />
            <Input
              placeholder="Category (optional)"
              value={newMemory.category}
              onChange={(e) => setNewMemory({ ...newMemory, category: e.target.value })}
              className="w-1/3"
              disabled={isAddingMemory}
            />
            <Button
              onClick={handleAddMemory}
              size="sm"
              disabled={isAddingMemory || !newMemory.content}
            >
              {isAddingMemory ? (
                <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-current mr-1" />
              ) : (
                <PlusIcon className="h-4 w-4 mr-1" />
              )}
              Add
            </Button>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <FilterIcon className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Filter memories..."
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="max-w-xs"
          />
        </div>

        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="all">All</TabsTrigger>
            {categories.map(category => (
              <TabsTrigger key={category} value={category}>
                {category}
              </TabsTrigger>
            ))}
          </TabsList>

          <TabsContent value={activeTab} className="space-y-4">
            {loading ? (
              <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : filteredMemories.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No memories found
              </div>
            ) : (
              filteredMemories.map(memory => (
                <React.Fragment key={memory.id}>
                  {editingMemory?.id === memory.id ? (
                    <div
                      className="flex flex-col gap-3 p-4 rounded-lg border border-primary/30 bg-gradient-to-b from-primary/5 to-primary/10 shadow-sm transition-all"
                    >
                      <div className="space-y-2 w-full">
                        <Input
                          value={editingMemory.content}
                          onChange={(e) => setEditingMemory({ ...editingMemory, content: e.target.value })}
                          className="w-full"
                          placeholder="Memory content"
                          disabled={isUpdatingMemory}
                        />
                        <Input
                          value={editingMemory.category || ''}
                          onChange={(e) => setEditingMemory({ ...editingMemory, category: e.target.value })}
                          className="w-full"
                          placeholder="Category (optional)"
                          disabled={isUpdatingMemory}
                        />
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleCancelEdit}
                          disabled={isUpdatingMemory}
                        >
                          Cancel
                        </Button>
                        <Button
                          size="sm"
                          onClick={handleUpdateMemory}
                          disabled={isUpdatingMemory || !editingMemory.content}
                        >
                          {isUpdatingMemory ? (
                            <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-current mr-1" />
                          ) : null}
                          Save
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div
                      className="flex items-start justify-between p-4 rounded-lg border border-border/40 bg-gradient-to-b from-muted/10 to-muted/20 hover:shadow-sm transition-all"
                    >
                      <div className="space-y-1">
                        <p className="text-sm">{memory.content}</p>
                        <div className="flex items-center gap-2">
                          {memory.category && (
                            <Badge variant="secondary" className="text-xs">
                              {memory.category}
                            </Badge>
                          )}
                          <span className="text-xs text-muted-foreground">
                            {new Date(memory.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleStartEdit(memory)}
                          className="text-muted-foreground hover:text-primary"
                          disabled={isDeletingMemory || Boolean(editingMemory)}
                        >
                          <Edit2Icon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteMemory(memory.id)}
                          className="text-muted-foreground hover:text-destructive"
                          disabled={isDeletingMemory || Boolean(editingMemory)}
                        >
                          <Trash2Icon className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </React.Fragment>
              ))
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Confirmation dialog for clearing all memories */}
      <AlertDialog open={showClearConfirm} onOpenChange={setShowClearConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangleIcon className="h-5 w-5 text-destructive" />
              Clear All Memories
            </AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete all your memories. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isClearingMemories}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleClearAllMemories();
              }}
              disabled={isClearingMemories}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isClearingMemories ? (
                <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-current mr-2" />
              ) : null}
              Clear All
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
