'use server';

import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getUser } from '@/lib/db/queries';
import { withRetry } from '@/lib/db/client';

/**
 * API endpoint to check if the user's session is still valid
 * This will check if the user exists in the database and if their confirmation status is true
 * The check can be disabled by setting CHECK_USER_STATUS=false in the environment
 */
export async function GET() {
  try {
    // Get the current session
    const session = await auth();

    // If no session, return null
    if (!session || !session.user || !session.user.email) {
      return NextResponse.json(null);
    }

    // Check if user validation is enabled
    const shouldCheckUserStatus = process.env.CHECK_USER_STATUS === 'true';

    if (shouldCheckUserStatus) {
      try {
        // Check if the user exists in the database and is confirmed
        const users = await withRetry(
          async () => await getUser(session.user.email as string),
          {
            maxRetries: 2,
            onRetry: (error) => {
              console.warn('Retrying user validation due to error:', error.message);
            }
          }
        );

        if (users.length === 0 || !users[0].confirmed) {
          console.log('User no longer exists or is not confirmed:', session.user.email);
          return NextResponse.json(null);
        }
      } catch (error) {
        console.error('Error checking user in database:', error);
        // If database check fails, continue with the session
        console.log('Continuing with session despite database error');
      }
    } else {
      console.log('Skipping user status check (CHECK_USER_STATUS=false)');
    }

    // Return the session if everything is valid
    return NextResponse.json(session);
  } catch (error) {
    console.error('Error checking session:', error);
    return NextResponse.json({ error: 'Failed to check session' }, { status: 500 });
  }
}
