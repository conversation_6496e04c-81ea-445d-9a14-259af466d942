import { getRelevantMemories } from '@/lib/memory/queries';
import { getRelevantToolExperiences } from '@/lib/memory/toolExperiences';

/**
 * Simplified Memory Manager - Orchestrates both user memories and tool experiences
 * Single-phase approach: just get relevant memories for any task request
 */

export interface MemoryContext {
  userId: string;
  userRequest?: string;
  connectedApps?: string[];
  targetApp?: string;
}

export interface MemoryResult {
  userMemories: any[];
  toolExperiences: any[];
  combinedContext: string;
  retrievalStats: {
    userMemoriesCount: number;
    toolExperiencesCount: number;
    totalRetrievalTime: number;
  };
}

/**
 * Retrieves relevant memories for task requests - simplified single-phase approach
 */
export async function getTaskMemory(context: MemoryContext): Promise<MemoryResult> {
  const startTime = Date.now();
  console.log('🧠 Retrieving task memory for:', context.userRequest);

  try {
    // Get user memories related to the specific task
    const userMemoriesPromise = context.userRequest
      ? getRelevantMemories({
          userId: context.userId,
          query: context.userRequest,
          limit: 5,
          similarityThreshold: 0.25
        })
      : Promise.resolve([]);

    // Get tool experiences for task guidance
    const toolExperiencesPromise = context.userRequest
      ? getRelevantToolExperiences({
          userRequest: context.userRequest,
          targetApp: context.targetApp,
          connectedApps: context.connectedApps,
          limit: 5,
          similarityThreshold: 0.3
        })
      : Promise.resolve([]);

    const [userMemories, toolExperiences] = await Promise.all([
      userMemoriesPromise,
      toolExperiencesPromise
    ]);

    const combinedContext = formatMemoryContext({
      userMemories,
      toolExperiences
    });

    return {
      userMemories,
      toolExperiences,
      combinedContext,
      retrievalStats: {
        userMemoriesCount: userMemories.length,
        toolExperiencesCount: toolExperiences.length,
        totalRetrievalTime: Date.now() - startTime
      }
    };
  } catch (error) {
    console.error('Error in task memory retrieval:', error);
    return createEmptyResult(startTime);
  }
}

// Backward compatibility aliases - all point to the simplified function
export const getTaskStartMemory = getTaskMemory;
export const getPreFetchMemory = getTaskMemory;
export const getPreExecuteMemory = getTaskMemory;
export const getStrategicMemory = getTaskMemory;
export const getTacticalMemory = getTaskMemory;

/**
 * Formats the combined context from user memories and tool experiences - simplified
 */
function formatMemoryContext({
  userMemories,
  toolExperiences
}: {
  userMemories: any[];
  toolExperiences: any[];
}): string {
  let context = '';

  // Add user memories if available
  if (userMemories.length > 0) {
    context += '\n\n📋 User Context:\n';
    context += userMemories.map((m: any) => `- ${m.content}`).join('\n');
  }

  // Add tool experiences if available
  if (toolExperiences.length > 0) {
    context += '\n\n🔧 Tool Guidance:\n';
    context += toolExperiences.map((exp: any) => `- ${exp.content}`).join('\n');
  }

  return context;
}

/**
 * Creates an empty result for error cases
 */
function createEmptyResult(startTime: number): MemoryResult {
  return {
    userMemories: [],
    toolExperiences: [],
    combinedContext: '',
    retrievalStats: {
      userMemoriesCount: 0,
      toolExperiencesCount: 0,
      totalRetrievalTime: Date.now() - startTime
    }
  };
}

/**
 * Utility function to extract app name from action name
 */
export function extractAppFromAction(actionName: string): string {
  const appPrefixes = ['GITHUB_', 'GMAIL_', 'NOTION_', 'JIRA_', 'GOOGLE_CALENDAR_'];

  for (const prefix of appPrefixes) {
    if (actionName.startsWith(prefix)) {
      return prefix.replace('_', '').toLowerCase();
    }
  }

  return 'unknown';
}

/**
 * Utility function to determine if a request involves multiple apps
 */
export function isMultiAppRequest(request: string): boolean {
  const appKeywords = ['github', 'gmail', 'notion', 'jira', 'calendar'];
  const foundApps = appKeywords.filter(app =>
    request.toLowerCase().includes(app)
  );

  return foundApps.length > 1;
}
