'use client';

import { useEffect, useState } from 'react';
import { Button } from './ui/button';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { ScrollToBottomIcon } from './scroll-to-bottom-icon';

interface ScrollToBottomButtonProps {
  containerRef: React.RefObject<HTMLDivElement>;
  endRef: React.RefObject<HTMLDivElement>;
  scrollToBottom?: () => void;
}

export function ScrollToBottomButton({
  containerRef,
  endRef,
  scrollToBottom: externalScrollToBottom,
}: ScrollToBottomButtonProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      // Show button when user has scrolled up a bit
      const scrolledPosition = container.scrollHeight - container.scrollTop - container.clientHeight;
      const isScrolledUp = scrolledPosition > 100;
      setIsVisible(isScrolledUp);
    };

    // Check initial scroll position
    handleScroll();

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [containerRef]);

  const handleScrollToBottom = (event: React.MouseEvent<HTMLButtonElement>) => {
    // Prevent default form submission behavior
    event.preventDefault();

    if (externalScrollToBottom) {
      // Use the function from useScrollToBottom if provided
      externalScrollToBottom();
    } else if (endRef.current) {
      // Fallback to basic scrolling if not provided
      endRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
  };

  return (
    <div
      className={cn(
        'transition-opacity duration-200',
        isVisible ? 'opacity-100' : 'opacity-0 pointer-events-none'
      )}
    >
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            onClick={handleScrollToBottom}
            variant="default"
            className="rounded-full size-8 md:size-9 bg-zinc-800/90 text-white hover:bg-zinc-700 shadow-md flex items-center justify-center"
            aria-label="Scroll to bottom"
          >
            <ScrollToBottomIcon size={16} className="md:scale-125" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Scroll to bottom</TooltipContent>
      </Tooltip>
    </div>
  );
}
