'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loader2, Search } from 'lucide-react';
import { toast } from 'sonner';

interface FetchActionsToolProps {
  append: (message: string) => void;
  isAnimating: boolean;
  setSelectedTool: (tool: string | null) => void;
}

export function FetchActionsTool({
  append,
  isAnimating,
  setSelectedTool,
}: FetchActionsToolProps) {
  const [description, setDescription] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!description.trim()) {
      toast.error('Please enter a description of what you want to do');
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/tools/fetch-actions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ description }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch actions');
      }

      if (data.success) {
        // Format the results for display in the chat
        let resultMessage = '';
        
        if (data.actions && data.actions.length > 0) {
          resultMessage = `Found ${data.actions.length} relevant actions for "${description}":\n\n`;
          
          // Group actions by app
          const actionsByApp: Record<string, any[]> = {};
          data.actions.forEach((action: any) => {
            const appName = action.name.split('_')[0].toLowerCase();
            if (!actionsByApp[appName]) {
              actionsByApp[appName] = [];
            }
            actionsByApp[appName].push(action);
          });
          
          // Format the actions by app
          Object.entries(actionsByApp).forEach(([app, actions]) => {
            resultMessage += `**${app.toUpperCase()}**:\n`;
            actions.forEach((action: any) => {
              const actionName = action.name.replace(/_/g, ' ').toLowerCase();
              resultMessage += `- ${actionName}\n`;
            });
            resultMessage += '\n';
          });
          
          resultMessage += 'I can execute any of these actions for you. Let me know which one you had like to use.';
        } else {
          if (data.metadata?.connectedApps?.length === 0) {
            resultMessage = "You don't have any connected apps. Please connect apps in your profile settings to use this feature.";
          } else {
            resultMessage = `I couldn't find any relevant actions for "${description}" in your connected apps (${data.metadata?.connectedApps?.join(', ')}). Please try a different description or connect more apps.`;
          }
        }
        
        // Append the result to the chat
        append(resultMessage);
        
        // Reset the tool
        setSelectedTool(null);
      } else {
        throw new Error(data.error || 'Failed to fetch actions');
      }
    } catch (error) {
      console.error('Error fetching actions:', error);
      toast.error(error instanceof Error ? error.message : 'An error occurred');
      append('Sorry, I encountered an error while fetching actions. Please try again later.');
    } finally {
      setIsLoading(false);
      setDescription('');
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="flex w-full items-center space-x-2"
    >
      <div className="flex-1 flex items-center border rounded-md overflow-hidden bg-background">
        <div className="px-3 py-2 text-muted-foreground">
          <Search className="h-4 w-4" />
        </div>
        <Input
          className="flex-1 border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
          placeholder="Describe what you want to do (e.g., 'list my GitHub repos')"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          disabled={isLoading || isAnimating}
        />
      </div>
      <Button
        type="submit"
        disabled={isLoading || isAnimating || !description.trim()}
        className="shrink-0"
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Fetching...
          </>
        ) : (
          'Fetch Actions'
        )}
      </Button>
    </form>
  );
}
