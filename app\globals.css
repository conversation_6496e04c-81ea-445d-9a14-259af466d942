@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 214, 219, 220;
    --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
    :root {
        --foreground-rgb: 255, 255, 255;
        --background-start-rgb: 15, 15, 25;
        --background-end-rgb: 10, 10, 20;
    }
}

@layer utilities {
    .text-balance {
        text-wrap: balance;
    }
}

@layer base {
    :root {
        /* Soft Navy blue theme - Light mode */
        --background: 210 25% 98%;
        --foreground: 215 20% 25%;
        --card: 0 0% 100%;
        --card-foreground: 215 20% 25%;
        --popover: 0 0% 100%;
        --popover-foreground: 215 20% 25%;
        --primary: 215 60% 35%;
        --primary-foreground: 0 0% 100%;
        --secondary: 210 20% 96%;
        --secondary-foreground: 215 20% 25%;
        --muted: 210 20% 96%;
        --muted-foreground: 215 15% 45%;
        --accent: 215 60% 40%;
        --accent-foreground: 0 0% 100%;
        --destructive: 0 70% 60%;
        --destructive-foreground: 0 0% 98%;
        --border: 210 20% 92%;
        --input: 210 20% 92%;
        --ring: 215 60% 35%;
        --chart-1: 220 70% 25%;
        --chart-2: 210 70% 40%;
        --chart-3: 30 80% 55%;
        --chart-4: 240 65% 60%;
        --chart-5: 340 75% 55%;
        --radius: 0.5rem;
        --sidebar-background: 210 25% 98%;
        --sidebar-foreground: 215 20% 25%;
        --sidebar-primary: 215 60% 35%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 210 20% 96%;
        --sidebar-accent-foreground: 215 20% 25%;
        --sidebar-border: 210 20% 92%;
        --sidebar-ring: 215 60% 35%;
    }
    .dark {
        /* Navy blue dark theme */
        --background: 220 20% 6%;
        --foreground: 0 0% 98%;
        --card: 220 20% 8%;
        --card-foreground: 0 0% 98%;
        --popover: 220 20% 8%;
        --popover-foreground: 0 0% 98%;
        --primary: 220 70% 30%;
        --primary-foreground: 0 0% 100%;
        --secondary: 220 15% 12%;
        --secondary-foreground: 0 0% 98%;
        --muted: 220 15% 12%;
        --muted-foreground: 220 5% 70%;
        --accent: 220 70% 35%;
        --accent-foreground: 0 0% 100%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 220 15% 15%;
        --input: 220 15% 15%;
        --ring: 220 70% 30%;
        --chart-1: 220 70% 30%;
        --chart-2: 210 70% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 240 65% 60%;
        --chart-5: 340 75% 55%;
        --sidebar-background: 220 20% 5%;
        --sidebar-foreground: 220 4.8% 95.9%;
        --sidebar-primary: 220 70% 30%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 220 15% 12%;
        --sidebar-accent-foreground: 220 4.8% 95.9%;
        --sidebar-border: 220 15% 15%;
        --sidebar-ring: 220 70% 30%;
    }
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }

    /* Custom Scrollbar Styles for Sidebar */
    [data-sidebar="content"] {
        scrollbar-width: thin;
        scrollbar-color: hsla(var(--sidebar-border), 0.3) transparent;
        /* Add mask image for fading effect at top and bottom */
        mask-image: linear-gradient(to bottom,
            transparent 0px,
            black 8px,
            black calc(100% - 8px),
            transparent 100%
        );
    }

    /* For Webkit browsers (Chrome, Safari, Edge) */
    [data-sidebar="content"]::-webkit-scrollbar {
        width: 4px; /* Slightly thinner for more premium look */
    }

    [data-sidebar="content"]::-webkit-scrollbar-track {
        background: transparent;
        margin: 8px 0; /* Increased margin for better spacing */
        border-radius: 10px;
    }

    [data-sidebar="content"]::-webkit-scrollbar-thumb {
        background: hsla(var(--sidebar-border), 0.3);
        border-radius: 10px;
        transition: all 0.3s ease;
        /* Add subtle shadow for depth */
        box-shadow: inset 0 0 2px hsla(var(--sidebar-border), 0.1);
    }

    [data-sidebar="content"]::-webkit-scrollbar-thumb:hover {
        background: hsla(var(--sidebar-border), 0.5);
        /* Slightly wider on hover for better usability */
        width: 6px;
    }

    /* Hide scrollbar when not hovering for a cleaner look */
    [data-sidebar="content"] {
        transition: all 0.3s ease;
    }

    [data-sidebar="content"]:not(:hover) {
        scrollbar-color: transparent transparent;
    }

    [data-sidebar="content"]:not(:hover)::-webkit-scrollbar-thumb {
        background: transparent;
        opacity: 0;
    }

    /* Dark mode adjustments */
    .dark [data-sidebar="content"]::-webkit-scrollbar-thumb {
        background: hsla(var(--sidebar-border), 0.4);
        /* Subtle glow effect in dark mode */
        box-shadow: inset 0 0 3px hsla(var(--sidebar-primary), 0.2);
    }

    .dark [data-sidebar="content"]::-webkit-scrollbar-thumb:hover {
        background: hsla(var(--sidebar-border), 0.6);
        box-shadow: inset 0 0 4px hsla(var(--sidebar-primary), 0.3);
    }

    /* Add smooth scroll behavior */
    [data-sidebar="content"] {
        scroll-behavior: smooth;
    }
}

.skeleton {
    * {
        pointer-events: none !important;
    }

    *[class^="text-"] {
        color: transparent;
        @apply rounded-md bg-foreground/20 select-none animate-pulse;
    }

    .skeleton-bg {
        @apply bg-foreground/10;
    }

    .skeleton-div {
        @apply bg-foreground/20 animate-pulse;
    }
}

.ProseMirror {
    outline: none;
}

.cm-editor,
.cm-gutters {
    @apply bg-background dark:bg-zinc-800 outline-none selection:bg-zinc-900 !important;
}

.ͼo.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground,
.ͼo.cm-selectionBackground,
.ͼo.cm-content::selection {
    @apply bg-zinc-200 dark:bg-zinc-900 !important;
}

.cm-activeLine,
.cm-activeLineGutter {
    @apply bg-transparent !important;
}

.cm-activeLine {
    @apply rounded-r-sm !important;
}

.cm-lineNumbers {
    @apply min-w-7;
}

.cm-foldGutter {
    @apply min-w-3;
}

.cm-lineNumbers .cm-activeLineGutter {
    @apply rounded-l-sm !important;
}

.suggestion-highlight {
    @apply bg-blue-200 hover:bg-blue-300 dark:hover:bg-blue-400/50 dark:text-blue-50 dark:bg-blue-500/40;
}

/* Navy blue gradient background */
.kortex-gradient-bg {
    background: linear-gradient(135deg, hsl(220, 70%, 15%) 0%, hsl(220, 70%, 10%) 100%);
    position: relative;
}

/* Light mode gradient - softer */
:root .kortex-gradient-bg {
    background: linear-gradient(135deg, hsl(210, 25%, 98%) 0%, hsl(210, 25%, 96%) 100%);
    position: relative;
}

.kortex-gradient-bg::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 15% 50%, hsla(220, 70%, 25%, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 85% 30%, hsla(220, 70%, 25%, 0.08) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

/* Light mode gradient overlay - softer */
:root .kortex-gradient-bg::before {
    background:
        radial-gradient(circle at 15% 50%, hsla(215, 60%, 35%, 0.04) 0%, transparent 50%),
        radial-gradient(circle at 85% 30%, hsla(215, 60%, 35%, 0.04) 0%, transparent 50%);
}

.kortex-content {
    position: relative;
    z-index: 1;
}
