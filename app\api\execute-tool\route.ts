import { OpenAIToolSet } from "composio-core";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
  try {
    let { action, params, entityId, connectedAccountId } = await req.json();

    if (!action) {
      return NextResponse.json({
        success: false,
        error: 'Action name is required'
      }, { status: 400 });
    }

    // Check if parameters are incorrectly included in the action name
    // Example: "GITHUB_LIST_BRANCHES owner=mizouzour86 repo=IRS"
    const actionNameRegex = /^([A-Z0-9_]+)(?:\s+(.*))?$/;
    const actionNameMatch = action.match(actionNameRegex);

    if (actionNameMatch && actionNameMatch[2]) {
      // We found parameters in the action name
      const cleanedAction = actionNameMatch[1]; // The clean action name
      const paramsString = actionNameMatch[2]; // The parameters part

      // Try to extract parameters in format "key=value key2=value2"
      const extractedParams = { ...(params || {}) };
      const paramPairs = paramsString.match(/(\w+)=([^\s]+)/g) || [];
      paramPairs.forEach((pair: string) => {
        const [key, value] = pair.split('=');
        if (key && value) {
          extractedParams[key] = value;
        }
      });

      console.log(`Extracted parameters from action name: ${JSON.stringify(extractedParams)}`);
      console.log(`Cleaned action name: ${cleanedAction}`);

      // Use the cleaned action name and merged parameters
      action = cleanedAction;
      params = extractedParams;
    }

    const toolset = new OpenAIToolSet();

    try {
      // Execute action using the documented structure
      const result = await toolset.client.actions.execute({
        actionName: action, // The action name is required
        requestBody: {
          appName: action.toLowerCase().split('_')[0], // Extract app name from action
          input: params || {}, // The parameters for the action
          entityId, // Optional: user context
          connectedAccountId, // Optional: specific connection
        }
      });

      // Return successful result
      return NextResponse.json({
        successful: true,
        data: result.data || result,
        raw: result
      });
    } catch (error) {
      const execError = error as Error;
      console.error('Execution error:', execError);

      // Check for connection-related errors
      if (execError.message && (
        execError.message.includes('ConnectedAccountNotFoundError') ||
        execError.message.includes('AuthenticationError') ||
        execError.message.includes('Connection')
      )) {
        return NextResponse.json({
          successful: false,
          error: execError instanceof Error ? execError.message : 'Connection error',
          details: execError,
          redirectUrl: "/profile/connections"
        }, { status: 401 });
      }

      return NextResponse.json({
        successful: false,
        error: execError instanceof Error ? execError.message : 'Tool execution failed',
        details: execError
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Request parse error:', error);
    return NextResponse.json({
      successful: false,
      error: error instanceof Error ? error.message : 'Invalid request'
    }, { status: 400 });
  }
}