-- Migration: Add ToolExperiences table for AI agent learning patterns
-- This table stores strategic insights, patterns, and successful approaches
-- that help the AI agent perform better with 20% of patterns solving 80% of tasks

CREATE TABLE IF NOT EXISTS "ToolExperiences" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "experience_type" TEXT NOT NULL CHECK (experience_type IN ('pattern', 'insight', 'advice', 'workflow', 'parameter_combo')),
  "app_name" TEXT NOT NULL,
  "action_name" TEXT,
  "context_tags" TEXT[],
  "experience_content" TEXT NOT NULL,
  "success_rate" REAL DEFAULT 1.0 CHECK (success_rate >= 0.0 AND success_rate <= 1.0),
  "usage_count" INTEGER DEFAULT 0,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "lastUsed" TIMESTAMP WITH TIME ZONE,
  "priority" TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('critical', 'high', 'medium', 'low')),
  "isActive" BOOLEAN NOT NULL DEFAULT true
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS "idx_tool_experiences_app_name" ON "ToolExperiences" ("app_name");
CREATE INDEX IF NOT EXISTS "idx_tool_experiences_experience_type" ON "ToolExperiences" ("experience_type");
CREATE INDEX IF NOT EXISTS "idx_tool_experiences_priority" ON "ToolExperiences" ("priority");
CREATE INDEX IF NOT EXISTS "idx_tool_experiences_context_tags" ON "ToolExperiences" USING GIN ("context_tags");
CREATE INDEX IF NOT EXISTS "idx_tool_experiences_active" ON "ToolExperiences" ("isActive");
CREATE INDEX IF NOT EXISTS "idx_tool_experiences_usage" ON "ToolExperiences" ("usage_count" DESC, "success_rate" DESC);

-- Insert some initial strategic experiences for common patterns
INSERT INTO "ToolExperiences" ("experience_type", "app_name", "action_name", "context_tags", "experience_content", "success_rate", "priority") VALUES

-- GitHub Repository Management Patterns
('workflow', 'github', 'general', ARRAY['repo_management', 'project_overview'], 
 'For repository exploration: 1) Use GITHUB_LIST_REPOSITORIES with type=owner and sort=updated 2) Then GITHUB_GET_REPOSITORY_CONTENT with path="" for overview 3) Check README.md for project details', 
 0.95, 'critical'),

('pattern', 'github', 'GITHUB_LIST_REPOSITORIES', ARRAY['repo_management'], 
 'Most effective parameters: {type: "owner", sort: "updated", per_page: 10} - shows user''s most recently active repositories first', 
 0.92, 'high'),

('parameter_combo', 'github', 'GITHUB_GET_REPOSITORY_CONTENT', ARRAY['file_exploration'], 
 'For file exploration, start with path="" to get repository structure, then drill down to specific files. Use ref parameter for specific branches.', 
 0.88, 'high'),

-- Gmail Email Management Patterns  
('workflow', 'gmail', 'general', ARRAY['email_management', 'search'], 
 'For email tasks: 1) Use GMAIL_FETCH_EMAILS with specific query parameters 2) Avoid fetching all emails - always use query filters 3) Set maxResults to reasonable limit (10-50)', 
 0.90, 'critical'),

('parameter_combo', 'gmail', 'GMAIL_FETCH_EMAILS', ARRAY['search', 'filtering'], 
 'Optimize with: {query: "subject:keyword OR from:email", maxResults: 20, includeSpamTrash: false} - much faster than fetching all emails then filtering', 
 0.85, 'high'),

('advice', 'gmail', 'GMAIL_FETCH_EMAILS', ARRAY['performance'], 
 'Always use query parameter to filter emails server-side. Common patterns: "is:unread", "from:domain.com", "subject:keyword", "newer_than:7d"', 
 0.87, 'medium'),

-- Notion Documentation Patterns
('workflow', 'notion', 'general', ARRAY['documentation', 'project_management'], 
 'For Notion tasks: 1) Search pages first with NOTION_SEARCH_PAGES 2) Get page content with NOTION_FETCH_NOTION_PAGE 3) Use block operations for specific content updates', 
 0.83, 'high'),

('pattern', 'notion', 'NOTION_SEARCH_PAGES', ARRAY['search'], 
 'Use specific search terms and filter by object type. Query should be descriptive but not too long - Notion search works best with 2-4 keywords', 
 0.80, 'medium'),

-- Multi-App Workflow Patterns
('workflow', 'multi', 'general', ARRAY['documentation', 'project_management'], 
 'Documentation sync workflow: 1) GitHub - find project files and README 2) Notion - search for related project pages 3) Update both platforms for consistency', 
 0.78, 'high'),

('workflow', 'multi', 'general', ARRAY['project_management', 'status_updates'], 
 'Project status workflow: 1) GitHub - check recent commits and issues 2) Jira - review ticket status 3) Calendar - check upcoming deadlines 4) Summarize in single update', 
 0.82, 'high'),

-- Jira Project Management Patterns
('pattern', 'jira', 'general', ARRAY['project_management', 'issue_tracking'], 
 'For Jira queries, use JQL (Jira Query Language) for precise filtering. Common patterns: "assignee = currentUser()", "status != Done", "project = KEY"', 
 0.85, 'medium'),

-- Calendar Scheduling Patterns
('advice', 'googlecalendar', 'general', ARRAY['scheduling', 'time_management'], 
 'When working with calendar events, always specify timeZone parameter and use ISO 8601 format for dates. Check for conflicts before creating events.', 
 0.90, 'medium');

-- Add a comment about the purpose
COMMENT ON TABLE "ToolExperiences" IS 'Stores AI agent learning patterns and strategic insights for tool usage optimization. Implements the 80/20 rule where 20% of patterns solve 80% of user tasks.';
