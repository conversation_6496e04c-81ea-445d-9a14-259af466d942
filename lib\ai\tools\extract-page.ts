import { tool } from 'ai';
import { z } from 'zod';
import { tavily } from '@tavily/core';

export const extractPage = tool({
  description: 'Extract and summarize content from a specific URL or webpage',
  parameters: z.object({
    url: z.string().describe('The URL of the webpage to extract content from'),
  }),
  execute: async ({ url }) => {
    try {
      // Initialize Tavily client with API key from environment variable
      const client = tavily({ apiKey: process.env.TAVILY_API_KEY });

      // Call Tavily extract API
      const extractionResult = await client.extract([url]);

      // Check if we have results
      if (extractionResult.results && extractionResult.results.length > 0) {
        const result = extractionResult.results[0];

        // Format the result for better readability
        return {
          url: url,
          title: 'Extracted Content', // <PERSON><PERSON> doesn't return titles in extract API
          content: result.rawContent || 'No content extracted',
          message: `Successfully extracted content from ${url}`,
        };
      } else if (extractionResult.failedResults && extractionResult.failedResults.length > 0) {
        // Handle failed extraction
        const failedResult = extractionResult.failedResults[0];
        console.error('Error extracting page content:', failedResult.error);

        return {
          url: url,
          title: 'No title found',
          content: 'No content extracted',
          message: `Failed to extract content: ${failedResult.error}`,
          error: failedResult.error
        };
      } else {
        // Handle unexpected response format
        console.error('Unexpected extraction result format:', extractionResult);

        return {
          url: url,
          title: 'No title found',
          content: 'No content extracted',
          message: 'Failed to extract content: Unexpected response format',
          error: 'Unexpected response format'
        };
      }
    } catch (error) {
      console.error('Error extracting page content:', error);

      return {
        url: url,
        title: 'No title found',
        content: 'No content extracted',
        message: error instanceof Error ? error.message : 'Unknown error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },
});
