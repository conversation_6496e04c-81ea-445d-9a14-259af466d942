import { tool } from 'ai';
import { z } from 'zod';
import { OpenAIToolSet } from "composio-core";
import { getConnectionsByUserId } from '@/lib/db/queries';
import { auth } from '@/app/(auth)/auth';

// Helper function to ensure connection ID is in UUID format
// Composio API requires UUID format for connection IDs
function ensureUuidFormat(connectionId: string): string {
  // If it's already in UUID format, return it
  if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(connectionId)) {
    return connectionId;
  }

  // If it's in ca_ format, convert it to a UUID-like format
  // This is just a fallback and may not work in all cases
  if (connectionId.startsWith('ca_')) {
    // Remove the ca_ prefix
    const idPart = connectionId.substring(3);

    // Create a UUID-like string (this is just a placeholder, not a real conversion)
    // In a real implementation, you would need to get the actual UUID for this connection
    console.log(`Warning: Converting connection ID from ca_ format to UUID format. This may not work.`);
    return `00000000-0000-0000-0000-${idPart.padStart(12, '0')}`;
  }

  // If it's neither, return as is and hope for the best
  console.log(`Warning: Connection ID ${connectionId} is not in UUID format. This may cause errors.`);
  return connectionId;
}

export const executeAction = tool({
  description: 'Execute an action on a connected service. IMPORTANT: You must first use fetchActions to find available actions, then use this tool to execute one of the EXACT action names returned by fetchActions. Do not attempt to guess action names.',
  parameters: z.object({
    action: z.string().describe('The EXACT action name to execute as returned by fetchActions (e.g., GITHUB_LIST_REPOSITORIES)'),
    params: z.record(z.any()).optional().describe('The parameters required by the action'),
  }),
  execute: async ({ action, params }) => {
    try {
      // Get the current user session
      const session = await auth();

      if (!session?.user?.id) {
        return {
          success: false,
          error: 'User not authenticated',
          message: 'You need to be logged in to execute actions on connected services.'
        };
      }

      // Get user's connected services
      const connections = await getConnectionsByUserId(session.user.id);

      if (!connections || connections.length === 0) {
        return {
          success: false,
          error: 'No connected services',
          message: 'You need to connect at least one service before executing actions. Please connect a service first.',
          redirectUrl: "/profile/connections"
        };
      }

      // Extract connected app names
      const connectedApps = connections.map(conn => conn.provider.toLowerCase());
      console.log(`User has active connections to: ${connectedApps.join(', ')}`);

      // Find the appropriate connection for this action
      const appName = action.toLowerCase().split('_')[0];

      // Get all connections for this app
      const appConnections = connections.filter(conn =>
        conn.provider.toLowerCase() === appName &&
        conn.status === 'ACTIVE'
      );

      if (appConnections.length === 0) {
        return {
          success: false,
          error: `No active connection found for ${appName}`,
          message: `You need to connect your ${appName} account before executing this action, or your existing connection may need to be refreshed.`,
          redirectUrl: "/profile/connections"
        };
      }

      // Use the most recently created connection for all services
      let connectionId;
      const connection = appConnections.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      // Get the connection ID from the connectionId column (not the record's id)
      connectionId = connection.connectionId;

      // Log connection details for debugging
      console.log(`Connection details for ${appName}:`, {
        recordId: connection.id,
        connectionId: connection.connectionId,
        provider: connection.provider,
        status: connection.status,
        createdAt: connection.createdAt
      });

      if (connectionId) {
        connectionId = ensureUuidFormat(connectionId);
        console.log(`Using connection ID: ${connectionId} for ${appName}`);
      }

      // Verify the connection is valid
      if (!connectionId) {
        return {
          success: false,
          error: `Invalid connection for ${appName}`,
          message: `Your ${appName} connection appears to be invalid. Please reconnect your account.`,
          redirectUrl: "/profile/connections"
        };
      }

      // Initialize the Composio toolset
      const toolset = new OpenAIToolSet();

      // Check if parameters are incorrectly included in the action name
      // Example: "GITHUB_LIST_BRANCHES owner=mizouzour86 repo=IRS"
      let cleanedAction = action;
      let extractedParams = { ...(params || {}) };

      // Regular expression to match the base action name (before any parameters)
      const actionNameRegex = /^([A-Z0-9_]+)(?:\s+(.*))?$/;
      const actionNameMatch = action.match(actionNameRegex);

      if (actionNameMatch && actionNameMatch[2]) {
        // We found parameters in the action name
        cleanedAction = actionNameMatch[1]; // The clean action name
        const paramsString = actionNameMatch[2]; // The parameters part

        // Try to extract parameters in format "key=value key2=value2"
        const paramPairs = paramsString.match(/(\w+)=([^\s]+)/g) || [];
        paramPairs.forEach((pair: string) => {
          const [key, value] = pair.split('=');
          if (key && value) {
            extractedParams[key] = value;
          }
        });

        console.log(`Extracted parameters from action name: ${JSON.stringify(extractedParams)}`);
        console.log(`Cleaned action name: ${cleanedAction}`);
      }

      // Use the cleaned action name and merged parameters
      action = cleanedAction;
      params = extractedParams;

      console.log(`Executing action: ${action}`);
      console.log(`With parameters:`, params || {});

      // Validate the action name format
      if (!action.includes('_')) {
        return {
          success: false,
          error: `Invalid action name format: ${action}`,
          message: `The action name must be in the format APP_ACTION_NAME as returned by the fetchActions tool. Please use fetchActions first to get a list of available actions.`
        };
      }

      // Try to get available actions for this app to validate
      try {
        console.log(`Validating action ${action} against available actions`);

        // Get the app name from the action
        const actionAppName = action.split('_')[0].toLowerCase();

        // Only proceed if the app name matches the connection's provider
        if (actionAppName !== appName.toLowerCase()) {
          return {
            success: false,
            error: `Action app mismatch: ${actionAppName} vs ${appName}`,
            message: `The action ${action} does not match the connected app ${appName}. Please use an action for the correct app.`
          };
        }

        // Try to get a list of available actions for this app
        const availableTools = await toolset.getTools({
          apps: [appName]
        });

        console.log(`Found ${availableTools.length} available tools for ${appName}`);

        // Check if the action is in the list of available actions
        const actionExists = availableTools.some((tool: any) => {
          const toolName = tool.name || tool.function?.name || tool.id || "";
          return toolName === action;
        });

        if (!actionExists) {
          console.log(`Action ${action} not found in available actions`);
          return {
            success: false,
            error: `Action not found: ${action}`,
            message: `The action "${action}" was not found in the list of available actions. Please use the fetchActions tool first to get a list of available actions, then use one of those exact action names.`
          };
        }
      } catch (validationError) {
        console.log(`Error validating action: ${validationError}`);
        // Continue even if validation fails - we'll let the execution attempt proceed
      }

      // Execute the action
      let result;
      try {
        console.log(`Executing action ${action} with connection ID ${connectionId}`);

        result = await toolset.client.actions.execute({
          actionName: action,
          requestBody: {
            appName: appName,
            input: params || {},
            entityId: session.user.id,
            connectedAccountId: connectionId
          }
        });
      } catch (error) {
        const execError = error as Error;
        console.error("Error executing action:", execError);

        // Handle specific error types
        if (execError.message && execError.message.includes('ConnectedAccountNotFoundError')) {
          return {
            success: false,
            error: `Connection not found or expired`,
            message: `Your ${appName} connection appears to have expired or been invalidated. Please reconnect your ${appName} account and try again.`,
            redirectUrl: "/profile/connections"
          };
        }

        if (execError.message && execError.message.includes('NotFoundError')) {
          return {
            success: false,
            error: `Action not found: ${action}`,
            message: `The action "${action}" could not be found. Please verify the action name and try again.`
          };
        }

        if (execError.message && execError.message.includes('AuthenticationError')) {
          return {
            success: false,
            error: `Authentication error`,
            message: `There was an authentication error with your ${appName} connection. Please reconnect your account and try again.`,
            redirectUrl: "/profile/connections"
          };
        }

        // Re-throw for general error handling
        throw execError;
      }

      console.log(`Action executed successfully`);

      // Format the result display message - simplified to avoid redundancy with UI
      let resultMessage = `Action executed successfully.`;

      // Add minimal summary based on action type without duplicating what's shown in the UI
      if (action.includes('LIST') && Array.isArray(result.data)) {
        resultMessage += ` Found ${result.data.length} items.`;
      } else if (action.includes('CREATE')) {
        resultMessage += ` Item created successfully.`;
      } else if (action.includes('UPDATE')) {
        resultMessage += ` Item updated successfully.`;
      } else if (action.includes('DELETE')) {
        resultMessage += ` Item deleted successfully.`;
      }

      // Return the result with metadata
      return {
        success: true,
        message: resultMessage,
        result: result.data || result,
        action,
        params
      };
    } catch (error) {
      console.error("Error in execute-action tool:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: 'Failed to execute action. Please check the action name and parameters and try again.'
      };
    }
  },
});
