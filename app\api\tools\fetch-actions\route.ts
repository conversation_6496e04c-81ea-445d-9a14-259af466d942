import { NextResponse } from 'next/server';
import { OpenAIToolSet } from "composio-core";
import { auth } from '@/app/(auth)/auth';
import { getConnectionsByUserId } from '@/lib/db/queries';

export async function POST(request: Request) {
  try {
    const session = await auth();
    
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { description } = await request.json();
    
    if (!description) {
      return NextResponse.json(
        { error: 'Description is required' },
        { status: 400 }
      );
    }

    console.log(`Fetching actions for description: "${description}"`);

    // Get user's active connections to determine which apps to search
    const userId = session.user.id as string;
    const userConnections = await getConnectionsByUserId(userId);
    const activeConnections = userConnections.filter(conn => conn.status === 'ACTIVE');
    
    // Extract the provider names (app names) from active connections
    const connectedApps = [...new Set(activeConnections.map(conn => conn.provider.toLowerCase()))];
    
    console.log(`User has active connections to: ${connectedApps.join(', ') || 'none'}`);
    
    // If user has no active connections, return empty results
    if (connectedApps.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No connected apps available',
        actions: [],
        metadata: {
          connectedApps: [],
          relevantActionsFound: 0,
          toolsFetched: 0
        }
      });
    }

    // Initialize the Composio toolset
    const toolset = new OpenAIToolSet();
    
    try {
      // Find relevant actions by use case
      console.log("Searching for actions with use case:", {
        useCase: description,
        apps: connectedApps
      });

      // First get the action enums (lightweight search)
      const relevantActionEnums = await toolset.client.actions.findActionEnumsByUseCase({
        useCase: description,
        apps: connectedApps,
        advanced: true // Enable multi-tool chains for more complex tasks
      });

      console.log(`Found ${relevantActionEnums.length} relevant action enums`);
      
      if (relevantActionEnums.length === 0) {
        return NextResponse.json({
          success: true,
          message: 'No relevant actions found',
          actions: [],
          metadata: {
            connectedApps,
            relevantActionsFound: 0,
            toolsFetched: 0
          }
        });
      }

      // Then get the full tool definitions for these actions
      const tools = await toolset.getTools({
        actions: relevantActionEnums
      });

      console.log(`Fetched ${tools.length} tool definitions`);

      // Return the tools with metadata
      return NextResponse.json({
        success: true,
        actions: tools,
        metadata: {
          connectedApps,
          relevantActionsFound: relevantActionEnums.length,
          toolsFetched: tools.length,
          searchDescription: description
        }
      });
    } catch (error) {
      console.error("Error fetching tools by use case:", error);
      return NextResponse.json({
        success: false,
        error: error instanceof Error ? error.message : String(error),
        metadata: {
          connectedApps
        }
      }, { status: 500 });
    }
  } catch (error) {
    console.error("Error in fetch-actions API:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
