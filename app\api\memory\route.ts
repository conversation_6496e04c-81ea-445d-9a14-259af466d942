import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getAllMemories, getRelevantMemories } from '@/lib/memory/queries';

export async function GET(request: Request) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Get the user ID
    const userId = session.user.id;

    let memories;
    if (query) {
      console.log(`Searching memories with query: "${query}"`);

      // Get relevant memories based on the query
      memories = await getRelevantMemories({
        userId,
        query,
        limit,
      });
    } else {
      // Get all memories
      memories = await getAllMemories({ userId });
    }

    return NextResponse.json({
      success: true,
      memories,
    });
  } catch (error) {
    console.error('Error retrieving memories:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
