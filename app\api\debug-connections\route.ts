import { NextResponse } from 'next/server';
import { OpenAIToolSet } from "composio-core";
import { auth } from '@/app/(auth)/auth';
import { getConnectionsByUserId } from '@/lib/db/queries';

export async function GET() {
  try {
    const session = await auth();

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Get user ID
    const userId = session.user.id as string;
    
    // Get connections from database
    const dbConnections = await getConnectionsByUserId(userId);
    
    // Initialize Composio toolset
    const toolset = new OpenAIToolSet();
    
    // Get entity for the user
    const entity = await toolset.getEntity(userId);
    
    // Get connections from Composio
    const composioConnections = await entity.getConnections();
    
    return NextResponse.json({
      success: true,
      dbConnections,
      composioConnections,
      userId
    });
  } catch (error) {
    console.error('Error debugging connections:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'An error occurred'
      },
      { status: 500 }
    );
  }
}
