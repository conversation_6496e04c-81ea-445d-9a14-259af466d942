'use client';

import { useEffect } from 'react';
import { NotFound } from '@/components/not-found';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Global error:', error);
  }, [error]);

  return (
    <NotFound
      title="Something went wrong"
      message="An unexpected error occurred. Please try again later."
      showHomeButton={true}
      showBackButton={true}
      showRefreshButton={true}
    />
  );
}
