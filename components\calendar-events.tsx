'use client';

import { format } from 'date-fns';

interface CalendarEvent {
  id: string;
  summary: string;
  description?: string;
  start?: {
    dateTime?: string;
    date?: string;
    timeZone?: string;
  };
  end?: {
    dateTime?: string;
    date?: string;
    timeZone?: string;
  };
  location?: string;
  organizer?: {
    email?: string;
    displayName?: string;
  };
  attendees?: Array<{
    email: string;
    displayName?: string;
    responseStatus?: string;
  }>;
  htmlLink?: string;
}

interface CalendarEventsProps {
  calendarData?: {
    items?: CalendarEvent[];
    summary?: string;
    description?: string;
    timeZone?: string;
    [key: string]: any;
  };
}

export function CalendarEvents({ calendarData }: CalendarEventsProps) {
  if (!calendarData) {
    return (
      <div className="flex flex-col gap-2 rounded-lg border p-4 max-w-[500px] bg-background">
        <div className="h-5 w-3/4 animate-pulse rounded-md bg-muted"></div>
        <div className="mt-2 h-4 w-full animate-pulse rounded-md bg-muted"></div>
        <div className="mt-1 h-4 w-1/2 animate-pulse rounded-md bg-muted"></div>
      </div>
    );
  }

  // Handle case where there are no events
  if (!calendarData.items || calendarData.items.length === 0) {
    return (
      <div className="flex flex-col gap-2 rounded-lg border p-4 max-w-[500px] bg-background">
        <div className="text-lg font-medium">No events found</div>
        <div className="text-sm text-muted-foreground">Your calendar is clear for the requested time period.</div>
      </div>
    );
  }

  // Format and display events
  return (
    <div className="flex flex-col gap-4 rounded-lg border p-4 max-w-[500px] bg-background">
      <div className="text-lg font-medium">Calendar Events</div>
      
      <div className="flex flex-col gap-3">
        {calendarData.items.map((event) => (
          <div key={event.id} className="flex flex-col gap-1 border-l-2 border-blue-500 pl-3 py-1">
            <div className="font-medium">{event.summary || 'Untitled Event'}</div>
            
            {/* Time information */}
            <div className="text-sm text-muted-foreground">
              {formatEventTime(event)}
            </div>
            
            {/* Location if available */}
            {event.location && (
              <div className="text-sm">
                <span className="text-muted-foreground">Location: </span>
                {event.location}
              </div>
            )}
            
            {/* Description if available - truncated */}
            {event.description && (
              <div className="text-sm text-muted-foreground mt-1 line-clamp-2">
                {event.description}
              </div>
            )}
            
            {/* Attendees if available */}
            {event.attendees && event.attendees.length > 0 && (
              <div className="text-xs text-muted-foreground mt-1">
                <span>{event.attendees.length} attendee{event.attendees.length !== 1 ? 's' : ''}</span>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

// Helper function to format event time in a readable way
function formatEventTime(event: CalendarEvent): string {
  // Handle all-day events
  if (event.start?.date && event.end?.date) {
    const startDate = new Date(event.start.date);
    const endDate = new Date(event.end.date);
    
    // Adjust end date (Google Calendar API returns the day after for all-day events)
    endDate.setDate(endDate.getDate() - 1);
    
    // Check if it's a single day event
    if (startDate.toDateString() === endDate.toDateString()) {
      return `All day on ${format(startDate, 'EEEE, MMMM d, yyyy')}`;
    } else {
      return `All day from ${format(startDate, 'MMM d, yyyy')} to ${format(endDate, 'MMM d, yyyy')}`;
    }
  }
  
  // Handle timed events
  if (event.start?.dateTime && event.end?.dateTime) {
    const startDateTime = new Date(event.start.dateTime);
    const endDateTime = new Date(event.end.dateTime);
    
    // Check if it's a single day event
    if (startDateTime.toDateString() === endDateTime.toDateString()) {
      return `${format(startDateTime, 'EEEE, MMMM d, yyyy')} from ${format(startDateTime, 'h:mm a')} to ${format(endDateTime, 'h:mm a')}`;
    } else {
      return `From ${format(startDateTime, 'MMM d, h:mm a')} to ${format(endDateTime, 'MMM d, h:mm a, yyyy')}`;
    }
  }
  
  // Fallback for incomplete data
  return 'Time information unavailable';
}
