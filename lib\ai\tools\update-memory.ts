import { tool } from 'ai';
import { z } from 'zod';
import { Session } from 'next-auth';
import { getRelevantMemories } from '@/lib/memory/queries';
import { generateEmbedding } from '@/lib/memory/embeddings';
import { db, withRetry } from '@/lib/db/client';
import { memory } from '@/lib/db/schema';
import { eq, sql } from 'drizzle-orm';

/**
 * AI agent tool for intelligently updating existing memories
 * Enables the AI to maintain accurate, up-to-date memory information
 */
export const createUpdateMemoryTool = ({ session }: { session: Session | null }) => {
  return tool({
    name: 'updateMemory',
    description: 'Update an existing memory when you discover new information that contradicts, enhances, or replaces previous knowledge. Use this to maintain accurate, current information rather than creating duplicates.',
    parameters: z.object({
      query: z.string().describe('Search query to find the memory to update (describe what you\'re looking for)'),
      newContent: z.string().describe('The updated memory content that replaces or enhances the existing information'),
      reason: z.string().describe('Why this update is needed (e.g., "User corrected previous information", "New details discovered")'),
      category: z.string().optional().describe('Updated category if different from original'),
    }),
    execute: async ({ query, newContent, reason, category }) => {
      if (!session?.user?.id) {
        return { error: 'User not authenticated', success: false };
      }

      try {
        console.log(`🔄 AI updating memory: searching for "${query}"`);

        // Find the most relevant existing memory
        const existingMemories = await getRelevantMemories({
          userId: session.user.id,
          query,
          limit: 3,
          similarityThreshold: 0.4 // Higher threshold for updates
        });

        if (!existingMemories || existingMemories.length === 0) {
          return {
            success: false,
            error: 'No relevant memory found to update',
            suggestion: 'Consider creating a new memory instead',
            isExecuting: false
          };
        }

        // Get the most similar memory
        const targetMemory = existingMemories[0];
        console.log(`🎯 Found target memory: "${targetMemory.content}" (similarity: ${targetMemory.similarity})`);

        // Generate new embedding for updated content
        const newEmbedding = await generateEmbedding(newContent);

        // Update the memory with enhanced content and reason
        const enhancedContent = `${newContent} [Updated: ${reason}]`;
        
        const result = await withRetry(async () => {
          return await db.execute(sql`
            UPDATE "Memory"
            SET 
              "content" = ${enhancedContent},
              "embedding" = ${JSON.stringify(newEmbedding)}::vector,
              "category" = ${category || targetMemory.category || null}
            WHERE id = ${targetMemory.id}
            RETURNING *
          `);
        });

        if (!result || result.length === 0) {
          return {
            success: false,
            error: 'Failed to update memory',
            isExecuting: false
          };
        }

        console.log(`✅ Successfully updated memory: "${enhancedContent}"`);

        return {
          success: true,
          message: `Updated memory: ${newContent}`,
          previousContent: targetMemory.content,
          newContent: enhancedContent,
          reason,
          memoryId: targetMemory.id,
          isExecuting: false
        };

      } catch (error) {
        console.error('Failed to update memory:', error);
        return {
          error: 'Failed to update memory',
          success: false,
          message: error instanceof Error ? error.message : 'Unknown error',
          isExecuting: false
        };
      }
    },
  });
};
