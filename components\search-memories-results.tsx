'use client';

import { useState } from 'react';
import { SearchIcon } from 'lucide-react';
import { ToolUsageCard } from './ui/tool-usage-card';

interface SearchMemoriesResultsProps {
  searchData: {
    success: boolean;
    message?: string;
    memories?: Array<{
      id: string;
      content: string;
      category?: string;
      similarity: number;
      createdAt: string;
    }>;
    intent?: string;
    query?: string;
    analysis?: string;
    recommendation?: string;
    highestSimilarity?: number;
    error?: string;
    isExecuting?: boolean;
  };
}

export function SearchMemoriesResults({ searchData }: SearchMemoriesResultsProps) {
  const [showDetails, setShowDetails] = useState(false);

  if (searchData.isExecuting) {
    return (
      <ToolUsageCard
        icon={SearchIcon}
        toolName="Search Memories"
        status="loading"
        isExecuting={true}
      />
    );
  }

  if (!searchData.success) {
    return (
      <ToolUsageCard
        icon={SearchIcon}
        toolName="Search Memories"
        status="error"
        query={searchData.error || 'Failed to search memories'}
        isExecuting={false}
      />
    );
  }

  // Create truncated preview
  const truncatedQuery = searchData.query 
    ? searchData.query.split(' ').slice(0, 4).join(' ') + (searchData.query.split(' ').length > 4 ? '...' : '')
    : '';

  return (
    <ToolUsageCard
      icon={SearchIcon}
      toolName="Search Memories"
      status="success"
      query={showDetails ? `"${searchData.query}"` : `"${truncatedQuery}`}
      showContent={showDetails}
      onToggleContent={() => setShowDetails(!showDetails)}
      isExecuting={false}
    >
      {showDetails && (
        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span className="text-primary font-medium">
              Found {searchData.memories?.length || 0} memories
            </span>
            {searchData.intent && (
              <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                {searchData.intent.replace('_', ' ')}
              </span>
            )}
          </div>

          {searchData.analysis && (
            <div className="text-sm">
              <span className="font-medium">Analysis:</span> {searchData.analysis}
            </div>
          )}

          {searchData.recommendation && (
            <div className="text-sm text-primary">
              <span className="font-medium">Recommendation:</span> {searchData.recommendation}
            </div>
          )}

          {searchData.memories && searchData.memories.length > 0 && (
            <div className="space-y-2">
              <div className="text-xs font-medium text-muted-foreground">Relevant Memories:</div>
              {searchData.memories.slice(0, 3).map((memory, index) => (
                <div key={memory.id} className="border rounded-md p-2 bg-muted/20">
                  <div className="text-sm">{memory.content}</div>
                  <div className="flex items-center justify-between mt-1">
                    <span className="text-xs text-muted-foreground">
                      {memory.category && `[${memory.category}]`}
                    </span>
                    <span className="text-xs text-primary">
                      {Math.round(memory.similarity * 100)}% match
                    </span>
                  </div>
                </div>
              ))}
              {searchData.memories.length > 3 && (
                <div className="text-xs text-muted-foreground">
                  ... and {searchData.memories.length - 3} more
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </ToolUsageCard>
  );
}
