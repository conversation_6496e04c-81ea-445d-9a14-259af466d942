import { useEffect, useRef, useState, type RefObject } from 'react';

export function useScrollToBottom<T extends HTMLElement>(): [
  RefObject<T>,
  RefObject<T>,
  () => void,
] {
  const containerRef = useRef<T>(null);
  const endRef = useRef<T>(null);
  const [userHasScrolled, setUserHasScrolled] = useState(false);
  const isNearBottom = useRef(true);
  const lastScrollHeight = useRef(0);
  const isAutoScrolling = useRef(false);
  const lastUserInteraction = useRef(Date.now());

  // Function to check if user is near bottom
  const checkIfNearBottom = () => {
    const container = containerRef.current;
    if (!container) return true;

    const threshold = 100; // Reduced threshold to be less aggressive
    const position = container.scrollHeight - container.scrollTop - container.clientHeight;
    return position < threshold;
  };

  // Function to scroll to bottom
  const scrollToBottomInternal = (behavior: ScrollBehavior = 'instant') => {
    if (endRef.current) {
      isAutoScrolling.current = true;
      endRef.current.scrollIntoView({ behavior, block: 'end' });

      // Reset the auto-scrolling flag after animation completes
      setTimeout(() => {
        isAutoScrolling.current = false;
      }, behavior === 'smooth' ? 300 : 0);
    }
  };

  useEffect(() => {
    const container = containerRef.current;
    const end = endRef.current;

    if (!container || !end) return;

    // Initialize lastScrollHeight
    lastScrollHeight.current = container.scrollHeight;

    // Handle scroll events to detect when user manually scrolls
    const handleScroll = () => {
      // Skip if this scroll event was triggered by our auto-scrolling
      if (isAutoScrolling.current) return;

      // Update the last user interaction time
      lastUserInteraction.current = Date.now();

      const nearBottom = checkIfNearBottom();
      isNearBottom.current = nearBottom;

      // Only set userHasScrolled to true if we're not near bottom
      // This prevents setting it when auto-scrolling happens
      if (!nearBottom) {
        setUserHasScrolled(true);
      } else if (nearBottom && userHasScrolled) {
        // If user scrolls back to bottom, reset userHasScrolled
        setUserHasScrolled(false);
      }
    };

    // Auto-scroll if user hasn't manually scrolled up or is already near bottom
    const scrollToBottomIfNeeded = () => {
      const currentScrollHeight = container.scrollHeight;
      const hasNewContent = currentScrollHeight > lastScrollHeight.current;

      // Update the last scroll height
      lastScrollHeight.current = currentScrollHeight;

      // Only auto-scroll if:
      // 1. There's new content AND
      // 2. User is already at the bottom OR
      // 3. It's been more than 2 seconds since the last user scroll interaction
      const timeSinceLastInteraction = Date.now() - lastUserInteraction.current;
      const shouldAutoScroll =
        hasNewContent &&
        (isNearBottom.current ||
         (timeSinceLastInteraction > 2000 && !userHasScrolled));

      if (shouldAutoScroll) {
        scrollToBottomInternal('instant');
      }
    };

    // Create a mutation observer that only triggers on content changes, not attribute changes
    const observer = new MutationObserver((mutations) => {
      // Check if any mutations are content-related (not just attribute changes)
      const hasContentChanges = mutations.some(mutation =>
        mutation.type === 'childList' ||
        mutation.type === 'characterData'
      );

      if (hasContentChanges) {
        scrollToBottomIfNeeded();
      }
    });

    // Only observe content changes, not attribute changes
    observer.observe(container, {
      childList: true,
      subtree: true,
      characterData: true,
      // Removed attributes: true to prevent hover effects from triggering scrolling
    });

    // Add scroll event listener
    container.addEventListener('scroll', handleScroll);

    return () => {
      observer.disconnect();
      container.removeEventListener('scroll', handleScroll);
    };
  }, [userHasScrolled]);

  // Function to manually scroll to bottom and reset userHasScrolled state
  const scrollToBottom = () => {
    if (endRef.current) {
      // Only force scroll to bottom if user hasn't scrolled up recently
      const timeSinceLastInteraction = Date.now() - lastUserInteraction.current;
      if (!userHasScrolled || timeSinceLastInteraction > 1000) {
        scrollToBottomInternal('smooth');
        setUserHasScrolled(false);
      }
    }
  };

  return [containerRef, endRef, scrollToBottom];
}
