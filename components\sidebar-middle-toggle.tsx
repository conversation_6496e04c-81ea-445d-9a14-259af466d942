import React from 'react';
import { useSidebar } from '@/components/ui/sidebar';
import { ChevronLeftIcon, ChevronRightIcon } from './icons';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';

export function SidebarMiddleToggle() {
  const { toggleSidebar, state, isMobile } = useSidebar();

  // Don't render on mobile
  if (isMobile) {
    return null;
  }

  return (
    <div
      className="fixed top-1/2 -translate-y-1/2 z-50 transition-all duration-300 ease-in-out hidden md:block"
      style={{
        left: state === 'collapsed' ? '0' : '16rem',
      }}
    >
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            onClick={toggleSidebar}
            className={cn(
              "flex items-center justify-center w-6 h-24 bg-background border border-border rounded-r-md shadow-md hover:bg-primary hover:text-white hover:border-primary transition-all duration-200",
              "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50",
            )}
            aria-label="Toggle Sidebar"
          >
            {state === 'collapsed' ? (
              <ChevronRightIcon size={16} />
            ) : (
              <ChevronLeftIcon size={16} />
            )}
          </button>
        </TooltipTrigger>
        <TooltipContent side="right">
          {state === 'collapsed' ? 'Expand Sidebar' : 'Collapse Sidebar'}
        </TooltipContent>
      </Tooltip>
    </div>
  );
}
