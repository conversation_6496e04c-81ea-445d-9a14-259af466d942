import { NextResponse } from 'next/server';
import { OpenAIToolSet } from "composio-core";
import { auth } from '@/app/(auth)/auth';
import { getConnectionsByUserId, updateConnectionStatus, deleteConnection } from '@/lib/db/queries';

// Get all connections for the authenticated user
export async function GET() {
  try {
    const session = await auth();

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Query connections for the user
    const userId = session.user.id as string;
    const userConnections = await getConnectionsByUserId(userId);

    return NextResponse.json({
      success: true,
      connections: userConnections
    });
  } catch (error) {
    console.error('Error fetching connections:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'An error occurred while fetching connections'
      },
      { status: 500 }
    );
  }
}

// Delete a connection
export async function DELETE(request: Request) {
  try {
    const session = await auth();

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { connectionId } = await request.json();

    if (!connectionId) {
      return NextResponse.json(
        { error: 'Connection ID is required' },
        { status: 400 }
      );
    }

    const userId = session.user.id as string;

    // First verify the connection belongs to the user
    const userConnections = await getConnectionsByUserId(userId);
    const userConnection = userConnections.find(conn => conn.id === connectionId);

    if (!userConnection) {
      return NextResponse.json(
        { error: 'Connection not found or not authorized' },
        { status: 404 }
      );
    }

    // Initialize the Composio toolset
    const toolset = new OpenAIToolSet();

    try {
      // Delete the connection from Composio
      // We need to use the connectionId from Composio, not our database ID
      const composioConnectionId = userConnection.connectionId;

      console.log(`Deleting connection from Composio: ${composioConnectionId}`);

      // Use the Composio API to delete the connection
      await toolset.client.connectedAccounts.delete({
        connectedAccountId: composioConnectionId
      });

      console.log('Successfully deleted connection from Composio');
    } catch (composioError) {
      console.error('Error deleting connection from Composio:', composioError);
      // Continue with database deletion even if Composio deletion fails
      // This ensures we don't leave orphaned records in our database
    }

    // Delete the connection from our database
    console.log(`Deleting connection from database: ${connectionId}`);
    await deleteConnection(connectionId);
    console.log('Successfully deleted connection from database');

    return NextResponse.json({
      success: true,
      message: 'Connection deleted'
    });
  } catch (error) {
    console.error('Error deleting connection:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Could not disconnect service'
      },
      { status: 500 }
    );
  }
}
