'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { X } from 'lucide-react';
import { useTheme } from 'next-themes';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { signOut } from 'next-auth/react';
import { MemoryManagement } from '@/components/memory-management';

interface UserData {
  email: string;
  firstName: string;
  lastName: string;
}

interface Connection {
  id: string;
  provider: string;
  connectionId: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  metadata?: any;
}

interface Provider {
  id: string;
  name: string;
  description: string;
}

export function ProfileSettingsModal({
  trigger,
  defaultOpen = false,
  onOpenChange
}: {
  trigger: React.ReactNode;
  defaultOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
}) {
  const router = useRouter();
  const { setTheme, theme } = useTheme();
  const [isUpdating, setIsUpdating] = useState(false);
  const [userData, setUserData] = useState<UserData>({
    email: '',
    firstName: '',
    lastName: ''
  });
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: ''
  });

  // Settings state
  const [showCode, setShowCode] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(true);

  // Connections state
  const [connections, setConnections] = useState<Connection[]>([]);
  const [connectingProvider, setConnectingProvider] = useState<string | null>(null);
  const [disconnectingProvider, setDisconnectingProvider] = useState<string | null>(null);
  const [checkingStatus, setCheckingStatus] = useState<Record<string, boolean>>({});

  // Available providers
  const availableProviders: Provider[] = [
    { id: 'github', name: 'GitHub', description: 'Connect to GitHub to access repositories, issues, and more.' },
    { id: 'gmail', name: 'Gmail', description: 'Connect to Gmail to access emails and send messages.' },
    { id: 'googlecalendar', name: 'Google Calendar', description: 'Connect to Google Calendar to manage events and schedules.' },
    { id: 'notion', name: 'Notion', description: 'Connect to Notion to access and manage your workspaces.' },
    { id: 'jira', name: 'Jira', description: 'Connect to Jira to manage issues and projects.' }
  ];

  useEffect(() => {
    // Fetch user data from session
    const fetchUserData = async () => {
      setIsLoading(true);
      try {
        const response = await fetch('/api/user');
        if (response.ok) {
          const data = await response.json();
          setUserData({
            email: data.email,
            firstName: data.firstName || '',
            lastName: data.lastName || ''
          });
          setFormData({
            firstName: data.firstName || '',
            lastName: data.lastName || ''
          });
        }
      } catch (error) {
        console.error('Failed to fetch user data', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
    fetchConnections();
  }, []);

  // Fetch user connections
  const fetchConnections = async () => {
    try {
      const response = await fetch('/api/connections');
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.connections) {
          setConnections(data.connections);
        }
      } else {
        toast.error('Failed to fetch connections');
      }
    } catch (error) {
      console.error('Error fetching connections:', error);
      toast.error('An error occurred while fetching connections');
    }
  };

  // Connect to a provider
  const connectProvider = async (provider: string) => {
    setConnectingProvider(provider);
    try {
      const response = await fetch('/api/connections/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ provider }),
      });

      const data = await response.json();

      if (data.success && data.redirectUrl) {
        // Create a message handler to listen for the OAuth callback
        const messageHandler = (event: MessageEvent) => {
          // Check if the message is from our expected origin
          if (event.origin !== window.location.origin) return;

          // Check if the message has the expected format
          if (event.data && event.data.type === 'oauth-callback' && event.data.provider === provider) {
            // Remove the message listener
            window.removeEventListener('message', messageHandler);

            // Check the connection status
            checkConnectionStatus(provider);
          }
        };

        // Add the message listener
        window.addEventListener('message', messageHandler);

        // Open the redirect URL in a new window
        window.open(data.redirectUrl, '_blank', 'width=600,height=700');
      } else if (data.comingSoon) {
        // Handle "coming soon" message
        toast.info(`${provider} integration coming soon`);
        setConnectingProvider(null);
      } else {
        // Handle other errors
        toast.error(`Couldn't connect to ${provider}. Please try again.`);
        setConnectingProvider(null);
      }
    } catch (error) {
      console.error(`Error connecting to ${provider}:`, error);
      toast.error(`Couldn't connect to ${provider}. Please try again.`);
      setConnectingProvider(null);
    }
  };

  // Check connection status
  const checkConnectionStatus = async (provider: string, attempts = 0, maxAttempts = 15) => {
    setCheckingStatus(prev => ({ ...prev, [provider]: true }));

    // If we've exceeded the maximum number of attempts (30 seconds total)
    if (attempts >= maxAttempts) {
      toast.error(`Couldn't connect to ${provider}. Please try again.`);
      setCheckingStatus(prev => ({ ...prev, [provider]: false }));
      setConnectingProvider(null);
      return;
    }

    try {
      const response = await fetch(`/api/connections/status?provider=${provider}`);
      const data = await response.json();

      if (data.success) {
        if (data.connected && data.status === 'ACTIVE') {
          toast.success(`${provider} connected`);
          // Refresh connections list
          fetchConnections();
          setCheckingStatus(prev => ({ ...prev, [provider]: false }));
          setConnectingProvider(null);
        } else {
          // If not connected yet or not active, check again in 2 seconds
          setTimeout(() => checkConnectionStatus(provider, attempts + 1, maxAttempts), 2000);
        }
      } else {
        toast.error(`Couldn't connect to ${provider}. Please try again.`);
        setCheckingStatus(prev => ({ ...prev, [provider]: false }));
        setConnectingProvider(null);
      }
    } catch (error) {
      console.error(`Error checking connection status for ${provider}:`, error);
      toast.error(`Couldn't connect to ${provider}. Please try again.`);
      setCheckingStatus(prev => ({ ...prev, [provider]: false }));
      setConnectingProvider(null);
    }
  };

  // Disconnect a service
  const disconnectService = async (connectionId: string, provider: string) => {
    setDisconnectingProvider(provider);
    try {
      const response = await fetch('/api/connections', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ connectionId }),
      });

      if (response.ok) {
        toast.success(`${provider} disconnected`);
        // Refresh connections list
        fetchConnections();
      } else {
        toast.error(`Failed to disconnect ${provider}`);
      }
    } catch (error) {
      console.error(`Error disconnecting ${provider}:`, error);
      toast.error(`An error occurred while disconnecting ${provider}`);
    } finally {
      setDisconnectingProvider(null);
    }
  };

  // Get connection status for a provider
  const getConnectionForProvider = (provider: string) => {
    return connections.find(conn => conn.provider.toLowerCase() === provider.toLowerCase());
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleUpdateProfile = async () => {
    setIsUpdating(true);
    try {
      const response = await fetch('/api/profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          firstName: formData.firstName,
          lastName: formData.lastName
        })
      });

      if (response.ok) {
        toast.success('Profile updated successfully');
      } else {
        const data = await response.json();
        toast.error(data.message || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('An error occurred while updating your profile');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleLogout = async () => {
    await signOut({ callbackUrl: '/login' });
  };

  const handleDeleteAccount = async () => {
    const confirmed = window.confirm('Are you sure you want to delete your account? This action cannot be undone.');
    if (!confirmed) return;

    try {
      const response = await fetch('/api/user', {
        method: 'DELETE'
      });

      if (response.ok) {
        toast.success('Account deleted successfully');
        router.push('/login');
      } else {
        const data = await response.json();
        toast.error(data.message || 'Failed to delete account');
      }
    } catch (error) {
      console.error('Error deleting account:', error);
      toast.error('An error occurred while deleting your account');
    }
  };

  return (
    <Dialog defaultOpen={defaultOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="sm:max-w-[900px] p-0 gap-0 overflow-hidden">
        <div className="flex flex-col h-[90vh] max-h-[850px]">
          <DialogHeader className="px-6 py-4 border-b">
            <DialogTitle className="text-xl">Profile Settings</DialogTitle>
          </DialogHeader>

          <div className="flex flex-1 overflow-hidden">
            <Tabs defaultValue="personalization" className="flex w-full h-full">
              {/* Sidebar */}
              <div className="w-[220px] border-r bg-muted/30 p-2">
                <TabsList className="flex flex-col h-auto bg-transparent p-0 justify-start items-stretch gap-1">
                  <TabsTrigger
                    value="personalization"
                    className="justify-start px-3 py-2 h-9 data-[state=active]:bg-background data-[state=active]:shadow-none"
                  >
                    <svg className="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                      <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                    Profile
                  </TabsTrigger>
                  <TabsTrigger
                    value="memory"
                    className="justify-start px-3 py-2 h-9 data-[state=active]:bg-background data-[state=active]:shadow-none"
                  >
                    <svg className="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M12 2a8 8 0 0 1 8 8v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V10a8 8 0 0 1 8-8z"></path>
                      <path d="M12 6v4"></path>
                      <path d="M8 10h8"></path>
                    </svg>
                    Memory
                  </TabsTrigger>
                  <TabsTrigger
                    value="connections"
                    className="justify-start px-3 py-2 h-9 data-[state=active]:bg-background data-[state=active]:shadow-none"
                  >
                    <svg className="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
                      <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
                    </svg>
                    Connected Services
                  </TabsTrigger>
                  <TabsTrigger
                    value="security"
                    className="justify-start px-3 py-2 h-9 data-[state=active]:bg-background data-[state=active]:shadow-none"
                  >
                    <svg className="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                      <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                    </svg>
                    Security
                  </TabsTrigger>
                  <TabsTrigger
                    value="preferences"
                    className="justify-start px-3 py-2 h-9 data-[state=active]:bg-background data-[state=active]:shadow-none"
                  >
                    <svg className="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20z"/>
                      <path d="M12 6v4l2 2"/>
                    </svg>
                    Preferences
                  </TabsTrigger>
                </TabsList>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-auto p-6">
                <TabsContent value="preferences" className="mt-0">
                  <div className="space-y-6">
                    <div className="flex flex-col items-center justify-center gap-2 text-center mb-4">
                      <h3 className="text-lg font-medium">Preferences</h3>
                      <p className="text-sm text-gray-500 dark:text-zinc-400">
                        Customize your application settings
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-medium">Theme</h3>
                      <div className="flex items-center justify-between mt-2">
                        <span>{theme === 'system' ? 'System' : theme === 'dark' ? 'Dark' : 'Light'}</span>
                        <div className="flex gap-2">
                          <Button
                            variant={theme === 'light' ? "default" : "outline"}
                            size="sm"
                            onClick={() => setTheme('light')}
                            className="px-3"
                          >
                            <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364-.7071-.7071M6.34315 6.34315l-.70711-.70711m12.72796.00005-.7071.70711M6.3432 17.6569l-.70711.7071M16 12c0 2.2091-1.7909 4-4 4-2.20914 0-4-1.7909-4-4 0-2.20914 1.79086-4 4-4 2.2091 0 4 1.79086 4 4z"/>
                            </svg>
                          </Button>
                          <Button
                            variant={theme === 'dark' ? "default" : "outline"}
                            size="sm"
                            onClick={() => setTheme('dark')}
                            className="px-3"
                          >
                            <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                            </svg>
                          </Button>
                          <Button
                            variant={theme === 'system' ? "default" : "outline"}
                            size="sm"
                            onClick={() => setTheme('system')}
                            className="px-3"
                          >
                            <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                              <line x1="8" y1="21" x2="16" y2="21"></line>
                              <line x1="12" y1="17" x2="12" y2="21"></line>
                            </svg>
                          </Button>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-lg font-medium">Language</h3>
                      <div className="flex items-center justify-between mt-2">
                        <span>Auto-detect</span>
                        <Button variant="outline" size="sm">
                          Auto-detect
                          <svg className="ml-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <polyline points="6 9 12 15 18 9"/>
                          </svg>
                        </Button>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-lg font-medium">Archived chats</h3>
                      <div className="flex items-center justify-between mt-2">
                        <span>Manage your archived chats</span>
                        <Button variant="outline" size="sm">
                          Manage
                        </Button>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-lg font-medium">Archive all chats</h3>
                      <div className="flex items-center justify-between mt-2">
                        <span>Archive all your current chats</span>
                        <Button variant="outline" size="sm">
                          Archive all
                        </Button>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-lg font-medium">Delete all chats</h3>
                      <div className="flex items-center justify-between mt-2">
                        <span>Permanently delete all your chats</span>
                        <Button variant="destructive" size="sm">
                          Delete all
                        </Button>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="personalization" className="mt-0">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium">Profile Information</h3>
                      <div className="flex flex-col items-center justify-center mt-4 mb-6">
                        <div className="relative size-24 mb-4">
                          <Image
                            src={`https://avatar.vercel.sh/${userData.email}`}
                            alt="User Avatar"
                            fill
                            className="rounded-full"
                          />
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Avatar is automatically generated based on your email
                        </p>
                      </div>

                      <div className="grid grid-cols-2 gap-4 mt-4">
                        <div className="flex flex-col gap-2">
                          <Label
                            htmlFor="firstName"
                            className="text-zinc-600 font-normal dark:text-zinc-400"
                          >
                            First Name
                          </Label>
                          <Input
                            id="firstName"
                            name="firstName"
                            type="text"
                            placeholder="John"
                            value={formData.firstName}
                            onChange={handleChange}
                            className="bg-muted text-md md:text-sm"
                          />
                        </div>
                        <div className="flex flex-col gap-2">
                          <Label
                            htmlFor="lastName"
                            className="text-zinc-600 font-normal dark:text-zinc-400"
                          >
                            Last Name
                          </Label>
                          <Input
                            id="lastName"
                            name="lastName"
                            type="text"
                            placeholder="Doe"
                            value={formData.lastName}
                            onChange={handleChange}
                            className="bg-muted text-md md:text-sm"
                          />
                        </div>
                      </div>

                      <div className="flex flex-col gap-2 mt-4">
                        <Label
                          htmlFor="email"
                          className="text-zinc-600 font-normal dark:text-zinc-400"
                        >
                          Email Address
                        </Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={userData.email}
                          readOnly
                          disabled
                          className="bg-muted text-md md:text-sm opacity-70 cursor-not-allowed"
                        />
                        <p className="text-xs text-gray-500 dark:text-zinc-400">
                          Email cannot be changed
                        </p>
                      </div>

                      <Button
                        onClick={handleUpdateProfile}
                        className="w-full mt-4"
                        disabled={isUpdating}
                      >
                        {isUpdating ? (
                          <>
                            <div className="h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent mr-2" />
                            Updating...
                          </>
                        ) : (
                          'Update Profile'
                        )}
                      </Button>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-lg font-medium">Display Settings</h3>
                      <div className="flex items-center justify-between mt-4">
                        <div className="space-y-1">
                          <p className="text-zinc-600 dark:text-zinc-400">Always show code when using data analyst</p>
                        </div>
                        <Switch
                          checked={showCode}
                          onCheckedChange={setShowCode}
                        />
                      </div>

                      <div className="flex items-center justify-between mt-4">
                        <div className="space-y-1">
                          <p className="text-zinc-600 dark:text-zinc-400">Show follow up suggestions in chats</p>
                        </div>
                        <Switch
                          checked={showSuggestions}
                          onCheckedChange={setShowSuggestions}
                        />
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="connections" className="mt-0">
                  <div className="space-y-6">
                    <div className="flex flex-col items-center justify-center gap-2 text-center mb-4">
                      <h3 className="text-lg font-medium">Connect Your Services</h3>
                      <p className="text-sm text-gray-500 dark:text-zinc-400">
                        Connect your accounts to enable AI tools and automation
                      </p>
                    </div>

                    <div className="space-y-4">
                      {isLoading ? (
                        <div className="flex justify-center items-center py-8">
                          <div className="size-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                        </div>
                      ) : (
                        availableProviders.map((provider) => {
                          const connection = getConnectionForProvider(provider.id);
                          const isConnected = !!connection && connection.status === 'ACTIVE';
                          const isChecking = checkingStatus[provider.id];

                          return (
                            <div key={provider.id} className="flex flex-col sm:flex-row sm:items-center justify-between p-4 border rounded-md gap-4">
                              <div>
                                <h4 className="font-medium">{provider.name}</h4>
                                <p className="text-sm text-gray-500">{provider.description}</p>
                                {isConnected && (
                                  <p className="text-xs text-green-500 mt-1">
                                    Connected
                                  </p>
                                )}
                              </div>
                              <Button
                                variant={isConnected ? "outline" : "default"}
                                size="sm"
                                disabled={connectingProvider === provider.id || disconnectingProvider === provider.id || isChecking}
                                onClick={() => isConnected
                                  ? disconnectService(connection.id, provider.id)
                                  : connectProvider(provider.id)
                                }
                                className="whitespace-nowrap"
                              >
                                {connectingProvider === provider.id || isChecking ? (
                                  <>
                                    <div className="size-4 animate-spin rounded-full border-2 border-background border-t-transparent mr-2" />
                                    {isChecking ? 'Checking...' : 'Connecting...'}
                                  </>
                                ) : disconnectingProvider === provider.id ? (
                                  <>
                                    <div className="size-4 animate-spin rounded-full border-2 border-background border-t-transparent mr-2" />
                                    Disconnecting...
                                  </>
                                ) : isConnected ? (
                                  'Disconnect'
                                ) : (
                                  `Connect ${provider.name}`
                                )}
                              </Button>
                            </div>
                          );
                        })
                      )}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="memory" className="mt-0">
                  <MemoryManagement />
                </TabsContent>

                <TabsContent value="security" className="mt-0">
                  <div className="space-y-6">
                    <div className="flex flex-col items-center justify-center gap-2 text-center mb-4">
                      <h3 className="text-lg font-medium">Account Security</h3>
                      <p className="text-sm text-gray-500 dark:text-zinc-400">
                        Manage your account security settings
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-medium">Password Management</h3>
                      <div className="mt-4">
                        <Button
                          onClick={() => router.push('/reset-password-request')}
                          variant="outline"
                          className="w-full"
                        >
                          Reset Password
                        </Button>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-lg font-medium">Account Management</h3>
                      <div className="space-y-4 mt-4">
                        <Button
                          onClick={handleLogout}
                          variant="outline"
                          className="w-full"
                        >
                          Logout
                        </Button>

                        <Button
                          onClick={handleDeleteAccount}
                          variant="destructive"
                          className="w-full"
                        >
                          Delete Account
                        </Button>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
