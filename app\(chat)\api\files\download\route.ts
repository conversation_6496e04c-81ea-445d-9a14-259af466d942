import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { createClient } from '@supabase/supabase-js';

export async function GET(request: Request) {
  const session = await auth();

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const path = searchParams.get('path');
  const bucket = searchParams.get('bucket') || 'files';

  if (!path) {
    return NextResponse.json({ error: 'Path is required' }, { status: 400 });
  }

  // Get user ID from session
  const userId = session.user?.id;
  if (!userId) {
    return NextResponse.json({ error: 'User ID not found' }, { status: 401 });
  }

  // Security check: Ensure the file belongs to the current user
  // The path should start with the user's ID
  if (!path.startsWith(`${userId}/`)) {
    return NextResponse.json({ error: 'Unauthorized: Cannot access files that do not belong to you' }, { status: 403 });
  }

  try {
    // Check if service role key is available
    if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('SUPABASE_SERVICE_ROLE_KEY is not defined in environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create client with service role key to bypass RLS
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    // Get the file from Supabase Storage
    const { data, error } = await supabase.storage
      .from(bucket)
      .download(path);

    if (error) {
      console.error('Error downloading file from Supabase:', error);
      return NextResponse.json({ error: 'File not found' }, { status: 404 });
    }

    if (!data) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 });
    }

    // Create a response with the file data
    const response = new Response(data);

    // Set appropriate headers
    response.headers.set('Content-Type', data.type);
    response.headers.set('Content-Disposition', `attachment; filename="${path.split('/').pop()}"`);

    return response;
  } catch (error) {
    console.error('Error in file download API:', error);
    return NextResponse.json(
      { error: 'Failed to download file' },
      { status: 500 },
    );
  }
}
