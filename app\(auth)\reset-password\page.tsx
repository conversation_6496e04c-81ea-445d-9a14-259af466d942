'use client';

import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState, useEffect, Suspense } from 'react';
import { toast } from '@/components/toast';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { resetPassword } from '../actions';

// Loading component for Suspense
function ResetPasswordLoading() {
  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-12">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h3 className="text-xl font-semibold dark:text-zinc-50">Loading...</h3>
          <div className="h-4 w-3/4 animate-pulse rounded-md bg-muted"></div>
        </div>
        <div className="px-4 sm:px-16 space-y-4">
          <div className="h-10 w-full animate-pulse rounded-md bg-muted"></div>
          <div className="h-10 w-full animate-pulse rounded-md bg-muted"></div>
          <div className="h-10 w-full animate-pulse rounded-md bg-muted"></div>
        </div>
      </div>
    </div>
  );
}

// Component that uses useSearchParams
function ResetPasswordContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');

  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [tokenError, setTokenError] = useState<string | null>(null);

  useEffect(() => {
    if (!token) {
      setTokenError('No reset token found. Please request a new reset link.');
    }
  }, [token]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!token) {
      toast({ type: 'error', description: 'No reset token found. Please request a new reset link.' });
      return;
    }

    if (password.length < 6) {
      toast({ type: 'error', description: 'Password must be at least 6 characters long' });
      return;
    }

    if (password !== confirmPassword) {
      toast({ type: 'error', description: 'Passwords do not match' });
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await resetPassword(token, password);

      if (result.status === 'success') {
        setIsSuccessful(true);
        toast({
          type: 'success',
          description: 'Password reset successful! You can now log in with your new password.'
        });

        // Redirect to login page after 3 seconds
        setTimeout(() => {
          router.push('/login');
        }, 3000);
      } else if (result.status === 'token_not_found') {
        setTokenError('Invalid or expired reset token. Please request a new reset link.');
        toast({
          type: 'error',
          description: 'Invalid or expired reset token. Please request a new reset link.'
        });
      } else if (result.status === 'token_expired') {
        setTokenError('Reset token has expired. Please request a new reset link.');
        toast({
          type: 'error',
          description: 'Reset token has expired. Please request a new reset link.'
        });
      } else {
        toast({
          type: 'error',
          description: 'Failed to reset password. Please try again.'
        });
      }
    } catch (error) {
      console.error('Error resetting password:', error);
      toast({
        type: 'error',
        description: 'An error occurred. Please try again later.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-12">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h3 className="text-xl font-semibold dark:text-zinc-50">Reset Your Password</h3>
          <p className="text-sm text-gray-500 dark:text-zinc-400">
            {tokenError
              ? 'There was a problem with your reset link'
              : isSuccessful
                ? 'Password reset successful!'
                : 'Enter your new password below'}
          </p>
        </div>

        {tokenError ? (
          <div className="flex flex-col gap-4 px-4 sm:px-16">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4 mb-4">
              <p className="text-red-600 dark:text-red-400 text-sm">{tokenError}</p>
            </div>
            <div className="flex flex-col gap-2 mt-4">
              <Link href="/reset-password-request">
                <Button className="w-full">
                  Request a new reset link
                </Button>
              </Link>
              <Link href="/login">
                <Button variant="outline" className="w-full">
                  Back to Login
                </Button>
              </Link>
            </div>
          </div>
        ) : isSuccessful ? (
          <div className="flex flex-col gap-4 px-4 sm:px-16">
            <p className="text-center text-sm text-gray-500 dark:text-zinc-400">
              Your password has been reset successfully. You will be redirected to the login page shortly.
            </p>
            <div className="flex flex-col gap-2 mt-4">
              <Link href="/login">
                <Button className="w-full">
                  Go to Login
                </Button>
              </Link>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="flex flex-col gap-4 px-4 sm:px-16">
            <div className="flex flex-col gap-2">
              <Label
                htmlFor="password"
                className="text-zinc-600 font-normal dark:text-zinc-400"
              >
                New Password
              </Label>
              <Input
                id="password"
                name="password"
                className="bg-muted text-md md:text-sm"
                type="password"
                placeholder="••••••••"
                autoComplete="new-password"
                required
                autoFocus
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>

            <div className="flex flex-col gap-2">
              <Label
                htmlFor="confirmPassword"
                className="text-zinc-600 font-normal dark:text-zinc-400"
              >
                Confirm New Password
              </Label>
              <Input
                id="confirmPassword"
                name="confirmPassword"
                className="bg-muted text-md md:text-sm"
                type="password"
                placeholder="••••••••"
                autoComplete="new-password"
                required
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
            </div>

            <Button
              type="submit"
              className="mt-2"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Resetting...' : 'Reset Password'}
            </Button>

            <p className="text-center text-sm text-gray-600 mt-4 dark:text-zinc-400">
              {'Remember your password? '}
              <Link
                href="/login"
                className="font-semibold text-gray-800 hover:underline dark:text-zinc-200"
              >
                Sign in
              </Link>
            </p>
          </form>
        )}
      </div>
    </div>
  );
}

// Main page component with Suspense
export default function ResetPasswordPage() {
  return (
    <Suspense fallback={<ResetPasswordLoading />}>
      <ResetPasswordContent />
    </Suspense>
  );
}
