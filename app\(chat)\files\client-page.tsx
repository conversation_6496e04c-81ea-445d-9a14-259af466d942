'use client';

import { FileManager } from '@/components/file-manager';
import { FileUpload } from '@/components/file-upload';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useState } from 'react';

export function FilesPageClient() {
  const [activeTab, setActiveTab] = useState('files');

  const handleUploadComplete = () => {
    // Switch to the files tab after upload is complete
    setActiveTab('files');
  };

  return (
    <div className="mx-auto max-w-6xl px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">File Manager</h1>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="files">My Files</TabsTrigger>
          <TabsTrigger value="upload">Upload Files</TabsTrigger>
        </TabsList>
        
        <TabsContent value="files">
          <FileManager />
        </TabsContent>
        
        <TabsContent value="upload">
          <div className="max-w-md mx-auto">
            <FileUpload onUploadComplete={handleUploadComplete} />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
