'use server';

import { NextRequest, NextResponse } from 'next/server';
import postgres from 'postgres';

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const email = url.searchParams.get('email');
    
    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }
    
    console.log(`Checking confirmation status for user: ${email}`);
    
    // Create a direct database connection
    const client = postgres(process.env.POSTGRES_URL!);
    
    // Execute a direct SQL query
    const result = await client`
      SELECT "id", "email", "confirmed" 
      FROM "User" 
      WHERE "email" = ${email}
    `;
    
    console.log('Query result:', result);
    
    // Close the connection
    await client.end();
    
    return NextResponse.json({ success: true, user: result[0] || null });
  } catch (error) {
    console.error('Error checking user confirmation:', error);
    return NextResponse.json({ error: 'Failed to check user confirmation' }, { status: 500 });
  }
}
