// This script sets up the necessary Supabase storage buckets and policies
// Run with: node scripts/setup-supabase-storage.js

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Missing Supabase credentials in .env.local file');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set');
  process.exit(1);
}

// Create Supabase client with service role key for admin privileges
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupStorage() {
  console.log('Setting up Supabase Storage buckets and policies...');

  try {
    // Create the 'files' bucket if it doesn't exist
    const { data: bucketData, error: bucketError } = await supabase.storage.createBucket('files', {
      public: false,
      fileSizeLimit: 5242880, // 5MB in bytes
      allowedMimeTypes: [
        'image/jpeg',
        'image/png',
        'application/pdf',
        'text/plain',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ]
    });

    if (bucketError) {
      if (bucketError.message.includes('already exists')) {
        console.log('✅ Bucket "files" already exists');
      } else {
        throw bucketError;
      }
    } else {
      console.log('✅ Created bucket "files"');
    }

    // Make the bucket public
    console.log('Setting bucket to public...');
    const { error: updateBucketError } = await supabase.storage.updateBucket('files', {
      public: true
    });

    if (updateBucketError) {
      throw updateBucketError;
    } else {
      console.log('✅ Set bucket to public');
    }

    console.log('✅ Supabase Storage setup completed successfully!');
    console.log('\nNOTE: For production, you should set up proper RLS policies in the Supabase dashboard:');
    console.log('1. Go to your Supabase project dashboard');
    console.log('2. Navigate to Storage > Policies');
    console.log('3. Create policies for the "files" bucket to:');
    console.log('   - Allow authenticated users to upload files');
    console.log('   - Allow authenticated users to download their own files');
    console.log('   - Allow authenticated users to delete their own files');
  } catch (error) {
    console.error('❌ Error setting up Supabase Storage:', error.message);
    process.exit(1);
  }
}

setupStorage();
