'use client';

import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { Suspense } from 'react';

// Component that uses useSearchParams
function ConfirmationContent() {
  const searchParams = useSearchParams();
  const email = searchParams.get('email') || '';

  return (
    <div className="flex h-dvh w-screen items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl gap-8 flex flex-col">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h3 className="text-xl font-semibold dark:text-zinc-50">Check Your Email</h3>
          <div className="mt-4 flex flex-col gap-4">
            <p className="text-sm text-gray-500 dark:text-zinc-400">
              We've sent a confirmation link to <strong>{email}</strong>.
              <br /><br />
              Please check your email and click the link to activate your account.
              The link will expire in 24 hours.
            </p>

            <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                <strong>Tip:</strong> If you don't see the email in your inbox, please check your spam or junk folder.
              </p>
            </div>

            <div className="flex flex-col gap-2 mt-4">
              <Link
                href="/login"
                className="w-full rounded-md bg-zinc-900 px-4 py-2 text-sm font-medium text-white hover:bg-zinc-800 dark:bg-zinc-100 dark:text-zinc-900 dark:hover:bg-zinc-200"
              >
                Back to Login
              </Link>
              <button
                onClick={() => window.location.href = '/register'}
                className="w-full rounded-md bg-transparent px-4 py-2 text-sm font-medium text-zinc-900 hover:bg-zinc-100 dark:text-zinc-100 dark:hover:bg-zinc-800"
              >
                Try Again with Different Email
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Loading fallback for Suspense
function ConfirmationLoading() {
  return (
    <div className="flex h-dvh w-screen items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl gap-8 flex flex-col">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h3 className="text-xl font-semibold dark:text-zinc-50">Loading...</h3>
          <div className="mt-4 flex flex-col gap-4">
            <div className="h-6 w-full animate-pulse rounded-md bg-muted"></div>
            <div className="h-20 w-full animate-pulse rounded-md bg-muted"></div>
            <div className="h-12 w-full animate-pulse rounded-md bg-muted"></div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Main page component with Suspense
export default function ConfirmationPage() {
  return (
    <Suspense fallback={<ConfirmationLoading />}>
      <ConfirmationContent />
    </Suspense>
  );
}
